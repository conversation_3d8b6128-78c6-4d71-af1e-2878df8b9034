{"status": 0, "msg": null, "errService": null, "data": [{"parentId": "root", "id": "13", "name": "投资开发项目", "check": 0, "leaf": false, "order": 0, "data": {"code": "13", "value": "投资开发项目", "parentId": null, "status": 1}, "children": [{"parentId": "13", "id": "1305", "name": "固定资产/无形资产项目", "check": 0, "leaf": true, "order": 0, "data": {"code": "1305", "value": "固定资产/无形资产项目", "parentId": "13", "status": 1}, "children": [], "selectable": true}, {"parentId": "13", "id": "1301", "name": "房地产开发项目", "check": 0, "leaf": false, "order": 0, "data": {"code": "1301", "value": "房地产开发项目", "parentId": "13", "status": 1}, "children": [{"parentId": "1301", "id": "130101", "name": "住宅", "check": 0, "leaf": true, "order": 0, "data": {"code": "130101", "value": "住宅", "parentId": "1301", "status": 1}, "children": [], "selectable": true}, {"parentId": "1301", "id": "130102", "name": "写字楼（办公楼）", "check": 0, "leaf": true, "order": 0, "data": {"code": "130102", "value": "写字楼（办公楼）", "parentId": "1301", "status": 1}, "children": [], "selectable": true}, {"parentId": "1301", "id": "130103", "name": "商业用房", "check": 0, "leaf": true, "order": 0, "data": {"code": "130103", "value": "商业用房", "parentId": "1301", "status": 1}, "children": [], "selectable": true}, {"parentId": "1301", "id": "130104", "name": "城市综合体", "check": 0, "leaf": true, "order": 0, "data": {"code": "130104", "value": "城市综合体", "parentId": "1301", "status": 1}, "children": [], "selectable": true}, {"parentId": "1301", "id": "130199", "name": "其他", "check": 0, "leaf": true, "order": 0, "data": {"code": "130199", "value": "其他", "parentId": "1301", "status": 1}, "children": [], "selectable": true}], "selectable": true}, {"parentId": "13", "id": "1302", "name": "基础设施项目", "check": 0, "leaf": false, "order": 0, "data": {"code": "1302", "value": "基础设施项目", "parentId": "13", "status": 1}, "children": [{"parentId": "1302", "id": "130203", "name": "石油化工工程", "check": 0, "leaf": false, "order": 0, "data": {"code": "130203", "value": "石油化工工程", "parentId": "1302", "status": 1}, "children": [{"parentId": "130203", "id": "13020301", "name": "炼油及石油化工工程", "check": 0, "leaf": true, "order": 0, "data": {"code": "13020301", "value": "炼油及石油化工工程", "parentId": "130203", "status": 1}, "children": [], "selectable": true}, {"parentId": "130203", "id": "13020302", "name": "输油管线及泵房", "check": 0, "leaf": true, "order": 0, "data": {"code": "13020302", "value": "输油管线及泵房", "parentId": "130203", "status": 1}, "children": [], "selectable": true}, {"parentId": "130203", "id": "13020303", "name": "长输燃气管道", "check": 0, "leaf": true, "order": 0, "data": {"code": "13020303", "value": "长输燃气管道", "parentId": "130203", "status": 1}, "children": [], "selectable": true}, {"parentId": "130203", "id": "13020399", "name": "其他石油化工工程", "check": 0, "leaf": true, "order": 0, "data": {"code": "13020399", "value": "其他石油化工工程", "parentId": "130203", "status": 1}, "children": [], "selectable": true}], "selectable": true}, {"parentId": "1302", "id": "130204", "name": "供水及处理工程", "check": 0, "leaf": false, "order": 0, "data": {"code": "130204", "value": "供水及处理工程", "parentId": "1302", "status": 1}, "children": [{"parentId": "130204", "id": "13020402", "name": "水处理工程（含管道及附属设备）", "check": 0, "leaf": true, "order": 0, "data": {"code": "13020402", "value": "水处理工程（含管道及附属设备）", "parentId": "130204", "status": 1}, "children": [], "selectable": true}, {"parentId": "130204", "id": "13020403", "name": "排污、排洪管道工程", "check": 0, "leaf": true, "order": 0, "data": {"code": "13020403", "value": "排污、排洪管道工程", "parentId": "130204", "status": 1}, "children": [], "selectable": true}, {"parentId": "130204", "id": "13020401", "name": "输、供水工程（含管道及附属设备）", "check": 0, "leaf": true, "order": 0, "data": {"code": "13020401", "value": "输、供水工程（含管道及附属设备）", "parentId": "130204", "status": 1}, "children": [], "selectable": true}, {"parentId": "130204", "id": "13020499", "name": "其他供水及处理工程", "check": 0, "leaf": true, "order": 0, "data": {"code": "13020499", "value": "其他供水及处理工程", "parentId": "130204", "status": 1}, "children": [], "selectable": true}], "selectable": true}, {"parentId": "1302", "id": "130202", "name": "能源工程", "check": 0, "leaf": false, "order": 0, "data": {"code": "130202", "value": "能源工程", "parentId": "1302", "status": 1}, "children": [{"parentId": "130202", "id": "13020203", "name": "水电工程", "check": 0, "leaf": true, "order": 0, "data": {"code": "13020203", "value": "水电工程", "parentId": "130202", "status": 1}, "children": [], "selectable": true}, {"parentId": "130202", "id": "13020202", "name": "核电工程", "check": 0, "leaf": true, "order": 0, "data": {"code": "13020202", "value": "核电工程", "parentId": "130202", "status": 1}, "children": [], "selectable": true}, {"parentId": "130202", "id": "13020207", "name": "热力工程", "check": 0, "leaf": true, "order": 0, "data": {"code": "13020207", "value": "热力工程", "parentId": "130202", "status": 1}, "children": [], "selectable": true}, {"parentId": "130202", "id": "13020204", "name": "火电工程", "check": 0, "leaf": true, "order": 0, "data": {"code": "13020204", "value": "火电工程", "parentId": "130202", "status": 1}, "children": [], "selectable": true}, {"parentId": "130202", "id": "13020299", "name": "其他能源工程", "check": 0, "leaf": true, "order": 0, "data": {"code": "13020299", "value": "其他能源工程", "parentId": "130202", "status": 1}, "children": [], "selectable": true}, {"parentId": "130202", "id": "13020209", "name": "太阳能工程", "check": 0, "leaf": true, "order": 0, "data": {"code": "13020209", "value": "太阳能工程", "parentId": "130202", "status": 1}, "children": [], "selectable": true}, {"parentId": "130202", "id": "13020206", "name": "煤炭化工工程", "check": 0, "leaf": true, "order": 0, "data": {"code": "13020206", "value": "煤炭化工工程", "parentId": "130202", "status": 1}, "children": [], "selectable": true}, {"parentId": "130202", "id": "13020201", "name": "变电站及输电线路工程", "check": 0, "leaf": true, "order": 0, "data": {"code": "13020201", "value": "变电站及输电线路工程", "parentId": "130202", "status": 1}, "children": [], "selectable": true}, {"parentId": "130202", "id": "13020205", "name": "风电工程", "check": 0, "leaf": true, "order": 0, "data": {"code": "13020205", "value": "风电工程", "parentId": "130202", "status": 1}, "children": [], "selectable": true}, {"parentId": "130202", "id": "13020208", "name": "燃气供应工程", "check": 0, "leaf": true, "order": 0, "data": {"code": "13020208", "value": "燃气供应工程", "parentId": "130202", "status": 1}, "children": [], "selectable": true}, {"parentId": "130202", "id": "13020210", "name": "矿山工程", "check": 0, "leaf": true, "order": 0, "data": {"code": "13020210", "value": "矿山工程", "parentId": "130202", "status": 1}, "children": [], "selectable": true}], "selectable": true}, {"parentId": "1302", "id": "130206", "name": "邮电通讯工程", "check": 0, "leaf": false, "order": 0, "data": {"code": "130206", "value": "邮电通讯工程", "parentId": "1302", "status": 1}, "children": [{"parentId": "130206", "id": "13020601", "name": "基站", "check": 0, "leaf": true, "order": 0, "data": {"code": "13020601", "value": "基站", "parentId": "130206", "status": 1}, "children": [], "selectable": true}, {"parentId": "130206", "id": "13020603", "name": "通信线路", "check": 0, "leaf": true, "order": 0, "data": {"code": "13020603", "value": "通信线路", "parentId": "130206", "status": 1}, "children": [], "selectable": true}, {"parentId": "130206", "id": "13020699", "name": "其他邮电通讯工程", "check": 0, "leaf": true, "order": 0, "data": {"code": "13020699", "value": "其他邮电通讯工程", "parentId": "130206", "status": 1}, "children": [], "selectable": true}, {"parentId": "130206", "id": "13020602", "name": "发射塔", "check": 0, "leaf": true, "order": 0, "data": {"code": "13020602", "value": "发射塔", "parentId": "130206", "status": 1}, "children": [], "selectable": true}], "selectable": true}, {"parentId": "1302", "id": "130207", "name": "防卫防灾工程", "check": 0, "leaf": false, "order": 0, "data": {"code": "130207", "value": "防卫防灾工程", "parentId": "1302", "status": 1}, "children": [{"parentId": "130207", "id": "13020703", "name": "防空设施", "check": 0, "leaf": true, "order": 0, "data": {"code": "13020703", "value": "防空设施", "parentId": "130207", "status": 1}, "children": [], "selectable": true}, {"parentId": "130207", "id": "13020704", "name": "消防设施", "check": 0, "leaf": true, "order": 0, "data": {"code": "13020704", "value": "消防设施", "parentId": "130207", "status": 1}, "children": [], "selectable": true}, {"parentId": "130207", "id": "13020702", "name": "山洪防御工程", "check": 0, "leaf": true, "order": 0, "data": {"code": "13020702", "value": "山洪防御工程", "parentId": "130207", "status": 1}, "children": [], "selectable": true}, {"parentId": "130207", "id": "13020799", "name": "其他防卫防灾工程", "check": 0, "leaf": true, "order": 0, "data": {"code": "13020799", "value": "其他防卫防灾工程", "parentId": "130207", "status": 1}, "children": [], "selectable": true}, {"parentId": "130207", "id": "13020701", "name": "堤坝工程", "check": 0, "leaf": true, "order": 0, "data": {"code": "13020701", "value": "堤坝工程", "parentId": "130207", "status": 1}, "children": [], "selectable": true}, {"parentId": "130207", "id": "13020705", "name": "排雨工程", "check": 0, "leaf": true, "order": 0, "data": {"code": "13020705", "value": "排雨工程", "parentId": "130207", "status": 1}, "children": [], "selectable": true}], "selectable": true}, {"parentId": "1302", "id": "130208", "name": "水利、水运工程", "check": 0, "leaf": false, "order": 0, "data": {"code": "130208", "value": "水利、水运工程", "parentId": "1302", "status": 1}, "children": [{"parentId": "130208", "id": "13020803", "name": "水利枢纽", "check": 0, "leaf": true, "order": 0, "data": {"code": "13020803", "value": "水利枢纽", "parentId": "130208", "status": 1}, "children": [], "selectable": true}, {"parentId": "130208", "id": "13020804", "name": "灌溉排水", "check": 0, "leaf": true, "order": 0, "data": {"code": "13020804", "value": "灌溉排水", "parentId": "130208", "status": 1}, "children": [], "selectable": true}, {"parentId": "130208", "id": "13020812", "name": "填海造地、人工岛", "check": 0, "leaf": true, "order": 0, "data": {"code": "13020812", "value": "填海造地、人工岛", "parentId": "130208", "status": 1}, "children": [], "selectable": true}, {"parentId": "130208", "id": "13020807", "name": "防波堤与护岸工程", "check": 0, "leaf": true, "order": 0, "data": {"code": "13020807", "value": "防波堤与护岸工程", "parentId": "130208", "status": 1}, "children": [], "selectable": true}, {"parentId": "130208", "id": "13020808", "name": "疏浚与吹填工程", "check": 0, "leaf": true, "order": 0, "data": {"code": "13020808", "value": "疏浚与吹填工程", "parentId": "130208", "status": 1}, "children": [], "selectable": true}, {"parentId": "130208", "id": "13020801", "name": "引水工程", "check": 0, "leaf": true, "order": 0, "data": {"code": "13020801", "value": "引水工程", "parentId": "130208", "status": 1}, "children": [], "selectable": true}, {"parentId": "130208", "id": "13020805", "name": "船闸工程", "check": 0, "leaf": true, "order": 0, "data": {"code": "13020805", "value": "船闸工程", "parentId": "130208", "status": 1}, "children": [], "selectable": true}, {"parentId": "130208", "id": "13020810", "name": "航道与航标工程", "check": 0, "leaf": true, "order": 0, "data": {"code": "13020810", "value": "航道与航标工程", "parentId": "130208", "status": 1}, "children": [], "selectable": true}, {"parentId": "130208", "id": "13020809", "name": "船坞与船台滑道工程", "check": 0, "leaf": true, "order": 0, "data": {"code": "13020809", "value": "船坞与船台滑道工程", "parentId": "130208", "status": 1}, "children": [], "selectable": true}, {"parentId": "130208", "id": "13020811", "name": "道路与堆场工程", "check": 0, "leaf": true, "order": 0, "data": {"code": "13020811", "value": "道路与堆场工程", "parentId": "130208", "status": 1}, "children": [], "selectable": true}, {"parentId": "130208", "id": "13020899", "name": "其他水利、水运工程", "check": 0, "leaf": true, "order": 0, "data": {"code": "13020899", "value": "其他水利、水运工程", "parentId": "130208", "status": 1}, "children": [], "selectable": true}, {"parentId": "130208", "id": "13020802", "name": "水库", "check": 0, "leaf": true, "order": 0, "data": {"code": "13020802", "value": "水库", "parentId": "130208", "status": 1}, "children": [], "selectable": true}, {"parentId": "130208", "id": "13020806", "name": "码头与岸壁工程", "check": 0, "leaf": true, "order": 0, "data": {"code": "13020806", "value": "码头与岸壁工程", "parentId": "130208", "status": 1}, "children": [], "selectable": true}], "selectable": true}, {"parentId": "1302", "id": "130201", "name": "交通运输工程", "check": 0, "leaf": false, "order": 0, "data": {"code": "130201", "value": "交通运输工程", "parentId": "1302", "status": 1}, "children": [{"parentId": "130201", "id": "13020101", "name": "公路", "check": 0, "leaf": true, "order": 0, "data": {"code": "13020101", "value": "公路", "parentId": "130201", "status": 1}, "children": [], "selectable": true}, {"parentId": "130201", "id": "13020103", "name": "铁路", "check": 0, "leaf": true, "order": 0, "data": {"code": "13020103", "value": "铁路", "parentId": "130201", "status": 1}, "children": [], "selectable": true}, {"parentId": "130201", "id": "13020105", "name": "机场", "check": 0, "leaf": true, "order": 0, "data": {"code": "13020105", "value": "机场", "parentId": "130201", "status": 1}, "children": [], "selectable": true}, {"parentId": "130201", "id": "13020199", "name": "其他交通运输工程", "check": 0, "leaf": true, "order": 0, "data": {"code": "13020199", "value": "其他交通运输工程", "parentId": "130201", "status": 1}, "children": [], "selectable": true}, {"parentId": "130201", "id": "13020109", "name": "桥梁", "check": 0, "leaf": true, "order": 0, "data": {"code": "13020109", "value": "桥梁", "parentId": "130201", "status": 1}, "children": [], "selectable": true}, {"parentId": "130201", "id": "13020102", "name": "市政道路", "check": 0, "leaf": true, "order": 0, "data": {"code": "13020102", "value": "市政道路", "parentId": "130201", "status": 1}, "children": [], "selectable": true}, {"parentId": "130201", "id": "13020104", "name": "城市轨道交通", "check": 0, "leaf": true, "order": 0, "data": {"code": "13020104", "value": "城市轨道交通", "parentId": "130201", "status": 1}, "children": [], "selectable": true}, {"parentId": "130201", "id": "13020107", "name": "停车场", "check": 0, "leaf": true, "order": 0, "data": {"code": "13020107", "value": "停车场", "parentId": "130201", "status": 1}, "children": [], "selectable": true}, {"parentId": "130201", "id": "13020108", "name": "隧道", "check": 0, "leaf": true, "order": 0, "data": {"code": "13020108", "value": "隧道", "parentId": "130201", "status": 1}, "children": [], "selectable": true}], "selectable": true}, {"parentId": "1302", "id": "130299", "name": "其他工程", "check": 0, "leaf": false, "order": 0, "data": {"code": "130299", "value": "其他工程", "parentId": "1302", "status": 1}, "children": [{"parentId": "130299", "id": "13029902", "name": "室外娱乐设施", "check": 0, "leaf": true, "order": 0, "data": {"code": "13029902", "value": "室外娱乐设施", "parentId": "130299", "status": 1}, "children": [], "selectable": true}, {"parentId": "130299", "id": "13029904", "name": "地下综合管廊、管网", "check": 0, "leaf": true, "order": 0, "data": {"code": "13029904", "value": "地下综合管廊、管网", "parentId": "130299", "status": 1}, "children": [], "selectable": true}, {"parentId": "130299", "id": "13029901", "name": "室外体育场", "check": 0, "leaf": true, "order": 0, "data": {"code": "13029901", "value": "室外体育场", "parentId": "130299", "status": 1}, "children": [], "selectable": true}, {"parentId": "130299", "id": "13029905", "name": "海绵城市", "check": 0, "leaf": true, "order": 0, "data": {"code": "13029905", "value": "海绵城市", "parentId": "130299", "status": 1}, "children": [], "selectable": true}, {"parentId": "130299", "id": "13029999", "name": "其他工程", "check": 0, "leaf": true, "order": 0, "data": {"code": "13029999", "value": "其他工程", "parentId": "130299", "status": 1}, "children": [], "selectable": true}], "selectable": true}, {"parentId": "1302", "id": "130205", "name": "环保工程", "check": 0, "leaf": false, "order": 0, "data": {"code": "130205", "value": "环保工程", "parentId": "1302", "status": 1}, "children": [{"parentId": "130205", "id": "13020501", "name": "民用垃圾处理", "check": 0, "leaf": true, "order": 0, "data": {"code": "13020501", "value": "民用垃圾处理", "parentId": "130205", "status": 1}, "children": [], "selectable": true}, {"parentId": "130205", "id": "13020502", "name": "工业废物处理", "check": 0, "leaf": true, "order": 0, "data": {"code": "13020502", "value": "工业废物处理", "parentId": "130205", "status": 1}, "children": [], "selectable": true}, {"parentId": "130205", "id": "13020503", "name": "建筑业垃圾处理", "check": 0, "leaf": true, "order": 0, "data": {"code": "13020503", "value": "建筑业垃圾处理", "parentId": "130205", "status": 1}, "children": [], "selectable": true}, {"parentId": "130205", "id": "13020506", "name": "防磁、防光、防辐射、防噪音", "check": 0, "leaf": true, "order": 0, "data": {"code": "13020506", "value": "防磁、防光、防辐射、防噪音", "parentId": "130205", "status": 1}, "children": [], "selectable": true}, {"parentId": "130205", "id": "13020505", "name": "景观、绿地与环境再造", "check": 0, "leaf": true, "order": 0, "data": {"code": "13020505", "value": "景观、绿地与环境再造", "parentId": "130205", "status": 1}, "children": [], "selectable": true}, {"parentId": "130205", "id": "13020599", "name": "其他环保工程", "check": 0, "leaf": true, "order": 0, "data": {"code": "13020599", "value": "其他环保工程", "parentId": "130205", "status": 1}, "children": [], "selectable": true}, {"parentId": "130205", "id": "13020504", "name": "污水处理", "check": 0, "leaf": true, "order": 0, "data": {"code": "13020504", "value": "污水处理", "parentId": "130205", "status": 1}, "children": [], "selectable": true}], "selectable": true}], "selectable": true}, {"parentId": "13", "id": "1303", "name": "房建工程项目", "check": 0, "leaf": false, "order": 0, "data": {"code": "1303", "value": "房建工程项目", "parentId": "13", "status": 1}, "children": [{"parentId": "1303", "id": "130304", "name": "商厦", "check": 0, "leaf": true, "order": 0, "data": {"code": "130304", "value": "商厦", "parentId": "1303", "status": 1}, "children": [], "selectable": true}, {"parentId": "1303", "id": "130311", "name": "医疗建筑", "check": 0, "leaf": true, "order": 0, "data": {"code": "130311", "value": "医疗建筑", "parentId": "1303", "status": 1}, "children": [], "selectable": true}, {"parentId": "1303", "id": "130313", "name": "城市综合体", "check": 0, "leaf": true, "order": 0, "data": {"code": "130313", "value": "城市综合体", "parentId": "1303", "status": 1}, "children": [], "selectable": true}, {"parentId": "1303", "id": "130317", "name": "仓储物流", "check": 0, "leaf": true, "order": 0, "data": {"code": "130317", "value": "仓储物流", "parentId": "1303", "status": 1}, "children": [], "selectable": true}, {"parentId": "1303", "id": "130318", "name": "宗教建筑", "check": 0, "leaf": true, "order": 0, "data": {"code": "130318", "value": "宗教建筑", "parentId": "1303", "status": 1}, "children": [], "selectable": true}, {"parentId": "1303", "id": "130312", "name": "酒店度假建筑", "check": 0, "leaf": true, "order": 0, "data": {"code": "130312", "value": "酒店度假建筑", "parentId": "1303", "status": 1}, "children": [], "selectable": true}, {"parentId": "1303", "id": "130315", "name": "工业制造厂房", "check": 0, "leaf": true, "order": 0, "data": {"code": "130315", "value": "工业制造厂房", "parentId": "1303", "status": 1}, "children": [], "selectable": true}, {"parentId": "1303", "id": "130319", "name": "市政配套建筑", "check": 0, "leaf": true, "order": 0, "data": {"code": "130319", "value": "市政配套建筑", "parentId": "1303", "status": 1}, "children": [], "selectable": true}, {"parentId": "1303", "id": "130303", "name": "商用写字楼", "check": 0, "leaf": true, "order": 0, "data": {"code": "130303", "value": "商用写字楼", "parentId": "1303", "status": 1}, "children": [], "selectable": true}, {"parentId": "1303", "id": "130310", "name": "福利设施", "check": 0, "leaf": true, "order": 0, "data": {"code": "130310", "value": "福利设施", "parentId": "1303", "status": 1}, "children": [], "selectable": true}, {"parentId": "1303", "id": "130316", "name": "会议会展中心", "check": 0, "leaf": true, "order": 0, "data": {"code": "130316", "value": "会议会展中心", "parentId": "1303", "status": 1}, "children": [], "selectable": true}, {"parentId": "1303", "id": "130399", "name": "其他", "check": 0, "leaf": true, "order": 0, "data": {"code": "130399", "value": "其他", "parentId": "1303", "status": 1}, "children": [], "selectable": true}, {"parentId": "1303", "id": "130301", "name": "住宅（含别墅、公寓）", "check": 0, "leaf": true, "order": 0, "data": {"code": "130301", "value": "住宅（含别墅、公寓）", "parentId": "1303", "status": 1}, "children": [], "selectable": true}, {"parentId": "1303", "id": "130306", "name": "文化设施", "check": 0, "leaf": true, "order": 0, "data": {"code": "130306", "value": "文化设施", "parentId": "1303", "status": 1}, "children": [], "selectable": true}, {"parentId": "1303", "id": "130302", "name": "保障性住房", "check": 0, "leaf": true, "order": 0, "data": {"code": "130302", "value": "保障性住房", "parentId": "1303", "status": 1}, "children": [], "selectable": true}, {"parentId": "1303", "id": "130305", "name": "政府办公楼", "check": 0, "leaf": true, "order": 0, "data": {"code": "130305", "value": "政府办公楼", "parentId": "1303", "status": 1}, "children": [], "selectable": true}, {"parentId": "1303", "id": "130307", "name": "教育设施", "check": 0, "leaf": true, "order": 0, "data": {"code": "130307", "value": "教育设施", "parentId": "1303", "status": 1}, "children": [], "selectable": true}, {"parentId": "1303", "id": "130308", "name": "体育设施", "check": 0, "leaf": true, "order": 0, "data": {"code": "130308", "value": "体育设施", "parentId": "1303", "status": 1}, "children": [], "selectable": true}, {"parentId": "1303", "id": "130309", "name": "娱乐设施", "check": 0, "leaf": true, "order": 0, "data": {"code": "130309", "value": "娱乐设施", "parentId": "1303", "status": 1}, "children": [], "selectable": true}, {"parentId": "1303", "id": "130314", "name": "工业加工厂房", "check": 0, "leaf": true, "order": 0, "data": {"code": "130314", "value": "工业加工厂房", "parentId": "1303", "status": 1}, "children": [], "selectable": true}], "selectable": true}, {"parentId": "13", "id": "1304", "name": "城镇综合建设项目", "check": 0, "leaf": false, "order": 0, "data": {"code": "1304", "value": "城镇综合建设项目", "parentId": "13", "status": 1}, "children": [{"parentId": "1304", "id": "130499", "name": "其他", "check": 0, "leaf": true, "order": 0, "data": {"code": "130499", "value": "其他", "parentId": "1304", "status": 1}, "children": [], "selectable": true}, {"parentId": "1304", "id": "130403", "name": "特色小镇", "check": 0, "leaf": true, "order": 0, "data": {"code": "130403", "value": "特色小镇", "parentId": "1304", "status": 1}, "children": [], "selectable": true}, {"parentId": "1304", "id": "130402", "name": "棚户区改造", "check": 0, "leaf": true, "order": 0, "data": {"code": "130402", "value": "棚户区改造", "parentId": "1304", "status": 1}, "children": [], "selectable": true}, {"parentId": "1304", "id": "130401", "name": "一级开发", "check": 0, "leaf": true, "order": 0, "data": {"code": "130401", "value": "一级开发", "parentId": "1304", "status": 1}, "children": [], "selectable": true}], "selectable": true}, {"parentId": "13", "id": "1399", "name": "其他项目", "check": 0, "leaf": true, "order": 0, "data": {"code": "1399", "value": "其他项目", "parentId": "13", "status": 1}, "children": [], "selectable": true}, {"parentId": "13", "id": "1306", "name": "长期性股权项目", "check": 0, "leaf": true, "order": 0, "data": {"code": "1306", "value": "长期性股权项目", "parentId": "13", "status": 1}, "children": [], "selectable": true}], "selectable": true}, {"parentId": "root", "id": "15", "name": "制造项目", "check": 0, "leaf": false, "order": 0, "data": {"code": "15", "value": "制造项目", "parentId": null, "status": 1}, "children": [{"parentId": "15", "id": "1501", "name": "商品混凝土项目", "check": 0, "leaf": false, "order": 0, "data": {"code": "1501", "value": "商品混凝土项目", "parentId": "15", "status": 1}, "children": [{"parentId": "1501", "id": "150101", "name": "房屋建设项目", "check": 0, "leaf": true, "order": 0, "data": {"code": "150101", "value": "房屋建设项目", "parentId": "1501", "status": 1}, "children": [], "selectable": true}, {"parentId": "1501", "id": "150102", "name": "基础设施建设项目", "check": 0, "leaf": true, "order": 0, "data": {"code": "150102", "value": "基础设施建设项目", "parentId": "1501", "status": 1}, "children": [], "selectable": true}, {"parentId": "1501", "id": "150199", "name": "其他项目", "check": 0, "leaf": true, "order": 0, "data": {"code": "150199", "value": "其他项目", "parentId": "1501", "status": 1}, "children": [], "selectable": true}], "selectable": true}, {"parentId": "15", "id": "1502", "name": "钢构制造项目", "check": 0, "leaf": true, "order": 0, "data": {"code": "1502", "value": "钢构制造项目", "parentId": "15", "status": 1}, "children": [], "selectable": true}, {"parentId": "15", "id": "1504", "name": "预制构件制造项目", "check": 0, "leaf": true, "order": 0, "data": {"code": "1504", "value": "预制构件制造项目", "parentId": "15", "status": 1}, "children": [], "selectable": true}, {"parentId": "15", "id": "1503", "name": "幕墙制造项目", "check": 0, "leaf": true, "order": 0, "data": {"code": "1503", "value": "幕墙制造项目", "parentId": "15", "status": 1}, "children": [], "selectable": true}, {"parentId": "15", "id": "1599", "name": "其他", "check": 0, "leaf": true, "order": 0, "data": {"code": "1599", "value": "其他", "parentId": "15", "status": 1}, "children": [], "selectable": true}], "selectable": true}, {"parentId": "root", "id": "12", "name": "施工项目", "check": 0, "leaf": false, "order": 0, "data": {"code": "12", "value": "施工项目", "parentId": null, "status": 1}, "children": [{"parentId": "12", "id": "1202", "name": "基础设施建设项目", "check": 0, "leaf": false, "order": 0, "data": {"code": "1202", "value": "基础设施建设项目", "parentId": "12", "status": 1}, "children": [{"parentId": "1202", "id": "120203", "name": "石油化工工程", "check": 0, "leaf": false, "order": 0, "data": {"code": "120203", "value": "石油化工工程", "parentId": "1202", "status": 1}, "children": [{"parentId": "120203", "id": "12020301", "name": "炼油及石油化工工程", "check": 0, "leaf": true, "order": 0, "data": {"code": "12020301", "value": "炼油及石油化工工程", "parentId": "120203", "status": 1}, "children": [], "selectable": true}, {"parentId": "120203", "id": "12020303", "name": "长输燃气管道", "check": 0, "leaf": true, "order": 0, "data": {"code": "12020303", "value": "长输燃气管道", "parentId": "120203", "status": 1}, "children": [], "selectable": true}, {"parentId": "120203", "id": "12020399", "name": "其他石油化工工程", "check": 0, "leaf": false, "order": 0, "data": {"code": "12020399", "value": "其他石油化工工程", "parentId": "120203", "status": 1}, "children": [{"parentId": "12020399", "id": "1202039902", "name": "油库建设类", "check": 0, "leaf": true, "order": 0, "data": {"code": "1202039902", "value": "油库建设类", "parentId": "12020399", "status": 1}, "children": [], "selectable": true}, {"parentId": "12020399", "id": "1202039901", "name": "原油罐制作安装", "check": 0, "leaf": true, "order": 0, "data": {"code": "1202039901", "value": "原油罐制作安装", "parentId": "12020399", "status": 1}, "children": [], "selectable": true}, {"parentId": "12020399", "id": "1202039999", "name": "其他", "check": 0, "leaf": true, "order": 0, "data": {"code": "1202039999", "value": "其他", "parentId": "12020399", "status": 1}, "children": [], "selectable": true}], "selectable": true}, {"parentId": "120203", "id": "12020302", "name": "输油管线及泵房", "check": 0, "leaf": true, "order": 0, "data": {"code": "12020302", "value": "输油管线及泵房", "parentId": "120203", "status": 1}, "children": [], "selectable": true}], "selectable": true}, {"parentId": "1202", "id": "120206", "name": "邮电通讯工程", "check": 0, "leaf": false, "order": 0, "data": {"code": "120206", "value": "邮电通讯工程", "parentId": "1202", "status": 1}, "children": [{"parentId": "120206", "id": "12020601", "name": "基站", "check": 0, "leaf": true, "order": 0, "data": {"code": "12020601", "value": "基站", "parentId": "120206", "status": 1}, "children": [], "selectable": true}, {"parentId": "120206", "id": "12020602", "name": "发射塔", "check": 0, "leaf": true, "order": 0, "data": {"code": "12020602", "value": "发射塔", "parentId": "120206", "status": 1}, "children": [], "selectable": true}, {"parentId": "120206", "id": "12020603", "name": "通信线路", "check": 0, "leaf": true, "order": 0, "data": {"code": "12020603", "value": "通信线路", "parentId": "120206", "status": 1}, "children": [], "selectable": true}, {"parentId": "120206", "id": "12020699", "name": "其他邮电通讯工程", "check": 0, "leaf": true, "order": 0, "data": {"code": "12020699", "value": "其他邮电通讯工程", "parentId": "120206", "status": 1}, "children": [], "selectable": true}], "selectable": true}, {"parentId": "1202", "id": "120299", "name": "其他工程", "check": 0, "leaf": false, "order": 0, "data": {"code": "120299", "value": "其他工程", "parentId": "1202", "status": 1}, "children": [{"parentId": "120299", "id": "12029981", "name": "科教基础设施", "check": 0, "leaf": false, "order": 0, "data": {"code": "12029981", "value": "科教基础设施", "parentId": "120299", "status": 1}, "children": [{"parentId": "12029981", "id": "1202998101", "name": "博物馆", "check": 0, "leaf": true, "order": 0, "data": {"code": "1202998101", "value": "博物馆", "parentId": "12029981", "status": 1}, "children": [], "selectable": true}, {"parentId": "12029981", "id": "1202998103", "name": "文化设施", "check": 0, "leaf": true, "order": 0, "data": {"code": "1202998103", "value": "文化设施", "parentId": "12029981", "status": 1}, "children": [], "selectable": true}, {"parentId": "12029981", "id": "1202998102", "name": "科技中心", "check": 0, "leaf": true, "order": 0, "data": {"code": "1202998102", "value": "科技中心", "parentId": "12029981", "status": 1}, "children": [], "selectable": true}], "selectable": true}, {"parentId": "120299", "id": "12029902", "name": "室外娱乐设施", "check": 0, "leaf": true, "order": 0, "data": {"code": "12029902", "value": "室外娱乐设施", "parentId": "120299", "status": 1}, "children": [], "selectable": true}, {"parentId": "120299", "id": "12029901", "name": "室外体育场", "check": 0, "leaf": true, "order": 0, "data": {"code": "12029901", "value": "室外体育场", "parentId": "120299", "status": 1}, "children": [], "selectable": true}, {"parentId": "120299", "id": "12029904", "name": "地下综合管廊、管网", "check": 0, "leaf": true, "order": 0, "data": {"code": "12029904", "value": "地下综合管廊、管网", "parentId": "120299", "status": 1}, "children": [], "selectable": true}, {"parentId": "120299", "id": "12029905", "name": "海绵城市", "check": 0, "leaf": true, "order": 0, "data": {"code": "12029905", "value": "海绵城市", "parentId": "120299", "status": 1}, "children": [], "selectable": true}, {"parentId": "120299", "id": "12029980", "name": "重大科技基础设施", "check": 0, "leaf": true, "order": 0, "data": {"code": "12029980", "value": "重大科技基础设施", "parentId": "120299", "status": 1}, "children": [], "selectable": true}, {"parentId": "120299", "id": "12029999", "name": "其他工程", "check": 0, "leaf": true, "order": 0, "data": {"code": "12029999", "value": "其他工程", "parentId": "120299", "status": 1}, "children": [], "selectable": true}, {"parentId": "120299", "id": "12029982", "name": "产业技术创新基础设施", "check": 0, "leaf": false, "order": 0, "data": {"code": "12029982", "value": "产业技术创新基础设施", "parentId": "120299", "status": 1}, "children": [{"parentId": "12029982", "id": "1202998202", "name": "超洁净厂房", "check": 0, "leaf": true, "order": 0, "data": {"code": "1202998202", "value": "超洁净厂房", "parentId": "12029982", "status": 1}, "children": [], "selectable": true}, {"parentId": "12029982", "id": "1202998201", "name": "新能源汽车产业基地", "check": 0, "leaf": true, "order": 0, "data": {"code": "1202998201", "value": "新能源汽车产业基地", "parentId": "12029982", "status": 1}, "children": [], "selectable": true}], "selectable": true}], "selectable": true}, {"parentId": "1202", "id": "120202", "name": "能源工程", "check": 0, "leaf": false, "order": 0, "data": {"code": "120202", "value": "能源工程", "parentId": "1202", "status": 1}, "children": [{"parentId": "120202", "id": "12020202", "name": "核电工程", "check": 0, "leaf": true, "order": 0, "data": {"code": "12020202", "value": "核电工程", "parentId": "120202", "status": 1}, "children": [], "selectable": true}, {"parentId": "120202", "id": "12020203", "name": "水电工程", "check": 0, "leaf": true, "order": 0, "data": {"code": "12020203", "value": "水电工程", "parentId": "120202", "status": 1}, "children": [], "selectable": true}, {"parentId": "120202", "id": "12020205", "name": "风电工程", "check": 0, "leaf": true, "order": 0, "data": {"code": "12020205", "value": "风电工程", "parentId": "120202", "status": 1}, "children": [], "selectable": true}, {"parentId": "120202", "id": "12020299", "name": "其他能源工程", "check": 0, "leaf": false, "order": 0, "data": {"code": "12020299", "value": "其他能源工程", "parentId": "120202", "status": 1}, "children": [{"parentId": "12020299", "id": "1202029999", "name": "其他", "check": 0, "leaf": true, "order": 0, "data": {"code": "1202029999", "value": "其他", "parentId": "12020299", "status": 1}, "children": [], "selectable": true}, {"parentId": "12020299", "id": "1202029902", "name": "特高压基础设施", "check": 0, "leaf": true, "order": 0, "data": {"code": "1202029902", "value": "特高压基础设施", "parentId": "12020299", "status": 1}, "children": [], "selectable": true}, {"parentId": "12020299", "id": "1202029901", "name": "垃圾焚烧发电", "check": 0, "leaf": true, "order": 0, "data": {"code": "1202029901", "value": "垃圾焚烧发电", "parentId": "12020299", "status": 1}, "children": [], "selectable": true}], "selectable": true}, {"parentId": "120202", "id": "12020207", "name": "热力工程", "check": 0, "leaf": true, "order": 0, "data": {"code": "12020207", "value": "热力工程", "parentId": "120202", "status": 1}, "children": [], "selectable": true}, {"parentId": "120202", "id": "12020209", "name": "太阳能工程", "check": 0, "leaf": false, "order": 0, "data": {"code": "12020209", "value": "太阳能工程", "parentId": "120202", "status": 1}, "children": [{"parentId": "12020209", "id": "1202020902", "name": "光热发电", "check": 0, "leaf": true, "order": 0, "data": {"code": "1202020902", "value": "光热发电", "parentId": "12020209", "status": 1}, "children": [], "selectable": true}, {"parentId": "12020209", "id": "1202020901", "name": "光伏发电场", "check": 0, "leaf": true, "order": 0, "data": {"code": "1202020901", "value": "光伏发电场", "parentId": "12020209", "status": 1}, "children": [], "selectable": true}, {"parentId": "12020209", "id": "1202020999", "name": "其他", "check": 0, "leaf": true, "order": 0, "data": {"code": "1202020999", "value": "其他", "parentId": "12020209", "status": 1}, "children": [], "selectable": true}], "selectable": true}, {"parentId": "120202", "id": "12020201", "name": "变电站及输电线路工程", "check": 0, "leaf": true, "order": 0, "data": {"code": "12020201", "value": "变电站及输电线路工程", "parentId": "120202", "status": 1}, "children": [], "selectable": true}, {"parentId": "120202", "id": "12020204", "name": "火电工程", "check": 0, "leaf": true, "order": 0, "data": {"code": "12020204", "value": "火电工程", "parentId": "120202", "status": 1}, "children": [], "selectable": true}, {"parentId": "120202", "id": "12020208", "name": "燃气供应工程", "check": 0, "leaf": true, "order": 0, "data": {"code": "12020208", "value": "燃气供应工程", "parentId": "120202", "status": 1}, "children": [], "selectable": true}, {"parentId": "120202", "id": "12020206", "name": "煤炭化工工程", "check": 0, "leaf": true, "order": 0, "data": {"code": "12020206", "value": "煤炭化工工程", "parentId": "120202", "status": 1}, "children": [], "selectable": true}, {"parentId": "120202", "id": "12020210", "name": "矿山工程", "check": 0, "leaf": true, "order": 0, "data": {"code": "12020210", "value": "矿山工程", "parentId": "120202", "status": 1}, "children": [], "selectable": true}], "selectable": true}, {"parentId": "1202", "id": "120205", "name": "环保工程", "check": 0, "leaf": false, "order": 0, "data": {"code": "120205", "value": "环保工程", "parentId": "1202", "status": 1}, "children": [{"parentId": "120205", "id": "12020503", "name": "建筑业垃圾处理", "check": 0, "leaf": true, "order": 0, "data": {"code": "12020503", "value": "建筑业垃圾处理", "parentId": "120205", "status": 1}, "children": [], "selectable": true}, {"parentId": "120205", "id": "12020599", "name": "其他环保工程", "check": 0, "leaf": false, "order": 0, "data": {"code": "12020599", "value": "其他环保工程", "parentId": "120205", "status": 1}, "children": [{"parentId": "12020599", "id": "1202059903", "name": "矿山生态环境恢复", "check": 0, "leaf": true, "order": 0, "data": {"code": "1202059903", "value": "矿山生态环境恢复", "parentId": "12020599", "status": 1}, "children": [], "selectable": true}, {"parentId": "12020599", "id": "1202059904", "name": "河湖与湿地保护恢复", "check": 0, "leaf": true, "order": 0, "data": {"code": "1202059904", "value": "河湖与湿地保护恢复", "parentId": "12020599", "status": 1}, "children": [], "selectable": true}, {"parentId": "12020599", "id": "1202059902", "name": "水系综合治理工程", "check": 0, "leaf": true, "order": 0, "data": {"code": "1202059902", "value": "水系综合治理工程", "parentId": "12020599", "status": 1}, "children": [], "selectable": true}, {"parentId": "12020599", "id": "1202059901", "name": "水环境", "check": 0, "leaf": true, "order": 0, "data": {"code": "1202059901", "value": "水环境", "parentId": "12020599", "status": 1}, "children": [], "selectable": true}, {"parentId": "12020599", "id": "1202059999", "name": "其他", "check": 0, "leaf": true, "order": 0, "data": {"code": "1202059999", "value": "其他", "parentId": "12020599", "status": 1}, "children": [], "selectable": true}], "selectable": true}, {"parentId": "120205", "id": "12020502", "name": "工业废物处理", "check": 0, "leaf": true, "order": 0, "data": {"code": "12020502", "value": "工业废物处理", "parentId": "120205", "status": 1}, "children": [], "selectable": true}, {"parentId": "120205", "id": "12020505", "name": "景观、绿地与环境再造", "check": 0, "leaf": false, "order": 0, "data": {"code": "12020505", "value": "景观、绿地与环境再造", "parentId": "120205", "status": 1}, "children": [{"parentId": "12020505", "id": "1202050502", "name": "城市绿道", "check": 0, "leaf": true, "order": 0, "data": {"code": "1202050502", "value": "城市绿道", "parentId": "12020505", "status": 1}, "children": [], "selectable": true}, {"parentId": "12020505", "id": "1202050599", "name": "其他", "check": 0, "leaf": true, "order": 0, "data": {"code": "1202050599", "value": "其他", "parentId": "12020505", "status": 1}, "children": [], "selectable": true}, {"parentId": "12020505", "id": "1202050501", "name": "公园绿地", "check": 0, "leaf": true, "order": 0, "data": {"code": "1202050501", "value": "公园绿地", "parentId": "12020505", "status": 1}, "children": [], "selectable": true}], "selectable": true}, {"parentId": "120205", "id": "12020506", "name": "防磁、防光、防辐射、防噪音", "check": 0, "leaf": true, "order": 0, "data": {"code": "12020506", "value": "防磁、防光、防辐射、防噪音", "parentId": "120205", "status": 1}, "children": [], "selectable": true}, {"parentId": "120205", "id": "12020501", "name": "民用垃圾处理", "check": 0, "leaf": false, "order": 0, "data": {"code": "12020501", "value": "民用垃圾处理", "parentId": "120205", "status": 1}, "children": [{"parentId": "12020501", "id": "1202050101", "name": "生活垃圾综合利用", "check": 0, "leaf": true, "order": 0, "data": {"code": "1202050101", "value": "生活垃圾综合利用", "parentId": "12020501", "status": 1}, "children": [], "selectable": true}, {"parentId": "12020501", "id": "1202050199", "name": "其他", "check": 0, "leaf": true, "order": 0, "data": {"code": "1202050199", "value": "其他", "parentId": "12020501", "status": 1}, "children": [], "selectable": true}, {"parentId": "12020501", "id": "1202050102", "name": "污泥协同", "check": 0, "leaf": true, "order": 0, "data": {"code": "1202050102", "value": "污泥协同", "parentId": "12020501", "status": 1}, "children": [], "selectable": true}], "selectable": true}, {"parentId": "120205", "id": "12020504", "name": "污水处理", "check": 0, "leaf": true, "order": 0, "data": {"code": "12020504", "value": "污水处理", "parentId": "120205", "status": 1}, "children": [], "selectable": true}], "selectable": true}, {"parentId": "1202", "id": "120207", "name": "防卫防灾工程", "check": 0, "leaf": false, "order": 0, "data": {"code": "120207", "value": "防卫防灾工程", "parentId": "1202", "status": 1}, "children": [{"parentId": "120207", "id": "12020702", "name": "山洪防御工程", "check": 0, "leaf": true, "order": 0, "data": {"code": "12020702", "value": "山洪防御工程", "parentId": "120207", "status": 1}, "children": [], "selectable": true}, {"parentId": "120207", "id": "12020701", "name": "堤坝工程", "check": 0, "leaf": true, "order": 0, "data": {"code": "12020701", "value": "堤坝工程", "parentId": "120207", "status": 1}, "children": [], "selectable": true}, {"parentId": "120207", "id": "12020705", "name": "排雨工程", "check": 0, "leaf": true, "order": 0, "data": {"code": "12020705", "value": "排雨工程", "parentId": "120207", "status": 1}, "children": [], "selectable": true}, {"parentId": "120207", "id": "12020799", "name": "其他防卫防灾工程", "check": 0, "leaf": false, "order": 0, "data": {"code": "12020799", "value": "其他防卫防灾工程", "parentId": "120207", "status": 1}, "children": [{"parentId": "12020799", "id": "1202079902", "name": "应急防疫工程", "check": 0, "leaf": true, "order": 0, "data": {"code": "1202079902", "value": "应急防疫工程", "parentId": "12020799", "status": 1}, "children": [], "selectable": true}, {"parentId": "12020799", "id": "1202079903", "name": "疫苗生产线建", "check": 0, "leaf": true, "order": 0, "data": {"code": "1202079903", "value": "疫苗生产线建", "parentId": "12020799", "status": 1}, "children": [], "selectable": true}, {"parentId": "12020799", "id": "1202079901", "name": "拦河闸", "check": 0, "leaf": true, "order": 0, "data": {"code": "1202079901", "value": "拦河闸", "parentId": "12020799", "status": 1}, "children": [], "selectable": true}, {"parentId": "12020799", "id": "1202079999", "name": "其他", "check": 0, "leaf": true, "order": 0, "data": {"code": "1202079999", "value": "其他", "parentId": "12020799", "status": 1}, "children": [], "selectable": true}], "selectable": true}, {"parentId": "120207", "id": "12020704", "name": "消防设施", "check": 0, "leaf": true, "order": 0, "data": {"code": "12020704", "value": "消防设施", "parentId": "120207", "status": 1}, "children": [], "selectable": true}, {"parentId": "120207", "id": "12020703", "name": "防空设施", "check": 0, "leaf": true, "order": 0, "data": {"code": "12020703", "value": "防空设施", "parentId": "120207", "status": 1}, "children": [], "selectable": true}], "selectable": true}, {"parentId": "1202", "id": "120204", "name": "供水及处理工程", "check": 0, "leaf": false, "order": 0, "data": {"code": "120204", "value": "供水及处理工程", "parentId": "1202", "status": 1}, "children": [{"parentId": "120204", "id": "12020401", "name": "输、供水工程（含管道及附属设备）", "check": 0, "leaf": true, "order": 0, "data": {"code": "12020401", "value": "输、供水工程（含管道及附属设备）", "parentId": "120204", "status": 1}, "children": [], "selectable": true}, {"parentId": "120204", "id": "12020402", "name": "水处理工程（含管道及附属设备）", "check": 0, "leaf": true, "order": 0, "data": {"code": "12020402", "value": "水处理工程（含管道及附属设备）", "parentId": "120204", "status": 1}, "children": [], "selectable": true}, {"parentId": "120204", "id": "12020403", "name": "排污、排洪管道工程", "check": 0, "leaf": true, "order": 0, "data": {"code": "12020403", "value": "排污、排洪管道工程", "parentId": "120204", "status": 1}, "children": [], "selectable": true}, {"parentId": "120204", "id": "12020499", "name": "其他供水及处理工程", "check": 0, "leaf": true, "order": 0, "data": {"code": "12020499", "value": "其他供水及处理工程", "parentId": "120204", "status": 1}, "children": [], "selectable": true}], "selectable": true}, {"parentId": "1202", "id": "120201", "name": "交通运输工程", "check": 0, "leaf": false, "order": 0, "data": {"code": "120201", "value": "交通运输工程", "parentId": "1202", "status": 1}, "children": [{"parentId": "120201", "id": "12020103", "name": "铁路", "check": 0, "leaf": false, "order": 0, "data": {"code": "12020103", "value": "铁路", "parentId": "120201", "status": 1}, "children": [{"parentId": "12020103", "id": "1202010304", "name": "轨道交通车站", "check": 0, "leaf": true, "order": 0, "data": {"code": "1202010304", "value": "轨道交通车站", "parentId": "12020103", "status": 1}, "children": [], "selectable": true}, {"parentId": "12020103", "id": "1202010301", "name": "普通铁路", "check": 0, "leaf": true, "order": 0, "data": {"code": "1202010301", "value": "普通铁路", "parentId": "12020103", "status": 1}, "children": [], "selectable": true}, {"parentId": "12020103", "id": "1202010302", "name": "高速铁路", "check": 0, "leaf": true, "order": 0, "data": {"code": "1202010302", "value": "高速铁路", "parentId": "12020103", "status": 1}, "children": [], "selectable": true}, {"parentId": "12020103", "id": "1202010305", "name": "停车场及其附属项目", "check": 0, "leaf": true, "order": 0, "data": {"code": "1202010305", "value": "停车场及其附属项目", "parentId": "12020103", "status": 1}, "children": [], "selectable": true}], "selectable": true}, {"parentId": "120201", "id": "12020199", "name": "其他交通运输工程", "check": 0, "leaf": true, "order": 0, "data": {"code": "12020199", "value": "其他交通运输工程", "parentId": "120201", "status": 1}, "children": [], "selectable": true}, {"parentId": "120201", "id": "12020102", "name": "市政道路", "check": 0, "leaf": true, "order": 0, "data": {"code": "12020102", "value": "市政道路", "parentId": "120201", "status": 1}, "children": [], "selectable": true}, {"parentId": "120201", "id": "12020104", "name": "城市轨道交通", "check": 0, "leaf": false, "order": 0, "data": {"code": "12020104", "value": "城市轨道交通", "parentId": "120201", "status": 1}, "children": [{"parentId": "12020104", "id": "1202010404", "name": "有轨电车轨道", "check": 0, "leaf": true, "order": 0, "data": {"code": "1202010404", "value": "有轨电车轨道", "parentId": "12020104", "status": 1}, "children": [], "selectable": true}, {"parentId": "12020104", "id": "1202010403", "name": "快轨", "check": 0, "leaf": true, "order": 0, "data": {"code": "1202010403", "value": "快轨", "parentId": "12020104", "status": 1}, "children": [], "selectable": true}, {"parentId": "12020104", "id": "1202010401", "name": "地铁", "check": 0, "leaf": true, "order": 0, "data": {"code": "1202010401", "value": "地铁", "parentId": "12020104", "status": 1}, "children": [], "selectable": true}, {"parentId": "12020104", "id": "1202010402", "name": "轻轨", "check": 0, "leaf": true, "order": 0, "data": {"code": "1202010402", "value": "轻轨", "parentId": "12020104", "status": 1}, "children": [], "selectable": true}, {"parentId": "12020104", "id": "1202010405", "name": "城际高速铁路", "check": 0, "leaf": true, "order": 0, "data": {"code": "1202010405", "value": "城际高速铁路", "parentId": "12020104", "status": 1}, "children": [], "selectable": true}, {"parentId": "12020104", "id": "1202010406", "name": "停车场及其附属项目", "check": 0, "leaf": true, "order": 0, "data": {"code": "1202010406", "value": "停车场及其附属项目", "parentId": "12020104", "status": 1}, "children": [], "selectable": true}], "selectable": true}, {"parentId": "120201", "id": "12020108", "name": "隧道", "check": 0, "leaf": true, "order": 0, "data": {"code": "12020108", "value": "隧道", "parentId": "120201", "status": 1}, "children": [], "selectable": true}, {"parentId": "120201", "id": "12020101", "name": "公路", "check": 0, "leaf": true, "order": 0, "data": {"code": "12020101", "value": "公路", "parentId": "120201", "status": 1}, "children": [], "selectable": true}, {"parentId": "120201", "id": "12020105", "name": "机场", "check": 0, "leaf": true, "order": 0, "data": {"code": "12020105", "value": "机场", "parentId": "120201", "status": 1}, "children": [], "selectable": true}, {"parentId": "120201", "id": "12020107", "name": "停车场", "check": 0, "leaf": false, "order": 0, "data": {"code": "12020107", "value": "停车场", "parentId": "120201", "status": 1}, "children": [{"parentId": "12020107", "id": "1202010702", "name": "新能源汽车充电桩", "check": 0, "leaf": true, "order": 0, "data": {"code": "1202010702", "value": "新能源汽车充电桩", "parentId": "12020107", "status": 1}, "children": [], "selectable": true}, {"parentId": "12020107", "id": "1202010701", "name": "智慧停车场", "check": 0, "leaf": true, "order": 0, "data": {"code": "1202010701", "value": "智慧停车场", "parentId": "12020107", "status": 1}, "children": [], "selectable": true}, {"parentId": "12020107", "id": "1202010703", "name": "其他", "check": 0, "leaf": true, "order": 0, "data": {"code": "1202010703", "value": "其他", "parentId": "12020107", "status": 1}, "children": [], "selectable": true}], "selectable": true}, {"parentId": "120201", "id": "12020109", "name": "桥梁", "check": 0, "leaf": true, "order": 0, "data": {"code": "12020109", "value": "桥梁", "parentId": "120201", "status": 1}, "children": [], "selectable": true}], "selectable": true}, {"parentId": "1202", "id": "120208", "name": "水利、水运工程", "check": 0, "leaf": false, "order": 0, "data": {"code": "120208", "value": "水利、水运工程", "parentId": "1202", "status": 1}, "children": [{"parentId": "120208", "id": "12020801", "name": "引水工程", "check": 0, "leaf": true, "order": 0, "data": {"code": "12020801", "value": "引水工程", "parentId": "120208", "status": 1}, "children": [], "selectable": true}, {"parentId": "120208", "id": "12020808", "name": "疏浚与吹填工程", "check": 0, "leaf": true, "order": 0, "data": {"code": "12020808", "value": "疏浚与吹填工程", "parentId": "120208", "status": 1}, "children": [], "selectable": true}, {"parentId": "120208", "id": "12020810", "name": "航道与航标工程", "check": 0, "leaf": true, "order": 0, "data": {"code": "12020810", "value": "航道与航标工程", "parentId": "120208", "status": 1}, "children": [], "selectable": true}, {"parentId": "120208", "id": "12020811", "name": "道路与堆场工程", "check": 0, "leaf": true, "order": 0, "data": {"code": "12020811", "value": "道路与堆场工程", "parentId": "120208", "status": 1}, "children": [], "selectable": true}, {"parentId": "120208", "id": "12020812", "name": "填海造地、人工岛", "check": 0, "leaf": true, "order": 0, "data": {"code": "12020812", "value": "填海造地、人工岛", "parentId": "120208", "status": 1}, "children": [], "selectable": true}, {"parentId": "120208", "id": "12020802", "name": "水库", "check": 0, "leaf": true, "order": 0, "data": {"code": "12020802", "value": "水库", "parentId": "120208", "status": 1}, "children": [], "selectable": true}, {"parentId": "120208", "id": "12020806", "name": "码头与岸壁工程", "check": 0, "leaf": true, "order": 0, "data": {"code": "12020806", "value": "码头与岸壁工程", "parentId": "120208", "status": 1}, "children": [], "selectable": true}, {"parentId": "120208", "id": "12020899", "name": "其他水利、水运工程", "check": 0, "leaf": true, "order": 0, "data": {"code": "12020899", "value": "其他水利、水运工程", "parentId": "120208", "status": 1}, "children": [], "selectable": true}, {"parentId": "120208", "id": "12020803", "name": "水利枢纽", "check": 0, "leaf": true, "order": 0, "data": {"code": "12020803", "value": "水利枢纽", "parentId": "120208", "status": 1}, "children": [], "selectable": true}, {"parentId": "120208", "id": "12020809", "name": "船坞与船台滑道工程", "check": 0, "leaf": true, "order": 0, "data": {"code": "12020809", "value": "船坞与船台滑道工程", "parentId": "120208", "status": 1}, "children": [], "selectable": true}, {"parentId": "120208", "id": "12020804", "name": "灌溉排水", "check": 0, "leaf": true, "order": 0, "data": {"code": "12020804", "value": "灌溉排水", "parentId": "120208", "status": 1}, "children": [], "selectable": true}, {"parentId": "120208", "id": "12020807", "name": "防波堤与护岸工程", "check": 0, "leaf": true, "order": 0, "data": {"code": "12020807", "value": "防波堤与护岸工程", "parentId": "120208", "status": 1}, "children": [], "selectable": true}, {"parentId": "120208", "id": "12020805", "name": "船闸工程", "check": 0, "leaf": true, "order": 0, "data": {"code": "12020805", "value": "船闸工程", "parentId": "120208", "status": 1}, "children": [], "selectable": true}], "selectable": true}], "selectable": true}, {"parentId": "12", "id": "1201", "name": "房屋建设项目", "check": 0, "leaf": false, "order": 0, "data": {"code": "1201", "value": "房屋建设项目", "parentId": "12", "status": 1}, "children": [{"parentId": "1201", "id": "120102", "name": "保障性住房", "check": 0, "leaf": true, "order": 0, "data": {"code": "120102", "value": "保障性住房", "parentId": "1201", "status": 1}, "children": [], "selectable": true}, {"parentId": "1201", "id": "120107", "name": "教育设施", "check": 0, "leaf": true, "order": 0, "data": {"code": "120107", "value": "教育设施", "parentId": "1201", "status": 1}, "children": [], "selectable": true}, {"parentId": "1201", "id": "120115", "name": "工业制造厂房", "check": 0, "leaf": false, "order": 0, "data": {"code": "120115", "value": "工业制造厂房", "parentId": "1201", "status": 1}, "children": [{"parentId": "120115", "id": "12011503", "name": "电器仪器", "check": 0, "leaf": true, "order": 0, "data": {"code": "12011503", "value": "电器仪器", "parentId": "120115", "status": 1}, "children": [], "selectable": true}, {"parentId": "120115", "id": "12011501", "name": "交通运输设备", "check": 0, "leaf": true, "order": 0, "data": {"code": "12011501", "value": "交通运输设备", "parentId": "120115", "status": 1}, "children": [], "selectable": true}, {"parentId": "120115", "id": "12011502", "name": "电子设备", "check": 0, "leaf": true, "order": 0, "data": {"code": "12011502", "value": "电子设备", "parentId": "120115", "status": 1}, "children": [], "selectable": true}, {"parentId": "120115", "id": "12011599", "name": "其他", "check": 0, "leaf": true, "order": 0, "data": {"code": "12011599", "value": "其他", "parentId": "120115", "status": 1}, "children": [], "selectable": true}], "selectable": true}, {"parentId": "1201", "id": "120119", "name": "市政配套建筑", "check": 0, "leaf": true, "order": 0, "data": {"code": "120119", "value": "市政配套建筑", "parentId": "1201", "status": 1}, "children": [], "selectable": true}, {"parentId": "1201", "id": "120104", "name": "商厦", "check": 0, "leaf": true, "order": 0, "data": {"code": "120104", "value": "商厦", "parentId": "1201", "status": 1}, "children": [], "selectable": true}, {"parentId": "1201", "id": "120105", "name": "政府办公楼", "check": 0, "leaf": true, "order": 0, "data": {"code": "120105", "value": "政府办公楼", "parentId": "1201", "status": 1}, "children": [], "selectable": true}, {"parentId": "1201", "id": "120112", "name": "酒店度假建筑", "check": 0, "leaf": true, "order": 0, "data": {"code": "120112", "value": "酒店度假建筑", "parentId": "1201", "status": 1}, "children": [], "selectable": true}, {"parentId": "1201", "id": "120117", "name": "仓储物流", "check": 0, "leaf": true, "order": 0, "data": {"code": "120117", "value": "仓储物流", "parentId": "1201", "status": 1}, "children": [], "selectable": true}, {"parentId": "1201", "id": "120114", "name": "工业加工厂房", "check": 0, "leaf": false, "order": 0, "data": {"code": "120114", "value": "工业加工厂房", "parentId": "1201", "status": 1}, "children": [{"parentId": "120114", "id": "12011401", "name": "钢铁、有色金属", "check": 0, "leaf": true, "order": 0, "data": {"code": "12011401", "value": "钢铁、有色金属", "parentId": "120114", "status": 1}, "children": [], "selectable": true}, {"parentId": "120114", "id": "12011403", "name": "制药", "check": 0, "leaf": true, "order": 0, "data": {"code": "12011403", "value": "制药", "parentId": "120114", "status": 1}, "children": [], "selectable": true}, {"parentId": "120114", "id": "12011404", "name": "食品加工", "check": 0, "leaf": true, "order": 0, "data": {"code": "12011404", "value": "食品加工", "parentId": "120114", "status": 1}, "children": [], "selectable": true}, {"parentId": "120114", "id": "12011407", "name": "家具建材", "check": 0, "leaf": true, "order": 0, "data": {"code": "12011407", "value": "家具建材", "parentId": "120114", "status": 1}, "children": [], "selectable": true}, {"parentId": "120114", "id": "12011499", "name": "其他", "check": 0, "leaf": true, "order": 0, "data": {"code": "12011499", "value": "其他", "parentId": "120114", "status": 1}, "children": [], "selectable": true}, {"parentId": "120114", "id": "12011402", "name": "化工化肥", "check": 0, "leaf": true, "order": 0, "data": {"code": "12011402", "value": "化工化肥", "parentId": "120114", "status": 1}, "children": [], "selectable": true}, {"parentId": "120114", "id": "12011406", "name": "纺织服装", "check": 0, "leaf": true, "order": 0, "data": {"code": "12011406", "value": "纺织服装", "parentId": "120114", "status": 1}, "children": [], "selectable": true}, {"parentId": "120114", "id": "12011405", "name": "造纸", "check": 0, "leaf": true, "order": 0, "data": {"code": "12011405", "value": "造纸", "parentId": "120114", "status": 1}, "children": [], "selectable": true}], "selectable": true}, {"parentId": "1201", "id": "120108", "name": "体育设施", "check": 0, "leaf": true, "order": 0, "data": {"code": "120108", "value": "体育设施", "parentId": "1201", "status": 1}, "children": [], "selectable": true}, {"parentId": "1201", "id": "120109", "name": "娱乐设施", "check": 0, "leaf": true, "order": 0, "data": {"code": "120109", "value": "娱乐设施", "parentId": "1201", "status": 1}, "children": [], "selectable": true}, {"parentId": "1201", "id": "120116", "name": "会议会展中心", "check": 0, "leaf": true, "order": 0, "data": {"code": "120116", "value": "会议会展中心", "parentId": "1201", "status": 1}, "children": [], "selectable": true}, {"parentId": "1201", "id": "120118", "name": "宗教建筑", "check": 0, "leaf": true, "order": 0, "data": {"code": "120118", "value": "宗教建筑", "parentId": "1201", "status": 1}, "children": [], "selectable": true}, {"parentId": "1201", "id": "120110", "name": "福利设施", "check": 0, "leaf": true, "order": 0, "data": {"code": "120110", "value": "福利设施", "parentId": "1201", "status": 1}, "children": [], "selectable": true}, {"parentId": "1201", "id": "120199", "name": "其他", "check": 0, "leaf": false, "order": 0, "data": {"code": "120199", "value": "其他", "parentId": "1201", "status": 1}, "children": [{"parentId": "120199", "id": "12019901", "name": "商业综合", "check": 0, "leaf": true, "order": 0, "data": {"code": "12019901", "value": "商业综合", "parentId": "120199", "status": 1}, "children": [], "selectable": true}, {"parentId": "120199", "id": "12019902", "name": "公共场馆", "check": 0, "leaf": true, "order": 0, "data": {"code": "12019902", "value": "公共场馆", "parentId": "120199", "status": 1}, "children": [], "selectable": true}, {"parentId": "120199", "id": "12019999", "name": "其他办公楼", "check": 0, "leaf": true, "order": 0, "data": {"code": "12019999", "value": "其他办公楼", "parentId": "120199", "status": 1}, "children": [], "selectable": true}], "selectable": true}, {"parentId": "1201", "id": "120101", "name": "住宅（含别墅、公寓）", "check": 0, "leaf": false, "order": 0, "data": {"code": "120101", "value": "住宅（含别墅、公寓）", "parentId": "1201", "status": 1}, "children": [{"parentId": "120101", "id": "12010101", "name": "民用住宅", "check": 0, "leaf": true, "order": 0, "data": {"code": "12010101", "value": "民用住宅", "parentId": "120101", "status": 1}, "children": [], "selectable": true}, {"parentId": "120101", "id": "12010103", "name": "公寓", "check": 0, "leaf": true, "order": 0, "data": {"code": "12010103", "value": "公寓", "parentId": "120101", "status": 1}, "children": [], "selectable": true}, {"parentId": "120101", "id": "12010199", "name": "其他商用住宅", "check": 0, "leaf": true, "order": 0, "data": {"code": "12010199", "value": "其他商用住宅", "parentId": "120101", "status": 1}, "children": [], "selectable": true}, {"parentId": "120101", "id": "12010102", "name": "别墅", "check": 0, "leaf": true, "order": 0, "data": {"code": "12010102", "value": "别墅", "parentId": "120101", "status": 1}, "children": [], "selectable": true}], "selectable": true}, {"parentId": "1201", "id": "120103", "name": "商用写字楼", "check": 0, "leaf": true, "order": 0, "data": {"code": "120103", "value": "商用写字楼", "parentId": "1201", "status": 1}, "children": [], "selectable": true}, {"parentId": "1201", "id": "120106", "name": "文化设施", "check": 0, "leaf": true, "order": 0, "data": {"code": "120106", "value": "文化设施", "parentId": "1201", "status": 1}, "children": [], "selectable": true}, {"parentId": "1201", "id": "120111", "name": "医疗建筑", "check": 0, "leaf": true, "order": 0, "data": {"code": "120111", "value": "医疗建筑", "parentId": "1201", "status": 1}, "children": [], "selectable": true}, {"parentId": "1201", "id": "120113", "name": "城市综合体", "check": 0, "leaf": true, "order": 0, "data": {"code": "120113", "value": "城市综合体", "parentId": "1201", "status": 1}, "children": [], "selectable": true}], "selectable": true}], "selectable": true}, {"parentId": "root", "id": "99", "name": "其他项目", "check": 0, "leaf": true, "order": 0, "data": {"code": "99", "value": "其他项目", "parentId": null, "status": 1}, "children": [], "selectable": true}, {"parentId": "root", "id": "11", "name": "设计勘察与咨询项目", "check": 0, "leaf": false, "order": 0, "data": {"code": "11", "value": "设计勘察与咨询项目", "parentId": null, "status": 1}, "children": [{"parentId": "11", "id": "1103", "name": "规划项目", "check": 0, "leaf": false, "order": 0, "data": {"code": "1103", "value": "规划项目", "parentId": "11", "status": 1}, "children": [{"parentId": "1103", "id": "110302", "name": "城市规划", "check": 0, "leaf": false, "order": 0, "data": {"code": "110302", "value": "城市规划", "parentId": "1103", "status": 1}, "children": [{"parentId": "110302", "id": "11030201", "name": "城市总体规划", "check": 0, "leaf": true, "order": 0, "data": {"code": "11030201", "value": "城市总体规划", "parentId": "110302", "status": 1}, "children": [], "selectable": true}, {"parentId": "110302", "id": "11030202", "name": "城市详细规划", "check": 0, "leaf": true, "order": 0, "data": {"code": "11030202", "value": "城市详细规划", "parentId": "110302", "status": 1}, "children": [], "selectable": true}], "selectable": true}, {"parentId": "1103", "id": "110301", "name": "城镇体系规划", "check": 0, "leaf": true, "order": 0, "data": {"code": "110301", "value": "城镇体系规划", "parentId": "1103", "status": 1}, "children": [], "selectable": true}, {"parentId": "1103", "id": "110304", "name": "乡规划", "check": 0, "leaf": true, "order": 0, "data": {"code": "110304", "value": "乡规划", "parentId": "1103", "status": 1}, "children": [], "selectable": true}, {"parentId": "1103", "id": "110305", "name": "村庄规划", "check": 0, "leaf": true, "order": 0, "data": {"code": "110305", "value": "村庄规划", "parentId": "1103", "status": 1}, "children": [], "selectable": true}, {"parentId": "1103", "id": "110303", "name": "镇规划", "check": 0, "leaf": false, "order": 0, "data": {"code": "110303", "value": "镇规划", "parentId": "1103", "status": 1}, "children": [{"parentId": "110303", "id": "11030301", "name": "镇总体规划", "check": 0, "leaf": true, "order": 0, "data": {"code": "11030301", "value": "镇总体规划", "parentId": "110303", "status": 1}, "children": [], "selectable": true}, {"parentId": "110303", "id": "11030302", "name": "镇详细规划", "check": 0, "leaf": true, "order": 0, "data": {"code": "11030302", "value": "镇详细规划", "parentId": "110303", "status": 1}, "children": [], "selectable": true}], "selectable": true}], "selectable": true}, {"parentId": "11", "id": "1199", "name": "其他", "check": 0, "leaf": false, "order": 0, "data": {"code": "1199", "value": "其他", "parentId": "11", "status": 1}, "children": [{"parentId": "1199", "id": "119999", "name": "其他", "check": 0, "leaf": true, "order": 0, "data": {"code": "119999", "value": "其他", "parentId": "1199", "status": 1}, "children": [], "selectable": true}], "selectable": true}, {"parentId": "11", "id": "1102", "name": "勘察项目", "check": 0, "leaf": false, "order": 0, "data": {"code": "1102", "value": "勘察项目", "parentId": "11", "status": 1}, "children": [{"parentId": "1102", "id": "110201", "name": "工程地球物理勘探", "check": 0, "leaf": true, "order": 0, "data": {"code": "110201", "value": "工程地球物理勘探", "parentId": "1102", "status": 1}, "children": [], "selectable": true}, {"parentId": "1102", "id": "110206", "name": "岩土测试与试验", "check": 0, "leaf": true, "order": 0, "data": {"code": "110206", "value": "岩土测试与试验", "parentId": "1102", "status": 1}, "children": [], "selectable": true}, {"parentId": "1102", "id": "110204", "name": "检测", "check": 0, "leaf": true, "order": 0, "data": {"code": "110204", "value": "检测", "parentId": "1102", "status": 1}, "children": [], "selectable": true}, {"parentId": "1102", "id": "110205", "name": "水文地质勘察", "check": 0, "leaf": true, "order": 0, "data": {"code": "110205", "value": "水文地质勘察", "parentId": "1102", "status": 1}, "children": [], "selectable": true}, {"parentId": "1102", "id": "110203", "name": "工程地质勘察", "check": 0, "leaf": true, "order": 0, "data": {"code": "110203", "value": "工程地质勘察", "parentId": "1102", "status": 1}, "children": [], "selectable": true}, {"parentId": "1102", "id": "110202", "name": "工程地质测绘", "check": 0, "leaf": true, "order": 0, "data": {"code": "110202", "value": "工程地质测绘", "parentId": "1102", "status": 1}, "children": [], "selectable": true}], "selectable": true}, {"parentId": "11", "id": "1105", "name": "监理项目", "check": 0, "leaf": false, "order": 0, "data": {"code": "1105", "value": "监理项目", "parentId": "11", "status": 1}, "children": [{"parentId": "1105", "id": "110501", "name": "工程监理项目", "check": 0, "leaf": true, "order": 0, "data": {"code": "110501", "value": "工程监理项目", "parentId": "1105", "status": 1}, "children": [], "selectable": true}], "selectable": true}, {"parentId": "11", "id": "1101", "name": "设计项目", "check": 0, "leaf": false, "order": 0, "data": {"code": "1101", "value": "设计项目", "parentId": "11", "status": 1}, "children": [{"parentId": "1101", "id": "110102", "name": "市政工程行业项目", "check": 0, "leaf": false, "order": 0, "data": {"code": "110102", "value": "市政工程行业项目", "parentId": "1101", "status": 1}, "children": [{"parentId": "110102", "id": "11010203", "name": "市政燃气设计项目", "check": 0, "leaf": true, "order": 0, "data": {"code": "11010203", "value": "市政燃气设计项目", "parentId": "110102", "status": 1}, "children": [], "selectable": true}, {"parentId": "110102", "id": "11010208", "name": "市政风景园林设计项目", "check": 0, "leaf": true, "order": 0, "data": {"code": "11010208", "value": "市政风景园林设计项目", "parentId": "110102", "status": 1}, "children": [], "selectable": true}, {"parentId": "110102", "id": "11010201", "name": "市政给水设计项目", "check": 0, "leaf": true, "order": 0, "data": {"code": "11010201", "value": "市政给水设计项目", "parentId": "110102", "status": 1}, "children": [], "selectable": true}, {"parentId": "110102", "id": "11010204", "name": "市政热力设计项目", "check": 0, "leaf": true, "order": 0, "data": {"code": "11010204", "value": "市政热力设计项目", "parentId": "110102", "status": 1}, "children": [], "selectable": true}, {"parentId": "110102", "id": "11010205", "name": "市政道路设计项目", "check": 0, "leaf": true, "order": 0, "data": {"code": "11010205", "value": "市政道路设计项目", "parentId": "110102", "status": 1}, "children": [], "selectable": true}, {"parentId": "110102", "id": "11010209", "name": "市政环境卫生设计项目", "check": 0, "leaf": true, "order": 0, "data": {"code": "11010209", "value": "市政环境卫生设计项目", "parentId": "110102", "status": 1}, "children": [], "selectable": true}, {"parentId": "110102", "id": "11010202", "name": "市政排水设计项目", "check": 0, "leaf": true, "order": 0, "data": {"code": "11010202", "value": "市政排水设计项目", "parentId": "110102", "status": 1}, "children": [], "selectable": true}, {"parentId": "110102", "id": "11010206", "name": "市政桥隧设计项目", "check": 0, "leaf": true, "order": 0, "data": {"code": "11010206", "value": "市政桥隧设计项目", "parentId": "110102", "status": 1}, "children": [], "selectable": true}, {"parentId": "110102", "id": "11010207", "name": "市政公共交通设计项目", "check": 0, "leaf": true, "order": 0, "data": {"code": "11010207", "value": "市政公共交通设计项目", "parentId": "110102", "status": 1}, "children": [], "selectable": true}], "selectable": true}, {"parentId": "1101", "id": "110108", "name": "商物粮行业项目", "check": 0, "leaf": false, "order": 0, "data": {"code": "110108", "value": "商物粮行业项目", "parentId": "1101", "status": 1}, "children": [{"parentId": "110108", "id": "11010801", "name": "冷冻冷藏工程设计项目", "check": 0, "leaf": true, "order": 0, "data": {"code": "11010801", "value": "冷冻冷藏工程设计项目", "parentId": "110108", "status": 1}, "children": [], "selectable": true}], "selectable": true}, {"parentId": "1101", "id": "110122", "name": "专项设计项目", "check": 0, "leaf": false, "order": 0, "data": {"code": "110122", "value": "专项设计项目", "parentId": "1101", "status": 1}, "children": [{"parentId": "110122", "id": "11012203", "name": "建筑幕墙设计项目", "check": 0, "leaf": true, "order": 0, "data": {"code": "11012203", "value": "建筑幕墙设计项目", "parentId": "110122", "status": 1}, "children": [], "selectable": true}, {"parentId": "110122", "id": "11012206", "name": "岩土工程设计项目", "check": 0, "leaf": true, "order": 0, "data": {"code": "11012206", "value": "岩土工程设计项目", "parentId": "110122", "status": 1}, "children": [], "selectable": true}, {"parentId": "110122", "id": "11012202", "name": "园林景观设计项目", "check": 0, "leaf": true, "order": 0, "data": {"code": "11012202", "value": "园林景观设计项目", "parentId": "110122", "status": 1}, "children": [], "selectable": true}, {"parentId": "110122", "id": "11012201", "name": "建筑装饰设计项目", "check": 0, "leaf": true, "order": 0, "data": {"code": "11012201", "value": "建筑装饰设计项目", "parentId": "110122", "status": 1}, "children": [], "selectable": true}, {"parentId": "110122", "id": "11012204", "name": "消防设施设计项目", "check": 0, "leaf": true, "order": 0, "data": {"code": "11012204", "value": "消防设施设计项目", "parentId": "110122", "status": 1}, "children": [], "selectable": true}, {"parentId": "110122", "id": "11012205", "name": "建筑智能化系统设计项目", "check": 0, "leaf": true, "order": 0, "data": {"code": "11012205", "value": "建筑智能化系统设计项目", "parentId": "110122", "status": 1}, "children": [], "selectable": true}], "selectable": true}, {"parentId": "1101", "id": "110101", "name": "建筑行业项目", "check": 0, "leaf": false, "order": 0, "data": {"code": "110101", "value": "建筑行业项目", "parentId": "1101", "status": 1}, "children": [{"parentId": "110101", "id": "11010105", "name": "人防工程设计项目", "check": 0, "leaf": true, "order": 0, "data": {"code": "11010105", "value": "人防工程设计项目", "parentId": "110101", "status": 1}, "children": [], "selectable": true}, {"parentId": "110101", "id": "11010103", "name": "工业建筑设计项目", "check": 0, "leaf": true, "order": 0, "data": {"code": "11010103", "value": "工业建筑设计项目", "parentId": "110101", "status": 1}, "children": [], "selectable": true}, {"parentId": "110101", "id": "11010104", "name": "农业建筑设计项目", "check": 0, "leaf": true, "order": 0, "data": {"code": "11010104", "value": "农业建筑设计项目", "parentId": "110101", "status": 1}, "children": [], "selectable": true}, {"parentId": "110101", "id": "11010102", "name": "公共建筑设计项目", "check": 0, "leaf": true, "order": 0, "data": {"code": "11010102", "value": "公共建筑设计项目", "parentId": "110101", "status": 1}, "children": [], "selectable": true}, {"parentId": "110101", "id": "11010101", "name": "居住建筑设计项目", "check": 0, "leaf": true, "order": 0, "data": {"code": "11010101", "value": "居住建筑设计项目", "parentId": "110101", "status": 1}, "children": [], "selectable": true}], "selectable": true}, {"parentId": "1101", "id": "110106", "name": "水利行业项目", "check": 0, "leaf": false, "order": 0, "data": {"code": "110106", "value": "水利行业项目", "parentId": "1101", "status": 1}, "children": [{"parentId": "110106", "id": "11010601", "name": "防洪设计项目", "check": 0, "leaf": true, "order": 0, "data": {"code": "11010601", "value": "防洪设计项目", "parentId": "110106", "status": 1}, "children": [], "selectable": true}], "selectable": true}, {"parentId": "1101", "id": "110103", "name": "公路行业项目", "check": 0, "leaf": true, "order": 0, "data": {"code": "110103", "value": "公路行业项目", "parentId": "1101", "status": 1}, "children": [], "selectable": true}, {"parentId": "1101", "id": "110109", "name": "建材行业项目", "check": 0, "leaf": false, "order": 0, "data": {"code": "110109", "value": "建材行业项目", "parentId": "1101", "status": 1}, "children": [{"parentId": "110109", "id": "11010901", "name": "新型建筑材料设计项目", "check": 0, "leaf": true, "order": 0, "data": {"code": "11010901", "value": "新型建筑材料设计项目", "parentId": "110109", "status": 1}, "children": [], "selectable": true}], "selectable": true}, {"parentId": "1101", "id": "110110", "name": "电力行业项目", "check": 0, "leaf": false, "order": 0, "data": {"code": "110110", "value": "电力行业项目", "parentId": "1101", "status": 1}, "children": [{"parentId": "110110", "id": "11011001", "name": "新能源发电设计项目", "check": 0, "leaf": true, "order": 0, "data": {"code": "11011001", "value": "新能源发电设计项目", "parentId": "110110", "status": 1}, "children": [], "selectable": true}], "selectable": true}, {"parentId": "1101", "id": "110104", "name": "铁路行业项目", "check": 0, "leaf": true, "order": 0, "data": {"code": "110104", "value": "铁路行业项目", "parentId": "1101", "status": 1}, "children": [], "selectable": true}, {"parentId": "1101", "id": "110105", "name": "桥梁行业项目", "check": 0, "leaf": true, "order": 0, "data": {"code": "110105", "value": "桥梁行业项目", "parentId": "1101", "status": 1}, "children": [], "selectable": true}, {"parentId": "1101", "id": "110107", "name": "电子通信光电行业项目", "check": 0, "leaf": false, "order": 0, "data": {"code": "110107", "value": "电子通信光电行业项目", "parentId": "1101", "status": 1}, "children": [{"parentId": "110107", "id": "11010701", "name": "通信铁塔设计项目", "check": 0, "leaf": true, "order": 0, "data": {"code": "11010701", "value": "通信铁塔设计项目", "parentId": "110107", "status": 1}, "children": [], "selectable": true}], "selectable": true}], "selectable": true}, {"parentId": "11", "id": "1104", "name": "咨询项目", "check": 0, "leaf": false, "order": 0, "data": {"code": "1104", "value": "咨询项目", "parentId": "11", "status": 1}, "children": [{"parentId": "1104", "id": "110403", "name": "项目管理咨询", "check": 0, "leaf": true, "order": 0, "data": {"code": "110403", "value": "项目管理咨询", "parentId": "1104", "status": 1}, "children": [], "selectable": true}, {"parentId": "1104", "id": "110401", "name": "造价咨询", "check": 0, "leaf": true, "order": 0, "data": {"code": "110401", "value": "造价咨询", "parentId": "1104", "status": 1}, "children": [], "selectable": true}, {"parentId": "1104", "id": "110402", "name": "全过程工程管理咨询", "check": 0, "leaf": true, "order": 0, "data": {"code": "110402", "value": "全过程工程管理咨询", "parentId": "1104", "status": 1}, "children": [], "selectable": true}], "selectable": true}, {"parentId": "11", "id": "1106", "name": "测量项目", "check": 0, "leaf": false, "order": 0, "data": {"code": "1106", "value": "测量项目", "parentId": "11", "status": 1}, "children": [{"parentId": "1106", "id": "110603", "name": "变形监测", "check": 0, "leaf": true, "order": 0, "data": {"code": "110603", "value": "变形监测", "parentId": "1106", "status": 1}, "children": [], "selectable": true}, {"parentId": "1106", "id": "110601", "name": "地形测量", "check": 0, "leaf": true, "order": 0, "data": {"code": "110601", "value": "地形测量", "parentId": "1106", "status": 1}, "children": [], "selectable": true}, {"parentId": "1106", "id": "110699", "name": "其他测量", "check": 0, "leaf": true, "order": 0, "data": {"code": "110699", "value": "其他测量", "parentId": "1106", "status": 1}, "children": [], "selectable": true}, {"parentId": "1106", "id": "110602", "name": "控制测量", "check": 0, "leaf": true, "order": 0, "data": {"code": "110602", "value": "控制测量", "parentId": "1106", "status": 1}, "children": [], "selectable": true}], "selectable": true}], "selectable": true}, {"parentId": "root", "id": "14", "name": "运营项目", "check": 0, "leaf": false, "order": 0, "data": {"code": "14", "value": "运营项目", "parentId": null, "status": 1}, "children": [{"parentId": "14", "id": "1401", "name": "物业管理项目", "check": 0, "leaf": false, "order": 0, "data": {"code": "1401", "value": "物业管理项目", "parentId": "14", "status": 1}, "children": [{"parentId": "1401", "id": "140104", "name": "商厦", "check": 0, "leaf": true, "order": 0, "data": {"code": "140104", "value": "商厦", "parentId": "1401", "status": 1}, "children": [], "selectable": true}, {"parentId": "1401", "id": "140108", "name": "体育设施", "check": 0, "leaf": true, "order": 0, "data": {"code": "140108", "value": "体育设施", "parentId": "1401", "status": 1}, "children": [], "selectable": true}, {"parentId": "1401", "id": "140113", "name": "城市综合体", "check": 0, "leaf": true, "order": 0, "data": {"code": "140113", "value": "城市综合体", "parentId": "1401", "status": 1}, "children": [], "selectable": true}, {"parentId": "1401", "id": "140116", "name": "会议会展中心", "check": 0, "leaf": true, "order": 0, "data": {"code": "140116", "value": "会议会展中心", "parentId": "1401", "status": 1}, "children": [], "selectable": true}, {"parentId": "1401", "id": "140103", "name": "商用写字楼", "check": 0, "leaf": true, "order": 0, "data": {"code": "140103", "value": "商用写字楼", "parentId": "1401", "status": 1}, "children": [], "selectable": true}, {"parentId": "1401", "id": "140106", "name": "文化设施", "check": 0, "leaf": true, "order": 0, "data": {"code": "140106", "value": "文化设施", "parentId": "1401", "status": 1}, "children": [], "selectable": true}, {"parentId": "1401", "id": "140109", "name": "娱乐设施", "check": 0, "leaf": true, "order": 0, "data": {"code": "140109", "value": "娱乐设施", "parentId": "1401", "status": 1}, "children": [], "selectable": true}, {"parentId": "1401", "id": "140115", "name": "工业制造厂房", "check": 0, "leaf": true, "order": 0, "data": {"code": "140115", "value": "工业制造厂房", "parentId": "1401", "status": 1}, "children": [], "selectable": true}, {"parentId": "1401", "id": "140110", "name": "福利设施", "check": 0, "leaf": true, "order": 0, "data": {"code": "140110", "value": "福利设施", "parentId": "1401", "status": 1}, "children": [], "selectable": true}, {"parentId": "1401", "id": "140111", "name": "医疗建筑", "check": 0, "leaf": true, "order": 0, "data": {"code": "140111", "value": "医疗建筑", "parentId": "1401", "status": 1}, "children": [], "selectable": true}, {"parentId": "1401", "id": "140114", "name": "工业加工厂房", "check": 0, "leaf": true, "order": 0, "data": {"code": "140114", "value": "工业加工厂房", "parentId": "1401", "status": 1}, "children": [], "selectable": true}, {"parentId": "1401", "id": "140119", "name": "市政配套建筑", "check": 0, "leaf": true, "order": 0, "data": {"code": "140119", "value": "市政配套建筑", "parentId": "1401", "status": 1}, "children": [], "selectable": true}, {"parentId": "1401", "id": "140102", "name": "保障性住房", "check": 0, "leaf": true, "order": 0, "data": {"code": "140102", "value": "保障性住房", "parentId": "1401", "status": 1}, "children": [], "selectable": true}, {"parentId": "1401", "id": "140105", "name": "政府办公楼", "check": 0, "leaf": true, "order": 0, "data": {"code": "140105", "value": "政府办公楼", "parentId": "1401", "status": 1}, "children": [], "selectable": true}, {"parentId": "1401", "id": "140118", "name": "宗教建筑", "check": 0, "leaf": true, "order": 0, "data": {"code": "140118", "value": "宗教建筑", "parentId": "1401", "status": 1}, "children": [], "selectable": true}, {"parentId": "1401", "id": "140107", "name": "教育设施", "check": 0, "leaf": true, "order": 0, "data": {"code": "140107", "value": "教育设施", "parentId": "1401", "status": 1}, "children": [], "selectable": true}, {"parentId": "1401", "id": "140101", "name": "住宅（含别墅、公寓）", "check": 0, "leaf": true, "order": 0, "data": {"code": "140101", "value": "住宅（含别墅、公寓）", "parentId": "1401", "status": 1}, "children": [], "selectable": true}, {"parentId": "1401", "id": "140112", "name": "酒店度假建筑", "check": 0, "leaf": true, "order": 0, "data": {"code": "140112", "value": "酒店度假建筑", "parentId": "1401", "status": 1}, "children": [], "selectable": true}, {"parentId": "1401", "id": "140117", "name": "仓储物流", "check": 0, "leaf": true, "order": 0, "data": {"code": "140117", "value": "仓储物流", "parentId": "1401", "status": 1}, "children": [], "selectable": true}, {"parentId": "1401", "id": "140199", "name": "其他", "check": 0, "leaf": true, "order": 0, "data": {"code": "140199", "value": "其他", "parentId": "1401", "status": 1}, "children": [], "selectable": true}], "selectable": true}, {"parentId": "14", "id": "1402", "name": "基础设施运营项目", "check": 0, "leaf": false, "order": 0, "data": {"code": "1402", "value": "基础设施运营项目", "parentId": "14", "status": 1}, "children": [{"parentId": "1402", "id": "140203", "name": "石油化工工程", "check": 0, "leaf": false, "order": 0, "data": {"code": "140203", "value": "石油化工工程", "parentId": "1402", "status": 1}, "children": [{"parentId": "140203", "id": "14020302", "name": "输油管线及泵房", "check": 0, "leaf": true, "order": 0, "data": {"code": "14020302", "value": "输油管线及泵房", "parentId": "140203", "status": 1}, "children": [], "selectable": true}, {"parentId": "140203", "id": "14020303", "name": "长输燃气管道", "check": 0, "leaf": true, "order": 0, "data": {"code": "14020303", "value": "长输燃气管道", "parentId": "140203", "status": 1}, "children": [], "selectable": true}, {"parentId": "140203", "id": "14020399", "name": "其他石油化工工程", "check": 0, "leaf": true, "order": 0, "data": {"code": "14020399", "value": "其他石油化工工程", "parentId": "140203", "status": 1}, "children": [], "selectable": true}, {"parentId": "140203", "id": "14020301", "name": "炼油及石油化工工程", "check": 0, "leaf": true, "order": 0, "data": {"code": "14020301", "value": "炼油及石油化工工程", "parentId": "140203", "status": 1}, "children": [], "selectable": true}], "selectable": true}, {"parentId": "1402", "id": "140207", "name": "防卫防灾工程", "check": 0, "leaf": false, "order": 0, "data": {"code": "140207", "value": "防卫防灾工程", "parentId": "1402", "status": 1}, "children": [{"parentId": "140207", "id": "14020701", "name": "堤坝工程", "check": 0, "leaf": true, "order": 0, "data": {"code": "14020701", "value": "堤坝工程", "parentId": "140207", "status": 1}, "children": [], "selectable": true}, {"parentId": "140207", "id": "14020705", "name": "排雨工程", "check": 0, "leaf": true, "order": 0, "data": {"code": "14020705", "value": "排雨工程", "parentId": "140207", "status": 1}, "children": [], "selectable": true}, {"parentId": "140207", "id": "14020702", "name": "山洪防御工程", "check": 0, "leaf": true, "order": 0, "data": {"code": "14020702", "value": "山洪防御工程", "parentId": "140207", "status": 1}, "children": [], "selectable": true}, {"parentId": "140207", "id": "14020703", "name": "防空设施", "check": 0, "leaf": true, "order": 0, "data": {"code": "14020703", "value": "防空设施", "parentId": "140207", "status": 1}, "children": [], "selectable": true}, {"parentId": "140207", "id": "14020704", "name": "消防设施", "check": 0, "leaf": true, "order": 0, "data": {"code": "14020704", "value": "消防设施", "parentId": "140207", "status": 1}, "children": [], "selectable": true}, {"parentId": "140207", "id": "14020799", "name": "其他防卫防灾工程", "check": 0, "leaf": true, "order": 0, "data": {"code": "14020799", "value": "其他防卫防灾工程", "parentId": "140207", "status": 1}, "children": [], "selectable": true}], "selectable": true}, {"parentId": "1402", "id": "140208", "name": "水利、水运工程", "check": 0, "leaf": false, "order": 0, "data": {"code": "140208", "value": "水利、水运工程", "parentId": "1402", "status": 1}, "children": [{"parentId": "140208", "id": "14020803", "name": "水利枢纽", "check": 0, "leaf": true, "order": 0, "data": {"code": "14020803", "value": "水利枢纽", "parentId": "140208", "status": 1}, "children": [], "selectable": true}, {"parentId": "140208", "id": "14020810", "name": "航道与航标工程", "check": 0, "leaf": true, "order": 0, "data": {"code": "14020810", "value": "航道与航标工程", "parentId": "140208", "status": 1}, "children": [], "selectable": true}, {"parentId": "140208", "id": "14020807", "name": "防波堤与护岸工程", "check": 0, "leaf": true, "order": 0, "data": {"code": "14020807", "value": "防波堤与护岸工程", "parentId": "140208", "status": 1}, "children": [], "selectable": true}, {"parentId": "140208", "id": "14020809", "name": "船坞与船台滑道工程", "check": 0, "leaf": true, "order": 0, "data": {"code": "14020809", "value": "船坞与船台滑道工程", "parentId": "140208", "status": 1}, "children": [], "selectable": true}, {"parentId": "140208", "id": "14020811", "name": "道路与堆场工程", "check": 0, "leaf": true, "order": 0, "data": {"code": "14020811", "value": "道路与堆场工程", "parentId": "140208", "status": 1}, "children": [], "selectable": true}, {"parentId": "140208", "id": "14020804", "name": "灌溉排水", "check": 0, "leaf": true, "order": 0, "data": {"code": "14020804", "value": "灌溉排水", "parentId": "140208", "status": 1}, "children": [], "selectable": true}, {"parentId": "140208", "id": "14020899", "name": "其他水利、水运工程", "check": 0, "leaf": true, "order": 0, "data": {"code": "14020899", "value": "其他水利、水运工程", "parentId": "140208", "status": 1}, "children": [], "selectable": true}, {"parentId": "140208", "id": "14020808", "name": "疏浚与吹填工程", "check": 0, "leaf": true, "order": 0, "data": {"code": "14020808", "value": "疏浚与吹填工程", "parentId": "140208", "status": 1}, "children": [], "selectable": true}, {"parentId": "140208", "id": "14020801", "name": "引水工程", "check": 0, "leaf": true, "order": 0, "data": {"code": "14020801", "value": "引水工程", "parentId": "140208", "status": 1}, "children": [], "selectable": true}, {"parentId": "140208", "id": "14020802", "name": "水库", "check": 0, "leaf": true, "order": 0, "data": {"code": "14020802", "value": "水库", "parentId": "140208", "status": 1}, "children": [], "selectable": true}, {"parentId": "140208", "id": "14020805", "name": "船闸工程", "check": 0, "leaf": true, "order": 0, "data": {"code": "14020805", "value": "船闸工程", "parentId": "140208", "status": 1}, "children": [], "selectable": true}, {"parentId": "140208", "id": "14020806", "name": "码头与岸壁工程", "check": 0, "leaf": true, "order": 0, "data": {"code": "14020806", "value": "码头与岸壁工程", "parentId": "140208", "status": 1}, "children": [], "selectable": true}, {"parentId": "140208", "id": "14020812", "name": "填海造地、人工岛", "check": 0, "leaf": true, "order": 0, "data": {"code": "14020812", "value": "填海造地、人工岛", "parentId": "140208", "status": 1}, "children": [], "selectable": true}], "selectable": true}, {"parentId": "1402", "id": "140201", "name": "交通运输工程", "check": 0, "leaf": false, "order": 0, "data": {"code": "140201", "value": "交通运输工程", "parentId": "1402", "status": 1}, "children": [{"parentId": "140201", "id": "14020101", "name": "公路", "check": 0, "leaf": true, "order": 0, "data": {"code": "14020101", "value": "公路", "parentId": "140201", "status": 1}, "children": [], "selectable": true}, {"parentId": "140201", "id": "14020199", "name": "其他交通运输工程", "check": 0, "leaf": true, "order": 0, "data": {"code": "14020199", "value": "其他交通运输工程", "parentId": "140201", "status": 1}, "children": [], "selectable": true}, {"parentId": "140201", "id": "14020105", "name": "机场", "check": 0, "leaf": true, "order": 0, "data": {"code": "14020105", "value": "机场", "parentId": "140201", "status": 1}, "children": [], "selectable": true}, {"parentId": "140201", "id": "14020107", "name": "停车场", "check": 0, "leaf": true, "order": 0, "data": {"code": "14020107", "value": "停车场", "parentId": "140201", "status": 1}, "children": [], "selectable": true}, {"parentId": "140201", "id": "14020108", "name": "隧道", "check": 0, "leaf": true, "order": 0, "data": {"code": "14020108", "value": "隧道", "parentId": "140201", "status": 1}, "children": [], "selectable": true}, {"parentId": "140201", "id": "14020109", "name": "桥梁", "check": 0, "leaf": true, "order": 0, "data": {"code": "14020109", "value": "桥梁", "parentId": "140201", "status": 1}, "children": [], "selectable": true}, {"parentId": "140201", "id": "14020102", "name": "市政道路", "check": 0, "leaf": true, "order": 0, "data": {"code": "14020102", "value": "市政道路", "parentId": "140201", "status": 1}, "children": [], "selectable": true}, {"parentId": "140201", "id": "14020103", "name": "铁路", "check": 0, "leaf": true, "order": 0, "data": {"code": "14020103", "value": "铁路", "parentId": "140201", "status": 1}, "children": [], "selectable": true}, {"parentId": "140201", "id": "14020104", "name": "城市轨道交通", "check": 0, "leaf": true, "order": 0, "data": {"code": "14020104", "value": "城市轨道交通", "parentId": "140201", "status": 1}, "children": [], "selectable": true}], "selectable": true}, {"parentId": "1402", "id": "140202", "name": "能源工程", "check": 0, "leaf": false, "order": 0, "data": {"code": "140202", "value": "能源工程", "parentId": "1402", "status": 1}, "children": [{"parentId": "140202", "id": "14020206", "name": "煤炭化工工程", "check": 0, "leaf": true, "order": 0, "data": {"code": "14020206", "value": "煤炭化工工程", "parentId": "140202", "status": 1}, "children": [], "selectable": true}, {"parentId": "140202", "id": "14020209", "name": "太阳能工程", "check": 0, "leaf": true, "order": 0, "data": {"code": "14020209", "value": "太阳能工程", "parentId": "140202", "status": 1}, "children": [], "selectable": true}, {"parentId": "140202", "id": "14020202", "name": "核电工程", "check": 0, "leaf": true, "order": 0, "data": {"code": "14020202", "value": "核电工程", "parentId": "140202", "status": 1}, "children": [], "selectable": true}, {"parentId": "140202", "id": "14020203", "name": "水电工程", "check": 0, "leaf": true, "order": 0, "data": {"code": "14020203", "value": "水电工程", "parentId": "140202", "status": 1}, "children": [], "selectable": true}, {"parentId": "140202", "id": "14020207", "name": "热力工程", "check": 0, "leaf": true, "order": 0, "data": {"code": "14020207", "value": "热力工程", "parentId": "140202", "status": 1}, "children": [], "selectable": true}, {"parentId": "140202", "id": "14020299", "name": "其他能源工程", "check": 0, "leaf": true, "order": 0, "data": {"code": "14020299", "value": "其他能源工程", "parentId": "140202", "status": 1}, "children": [], "selectable": true}, {"parentId": "140202", "id": "14020201", "name": "变电站及输电线路工程", "check": 0, "leaf": true, "order": 0, "data": {"code": "14020201", "value": "变电站及输电线路工程", "parentId": "140202", "status": 1}, "children": [], "selectable": true}, {"parentId": "140202", "id": "14020204", "name": "火电工程", "check": 0, "leaf": true, "order": 0, "data": {"code": "14020204", "value": "火电工程", "parentId": "140202", "status": 1}, "children": [], "selectable": true}, {"parentId": "140202", "id": "14020205", "name": "风电工程", "check": 0, "leaf": true, "order": 0, "data": {"code": "14020205", "value": "风电工程", "parentId": "140202", "status": 1}, "children": [], "selectable": true}, {"parentId": "140202", "id": "14020208", "name": "燃气供应工程", "check": 0, "leaf": true, "order": 0, "data": {"code": "14020208", "value": "燃气供应工程", "parentId": "140202", "status": 1}, "children": [], "selectable": true}, {"parentId": "140202", "id": "14020210", "name": "矿山工程", "check": 0, "leaf": true, "order": 0, "data": {"code": "14020210", "value": "矿山工程", "parentId": "140202", "status": 1}, "children": [], "selectable": true}], "selectable": true}, {"parentId": "1402", "id": "140204", "name": "供水及处理工程", "check": 0, "leaf": false, "order": 0, "data": {"code": "140204", "value": "供水及处理工程", "parentId": "1402", "status": 1}, "children": [{"parentId": "140204", "id": "14020402", "name": "水处理工程（含管道及附属设备）", "check": 0, "leaf": true, "order": 0, "data": {"code": "14020402", "value": "水处理工程（含管道及附属设备）", "parentId": "140204", "status": 1}, "children": [], "selectable": true}, {"parentId": "140204", "id": "14020499", "name": "其他供水及处理工程", "check": 0, "leaf": true, "order": 0, "data": {"code": "14020499", "value": "其他供水及处理工程", "parentId": "140204", "status": 1}, "children": [], "selectable": true}, {"parentId": "140204", "id": "14020403", "name": "排污、排洪管道工程", "check": 0, "leaf": true, "order": 0, "data": {"code": "14020403", "value": "排污、排洪管道工程", "parentId": "140204", "status": 1}, "children": [], "selectable": true}, {"parentId": "140204", "id": "14020401", "name": "输、供水工程（含管道及附属设备）", "check": 0, "leaf": true, "order": 0, "data": {"code": "14020401", "value": "输、供水工程（含管道及附属设备）", "parentId": "140204", "status": 1}, "children": [], "selectable": true}], "selectable": true}, {"parentId": "1402", "id": "140206", "name": "邮电通讯工程", "check": 0, "leaf": false, "order": 0, "data": {"code": "140206", "value": "邮电通讯工程", "parentId": "1402", "status": 1}, "children": [{"parentId": "140206", "id": "14020602", "name": "发射塔", "check": 0, "leaf": true, "order": 0, "data": {"code": "14020602", "value": "发射塔", "parentId": "140206", "status": 1}, "children": [], "selectable": true}, {"parentId": "140206", "id": "14020699", "name": "其他邮电通讯工程", "check": 0, "leaf": true, "order": 0, "data": {"code": "14020699", "value": "其他邮电通讯工程", "parentId": "140206", "status": 1}, "children": [], "selectable": true}, {"parentId": "140206", "id": "14020601", "name": "基站", "check": 0, "leaf": true, "order": 0, "data": {"code": "14020601", "value": "基站", "parentId": "140206", "status": 1}, "children": [], "selectable": true}, {"parentId": "140206", "id": "14020603", "name": "通信线路", "check": 0, "leaf": true, "order": 0, "data": {"code": "14020603", "value": "通信线路", "parentId": "140206", "status": 1}, "children": [], "selectable": true}], "selectable": true}, {"parentId": "1402", "id": "140205", "name": "环保工程", "check": 0, "leaf": false, "order": 0, "data": {"code": "140205", "value": "环保工程", "parentId": "1402", "status": 1}, "children": [{"parentId": "140205", "id": "14020503", "name": "建筑业垃圾处理", "check": 0, "leaf": true, "order": 0, "data": {"code": "14020503", "value": "建筑业垃圾处理", "parentId": "140205", "status": 1}, "children": [], "selectable": true}, {"parentId": "140205", "id": "14020506", "name": "防磁、防光、防辐射、防噪音", "check": 0, "leaf": true, "order": 0, "data": {"code": "14020506", "value": "防磁、防光、防辐射、防噪音", "parentId": "140205", "status": 1}, "children": [], "selectable": true}, {"parentId": "140205", "id": "14020501", "name": "民用垃圾处理", "check": 0, "leaf": true, "order": 0, "data": {"code": "14020501", "value": "民用垃圾处理", "parentId": "140205", "status": 1}, "children": [], "selectable": true}, {"parentId": "140205", "id": "14020502", "name": "工业废物处理", "check": 0, "leaf": true, "order": 0, "data": {"code": "14020502", "value": "工业废物处理", "parentId": "140205", "status": 1}, "children": [], "selectable": true}, {"parentId": "140205", "id": "14020599", "name": "其他环保工程", "check": 0, "leaf": true, "order": 0, "data": {"code": "14020599", "value": "其他环保工程", "parentId": "140205", "status": 1}, "children": [], "selectable": true}, {"parentId": "140205", "id": "14020504", "name": "污水处理", "check": 0, "leaf": true, "order": 0, "data": {"code": "14020504", "value": "污水处理", "parentId": "140205", "status": 1}, "children": [], "selectable": true}, {"parentId": "140205", "id": "14020505", "name": "景观、绿地与环境再造", "check": 0, "leaf": true, "order": 0, "data": {"code": "14020505", "value": "景观、绿地与环境再造", "parentId": "140205", "status": 1}, "children": [], "selectable": true}], "selectable": true}, {"parentId": "1402", "id": "140299", "name": "其他工程", "check": 0, "leaf": false, "order": 0, "data": {"code": "140299", "value": "其他工程", "parentId": "1402", "status": 1}, "children": [{"parentId": "140299", "id": "14029901", "name": "室外体育场", "check": 0, "leaf": true, "order": 0, "data": {"code": "14029901", "value": "室外体育场", "parentId": "140299", "status": 1}, "children": [], "selectable": true}, {"parentId": "140299", "id": "14029902", "name": "室外娱乐设施", "check": 0, "leaf": true, "order": 0, "data": {"code": "14029902", "value": "室外娱乐设施", "parentId": "140299", "status": 1}, "children": [], "selectable": true}, {"parentId": "140299", "id": "14029999", "name": "其他工程", "check": 0, "leaf": true, "order": 0, "data": {"code": "14029999", "value": "其他工程", "parentId": "140299", "status": 1}, "children": [], "selectable": true}, {"parentId": "140299", "id": "14029905", "name": "海绵城市", "check": 0, "leaf": true, "order": 0, "data": {"code": "14029905", "value": "海绵城市", "parentId": "140299", "status": 1}, "children": [], "selectable": true}, {"parentId": "140299", "id": "14029904", "name": "地下综合管廊、管网", "check": 0, "leaf": true, "order": 0, "data": {"code": "14029904", "value": "地下综合管廊、管网", "parentId": "140299", "status": 1}, "children": [], "selectable": true}], "selectable": true}], "selectable": true}], "selectable": true}]}