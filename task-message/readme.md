### 实现功能

1. 发送待办与通知
2. 发送催办消息

### 规划功能

1. 查看定时任务状态
2. 动态查询修改待办任务的催办时间
3. 发送流程待办的通知消息

### 接入方法

1. 配置参数
   模块引入了 quartz框架作为催办功能的实现，所以需要配置quartz，

```yml
spring:
  quartz:
    # 持久化到数据库
    job-store-type: jdbc
    # 等待任务完成关闭
    wait-for-jobs-to-complete-on-shutdown: true
    # 启动时更新已存在的job
    overwrite-existing-jobs: false
    jdbc:
      # 初始化表成功后，需要将此项修改为never
      initialize-schema: always
```

> 如表初始化不成功，请使用模块目录下qutartz.sql脚本，执行即可

> <strong>注意 u_toto_task 表需要手动初始化<strong>

另模块内还依赖了g3-org-sdk,还需要配置org-sdk对应的参数,并在其参数下配置 task-config

```yaml
com:
  g3:
    org:
      url: https://k8stest.cscec3b-iti.com/g3-org-web
      clientId: fe9*****59
      clientSecret: NT*****TA
      # 以下为本模块的配置信息
      task-config:
        host: https://cpm-test.cscec3b-iti.com/frontend/#
        msg-config:
          # 中标未立项
          bid-approval-msg-config: C-CONFIG-00000027
          bid-approval-app-link: /project-establishment/notestablishing/detail?id=%s&operation=operation
          # 人资 标准立项完成并更新了dop主数据编码
          hr-notice: C-CONFIG-00000028

```

2. 引入依赖

```xml

<dependency>
    <groupId>com.cscec3b.iti</groupId>
    <artifactId>task-message</artifactId>
    <version>0.0.1-SNAPSHOT</version>
</dependency>

```

3. 调用方法

> 需要在启动类中@MapperScan(basePackages = "com.cscec3b.iti.**.mapper")

```java
 final TaskAndMsgDto taskAndMsgDto = TaskAndMsgDto.builder().appLink(appLinkFormat).webLink(webLinkFormat)
        .configCode(msgConfigCode).payload(JsonUtils.toJsonStr(payload)).targetUsers(targetUsers)
        .taskCode(IdUtil.objectId()).billId(String.valueOf(approvalId)).billType(type).billTypeName(billTypeName)
        .startUserName("项目中心").title(title).bpmInstanceId(approvalId + "-" + currentStepNo)
        .retryTimeCycle(stepMapping.getRemindExpression()).build();
// sendTodoTaskAndMsg(taskAndMsgDto);
```

#### 4.注意事项

1. 如有 druid-spring-boot-starter 依赖，则要求版本号不低于 1.1.18;
2. ymal 配置文件中 mapper-locations: classpath*: 添加通配符扫描
3. 启动类上@MapperSan需要添加模块路径