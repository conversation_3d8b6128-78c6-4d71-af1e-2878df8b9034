package com.cscec3b.iti.taskmesage.service;

import java.time.LocalDateTime;
import java.time.ZoneId;
import java.util.Objects;

import org.quartz.*;
import org.springframework.scheduling.quartz.QuartzJobBean;
import org.springframework.stereotype.Service;

import com.baomidou.mybatisplus.core.toolkit.Wrappers;
import com.cscec3b.iti.common.redis.lock.annotation.Lock;
import com.cscec3b.iti.taskmesage.dto.TaskAndMsgDto;
import com.cscec3b.iti.taskmesage.mapper.TodoTaskMapper;
import com.cscec3b.iti.taskmesage.model.TodoTask;
import com.cscec3b.iti.taskmesage.utils.ReminderTimeCalculator;
import com.google.common.collect.Sets;

import cn.hutool.json.JSONUtil;
import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;

@Slf4j
@Service
// 持久化
@PersistJobDataAfterExecution
// 禁止并发执行
@DisallowConcurrentExecution
@RequiredArgsConstructor
public class RemindJobBean extends QuartzJobBean {

    private final TodoTaskMapper todoTaskMapper;
    private final TaskAndMessageService taskAndMessageService;
    @Override
    @Lock
    protected void executeInternal(JobExecutionContext context) throws JobExecutionException {
        log.info("remind job start :{}, key: {}", context.getFireTime(), context.getJobDetail().getKey());
        final JobDetail jobDetail = context.getJobDetail();
        final String taskCode = (String)context.getMergedJobDataMap().get("taskCode");
        final TodoTask task =
            todoTaskMapper.selectOne(Wrappers.<TodoTask>lambdaQuery().eq(TodoTask::getTaskCode, taskCode));
        // 待办中
        log.info("remind job detail :{} ", JSONUtil.toJsonStr(task));
        if (shouldContinueJob(task)) {
            processJob(task);
        } else {
            cleanupJob(context, task);
        }
        log.info("remind job finish");
    }

    /**
     * 处理作业
     *
     * @param task 任务
     * @throws RuntimeException 运行时异常
     */
    private void processJob(TodoTask task) throws RuntimeException {
        // 增加当前执行次数
        task.setCurrentCount(task.getCurrentCount() + 1);
        // 获取下次执行时间(在当前基础上加1分钟计算)
        LocalDateTime nextReminderTime = ReminderTimeCalculator.calculateNextReminderTime(task.getRemindExpression(),
            LocalDateTime.now().plusMinutes(1));

        // nextReminderTime = DateUtils.adjustToNextWorkingTime(nextReminderTime);
        task.setNextRemindTime(nextReminderTime.atZone(ZoneId.systemDefault()).toInstant().toEpochMilli());
        todoTaskMapper.updateById(task);

        try {
            final TaskAndMsgDto taskAndMsgDto = new TaskAndMsgDto(task.getWebLink(), task.getAppLink(),
                task.getConfigCode(), task.getPayload(), Sets.newHashSet(task.getTargetUser()));
            log.info("remind job execute:{}", JSONUtil.toJsonStr(taskAndMsgDto));
            taskAndMessageService.sendNoticeMsg(taskAndMsgDto);
        } catch (Exception e) {
            // todo 异常重试
            log.info("remind job failed:{}", JSONUtil.toJsonStr(e));
            throw new RuntimeException(e);
        }
    }

    private boolean shouldContinueJob(TodoTask task) {
        return Objects.nonNull(task) && task.getState() == 0
            && (task.getCurrentCount() < task.getRepeatCount() || task.getRepeatCount() == -1);
    }

    /**
     * 清理作业
     *
     * @param context 上下文
     * @param task 任务
     */
    private void cleanupJob(JobExecutionContext context, TodoTask task) {
        try {
            Scheduler scheduler = context.getScheduler();
            JobDetail jobDetail = context.getJobDetail();

            // Unschedule and delete job with additional checks
            if (scheduler.checkExists(jobDetail.getKey())) {
                scheduler.unscheduleJob(context.getTrigger().getKey());
                scheduler.deleteJob(jobDetail.getKey());

                log.info("Job {} successfully cleaned up", jobDetail.getKey());
            }
        } catch (SchedulerException e) {
            log.error("Job cleanup failed", e);
        }
    }
}
