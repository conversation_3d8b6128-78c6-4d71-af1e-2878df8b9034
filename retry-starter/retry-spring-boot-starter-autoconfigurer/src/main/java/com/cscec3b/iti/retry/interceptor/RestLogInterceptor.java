package com.cscec3b.iti.retry.interceptor;

import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.http.HttpRequest;
import org.springframework.http.client.ClientHttpRequestExecution;
import org.springframework.http.client.ClientHttpRequestInterceptor;
import org.springframework.http.client.ClientHttpResponse;

import java.io.BufferedReader;
import java.io.IOException;
import java.io.InputStreamReader;
import java.nio.charset.StandardCharsets;

/**
 * RestTemplate 拦截类
 *
 * <AUTHOR>
 * @date 2022/09/27 10:44
 **/

public class RestLogInterceptor implements ClientHttpRequestInterceptor {

    private static final Logger log = LoggerFactory.getLogger(RestLogInterceptor.class);

    @Override
    public ClientHttpResponse intercept(HttpRequest request, byte[] body, ClientHttpRequestExecution execution)
            throws IOException {
        // 处理请求数据
        traceRequest(request, body);
        ClientHttpResponse response = execution.execute(request, body);
        // 处理响应数据
        traceResponse(response);
        return response;
    }

    /**
     * 处理请求数据
     *
     * @param request 请求对象
     * @param body    请求体
     * @return void
     * <AUTHOR>
     * @date 2022/09/27 14:56
     */
    private void traceRequest(HttpRequest request, byte[] body) {
        log.info("【RestTemplate Request】 URI : {}; Method : {}; Headers : {}; Request body : {};", request.getURI(),
                request.getMethod(), request.getHeaders(), new String(body, StandardCharsets.UTF_8));
    }

    /**
     * 处理响应数据
     *
     * @param response 响应对象
     * @return void
     * <AUTHOR>
     * @date 2022/09/27 14:56
     */
    private void traceResponse(ClientHttpResponse response) throws IOException {
        StringBuilder inputStringBuilder = null;
        try (BufferedReader bufferedReader =
                     new BufferedReader(new InputStreamReader(response.getBody(), StandardCharsets.UTF_8))) {
            inputStringBuilder = new StringBuilder();

            String line = bufferedReader.readLine();
            while (line != null) {
                inputStringBuilder.append(line);
                inputStringBuilder.append('\n');
                line = bufferedReader.readLine();
            }
        } catch (Exception e) {
            log.error("failed to get response body");
        }
        String body = inputStringBuilder == null ? "null" : inputStringBuilder.toString();
        log.info("【RestTemplate Response】 Status code : {}; Status text : {}; Headers : {}; Response body : {};",
                response.getStatusCode(), response.getStatusText(), response.getHeaders(), body);
    }
}
