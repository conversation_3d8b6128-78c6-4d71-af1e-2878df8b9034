package com.cscec3b.iti.projectmanagement.server.pushservice.service.impl;

import static org.junit.Assert.assertEquals;
import static org.junit.Assert.assertNotNull;
import static org.junit.Assert.assertNull;

import java.util.ArrayList;
import java.util.List;

import org.junit.Before;
import org.junit.Test;
import org.mockito.Mockito;

import com.cscec3b.iti.projectmanagement.api.dto.dto.ProjectEventMsgDto;
import com.cscec3b.iti.projectmanagement.server.entity.ProjectEvent;
import com.cscec3b.iti.projectmanagement.server.entity.ProjectEventSubscribe;
import com.cscec3b.iti.projectmanagement.server.mapper.ProjectEventMapper;
import com.cscec3b.iti.projectmanagement.server.mapper.ProjectMapper;
import com.cscec3b.iti.projectmanagement.server.pushservice.consumer.ProjectPushConsumer;
import com.cscec3b.iti.projectmanagement.server.pushservice.enums.ProjectEventEnum;

public class ProjectEventServiceImplTest {

    private ProjectEventServiceImpl projectEventService;
    private ProjectEventMapper projectEventMapper;

    private ProjectMapper projectMapper;
    private ProjectPushConsumer projectPushConsumer;


    @Before
    public void setUp() throws Exception {
        projectEventMapper = Mockito.mock(ProjectEventMapper.class);
        projectPushConsumer = Mockito.mock(ProjectPushConsumer.class);
//        projectEventService = new ProjectEventServiceImpl(projectEventMapper, projectPushConsumer, projectMapper);
    }

    @Test
    public void getSubscriber2SendMsgByEvent() {
        ProjectEventEnum eventEnum = ProjectEventEnum.INITIATION;
        Long projectId = 1L;
        List<String> excludes = new ArrayList<>();
        // Mock projectEventMapper.selectByEventCode()
        ProjectEvent event = new ProjectEvent();
        Mockito.when(projectEventMapper.selectByEventCode(Mockito.anyString())).thenReturn(event);
        // Mock projectEventMapper.getSubscribeListByEvent()
        List<ProjectEventSubscribe> subscribeList = new ArrayList<>();
        subscribeList.add(new ProjectEventSubscribe());
        Mockito.when(projectEventMapper.getSubscribeListByEvent(Mockito.any(), Mockito.any())).thenReturn(subscribeList);
        // Mock event.getEventApi()
        event.setEventApi("http://example.com/api");
        // Mock projectPushConsumer.pushMsg()
        Mockito.doNothing().when(projectPushConsumer).pushMsg(Mockito.any(ProjectEventMsgDto.class), Mockito.any(ProjectEventSubscribe.class));
        // Act
        projectEventService.getSubscriber2SendMsgByEvent(eventEnum, projectId, excludes);

    }
    @Test
    public void testGetSubscriber2SendMsgByEvent_EventNotConfigured() {
        // Arrange
        ProjectEventEnum eventEnum = ProjectEventEnum.INITIATION;
        Long projectId = 1L;
        List<String> excludes = new ArrayList<>();
        // Mock projectEventMapper.selectByEventCode()
        Mockito.when(projectEventMapper.selectByEventCode(Mockito.anyString())).thenReturn(null);
        // Act
        projectEventService.getSubscriber2SendMsgByEvent(eventEnum, projectId, excludes);

    }
    @Test
    public void testGetSubscriber2SendMsgByEvent_NoSubscribers() {
        // Arrange
        ProjectEventEnum eventEnum = ProjectEventEnum.INITIATION;
        Long projectId = 1L;
        List<String> excludes = new ArrayList<>();
        // Mock projectEventMapper.selectByEventCode()
        ProjectEvent event = new ProjectEvent();
        Mockito.when(projectEventMapper.selectByEventCode(Mockito.anyString())).thenReturn(event);
        // Mock projectEventMapper.getSubscribeListByEvent()
        Mockito.when(projectEventMapper.getSubscribeListByEvent(Mockito.anyInt(), Mockito.anyList())).thenReturn(null);
        // Act
        projectEventService.getSubscriber2SendMsgByEvent(eventEnum, projectId, excludes);

    }
    @Test
    public void testGetSubscriber2SendMsgByEvent_NoEventApi() {
        // Arrange
        ProjectEventEnum eventEnum = ProjectEventEnum.INITIATION;
        Long projectId = 1L;
        List<String> excludes = new ArrayList<>();
        // Mock projectEventMapper.selectByEventCode()
        ProjectEvent event = new ProjectEvent();
        Mockito.when(projectEventMapper.selectByEventCode(Mockito.anyString())).thenReturn(event);
        // Mock projectEventMapper.getSubscribeListByEvent()
        List<ProjectEventSubscribe> subscribeList = new ArrayList<>();
        subscribeList.add(new ProjectEventSubscribe());
        Mockito.when(projectEventMapper.getSubscribeListByEvent(Mockito.any(), Mockito.any())).thenReturn(subscribeList);
        // Act
        projectEventService.getSubscriber2SendMsgByEvent(eventEnum, projectId, excludes);

    }



    @Test
    public void testSelectByPrimaryKeyPositive() {
        // Positive test case
        Integer id = 100001;
        final ProjectEvent event = new ProjectEvent().setId(100001);
        Mockito.doReturn(projectEventService.selectByPrimaryKey(id)).doReturn(event);
        assertNotNull(event);
        assertEquals(id, event.getId());
    }
    @Test
    public void testSelectByPrimaryKeyNegative() {
        // Negative test case
        Integer id = -1;
        ProjectEvent result = projectEventService.selectByPrimaryKey(id);
        assertNull(result);
    }

    @Test
    public void updateByPrimaryKey() {
    }

    @Test
    public void updateBatchSelective() {
    }

    @Test
    public void getProjectEvents() {
    }

    @Test
    public void createEvent() {
    }

    @Test
    public void updateEvent() {
    }

    @Test
    public void updateEventStatus() {
    }
}
