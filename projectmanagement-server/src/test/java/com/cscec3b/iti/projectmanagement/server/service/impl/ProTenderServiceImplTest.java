package com.cscec3b.iti.projectmanagement.server.service.impl;

import java.lang.reflect.Method;
import java.math.BigDecimal;
import java.util.ArrayList;
import java.util.HashMap;
import java.util.List;
import java.util.Map;

import org.junit.Assert;
import org.junit.Before;
import org.junit.Test;
import org.junit.runner.RunWith;
import org.mockito.InjectMocks;
import org.mockito.Mock;
import org.mockito.Mockito;
import org.powermock.api.mockito.PowerMockito;
import org.powermock.api.support.membermodification.MemberModifier;
import org.powermock.core.classloader.annotations.PrepareForTest;
import org.powermock.modules.junit4.PowerMockRunner;

import com.cscec3b.iti.common.base.dictionary.YesNoEnum;
import com.cscec3b.iti.common.web.exception.BusinessException;
import com.cscec3b.iti.projectmanagement.api.dto.request.BidOpenRecordReq;
import com.cscec3b.iti.projectmanagement.api.dto.request.BidSummaryReq;
import com.cscec3b.iti.projectmanagement.api.dto.request.ContractEntryReq;
import com.cscec3b.iti.projectmanagement.api.dto.request.MarketProReq;
import com.cscec3b.iti.projectmanagement.server.constant.Constants;
import com.cscec3b.iti.projectmanagement.server.entity.Project;
import com.cscec3b.iti.projectmanagement.server.entity.ProjectProgress;
import com.cscec3b.iti.projectmanagement.server.mapper.ProTenderMapper;
import com.cscec3b.iti.projectmanagement.server.service.ProjectProgressService;
import com.cscec3b.iti.projectmanagement.server.service.ProjectService;
import com.google.common.collect.Lists;

import lombok.extern.slf4j.Slf4j;

@RunWith(PowerMockRunner.class)
@PrepareForTest({ProTenderServiceImpl.class})
@Slf4j
public class ProTenderServiceImplTest {

    @InjectMocks
    private ProTenderServiceImpl proTenderServiceImpl;

    @Mock
    private ProTenderMapper proTenderMapper;

    @Mock
    private ProjectService projectService;

    @Mock
    private ProjectProgressService projectProgressService;

    @Before
    public void setUp() throws IllegalAccessException {

        MemberModifier.field(ProTenderServiceImpl.class, "projectService").set(proTenderServiceImpl, projectService);
        MemberModifier.field(ProTenderServiceImpl.class, "earlyWarningService").set(proTenderServiceImpl, projectProgressService);
        proTenderServiceImpl = PowerMockito.spy(proTenderServiceImpl);
    }

    @Test
    public void approvalByTender() throws Exception {
        MarketProReq<BidSummaryReq> obj = new MarketProReq<>();
        BidSummaryReq req = new BidSummaryReq();
        obj.setAssociatedId(1l).setOriginFileType("tender_summary1");
        Map<String, Object> map = new HashMap<String, Object>();
        map.put("123", 211);
        map.put("222", "3242");
        req.setProjectBelong("国外").setProvince("湖北").setCity("武汉").setRegion("江夏区").setAddress("中国.湖北.武汉.洪山区.中建三局G5-1")
            .setCountry("中国").setTotalDuration("1").setCountryProjectType("国家类型1").setMarketProjectType("市场类型1")
            .setProjectType("公司类型1").setProjectAttachment1(Lists.newArrayList(map)).setInvestmentProjects("是")
            .setNoTaxIncludedMoney(new BigDecimal(1)).setBusinessType("基础设施1").setCustomerName("客户姓名2")
            .setEnterpriseType("客户企业性质").setIsCreateHead("否").setBusinessType("基础设施1")
            .setBidOpeningRecords(new ArrayList<>());
        obj.setData(req);
        PowerMockito.doReturn(Constants.NUMBER_ZERO).when(proTenderMapper).createBidSummary(Mockito.any());
        PowerMockito.doNothing().when(proTenderServiceImpl, "createBidOpenRecord", Mockito.any(), Mockito.any());
        try {
            proTenderServiceImpl.approvalByTender(obj);
        } catch (BusinessException e) {
            Assert.assertTrue(8010009 == e.getStatus());
        }
        obj.setOriginFileType("tender_summary");
        try {
            proTenderServiceImpl.approvalByTender(obj);
        } catch (BusinessException e) {
            Assert.assertTrue(8010010 == e.getStatus());
        }
        PowerMockito.doReturn(Constants.NUMBER_ONE).when(proTenderMapper).createBidSummary(Mockito.any());
        List<Project> list = Lists.newArrayList(new Project().setId(1l));
        PowerMockito.doReturn(list).when(projectService).qryProjectByConId(Mockito.any(), Mockito.any());
        try {
            proTenderServiceImpl.approvalByTender(obj);
        } catch (BusinessException e) {
            Assert.assertTrue(8010011 == e.getStatus());
        }
        PowerMockito.doReturn(new ArrayList<>()).when(projectService).qryProjectByConId(Mockito.any(), Mockito.any());
        PowerMockito.doReturn(Constants.NUMBER_ZERO).when(projectService).createProject(Mockito.any());
        try {
            proTenderServiceImpl.approvalByTender(obj);
        } catch (BusinessException e) {
            Assert.assertTrue(8010012 == e.getStatus());
        }
        obj.getData().setProjectBelong("国内");
        PowerMockito.doReturn(Constants.NUMBER_ONE).when(projectService).createProject(Mockito.any());
        try {
            proTenderServiceImpl.approvalByTender(obj);
        } catch (Exception e) {

        }
        obj.getData().setIsCreateHead("是").setContractMode1("模式1").setContractMode2("模式2").setMarketProjectType("口径1")
                .setMarketProjectType2("口径2").setProjectType("类型1").setProjectType2("类型2").setProjectType3("类型3")
                .setProjectType4("类型4");
        try {
            proTenderServiceImpl.approvalByTender(obj);
        } catch (Exception e) {

        }
    }

    @Test
    public void contractEntry() throws Exception {
        MarketProReq<ContractEntryReq> obj = new MarketProReq<>();
        ContractEntryReq req = new ContractEntryReq();
        obj.setAssociatedId(1l).setOriginFileType("投标总结123");
        Map<String, Object> map = new HashMap<String, Object>();
        map.put("123", 211);
        map.put("222", "3242");
        req.setProjectBelong("国外").setProvince("湖北").setCity("武汉").setRegion("江夏区").setAddress("中国.湖北.武汉.洪山区.中建三局G5-1")
                .setCountry("中国")
                .setCountryProjectType("国家类型1").setMarketProjectType("市场类型1").setProjectType("公司类型1")
                .setProjectAttachment1(Lists.newArrayList(map)).setInvestmentProjects("是")
                .setNoTaxIncludedMoney(new BigDecimal(1)).setBusinessType("基础设施1").setCustomerName("客户姓名2")
                .setContractManager("admin").setEnterpriseType("客户企业性质").setBusinessType("基础设施1")
                .setIsFirstContract(YesNoEnum.YES.getDictCode()).setBelongId(1l);
        obj.setData(req);
        PowerMockito.doReturn(Constants.NUMBER_ZERO).when(proTenderMapper).createBidSummary(Mockito.any());
        try {
            proTenderServiceImpl.contractEntry(obj);
        } catch (BusinessException e) {
            Assert.assertTrue(8010009 == e.getStatus());
        }
        obj.setOriginFileType("presentation");
        PowerMockito.doReturn(new ArrayList<>()).when(projectService).qryProjectByConId(Mockito.any(), Mockito.any());
        try {
            proTenderServiceImpl.contractEntry(obj);
        } catch (BusinessException e) {
            Assert.assertTrue(8010013 == e.getStatus());
        }
        PowerMockito.doReturn(Lists.newArrayList(new Project().setId(1l), new Project().setId(2l))).when(projectService)
                .qryProjectByConId(Mockito.any(), Mockito.any());
        try {
            proTenderServiceImpl.contractEntry(obj);
        } catch (BusinessException e) {
            Assert.assertTrue(8010013 == e.getStatus());
        }
        PowerMockito.doReturn(Lists.newArrayList(new Project().setId(1l))).when(projectService)
                .qryProjectByConId(Mockito.any(), Mockito.any());
        PowerMockito.doReturn(Constants.NUMBER_ONE).when(proTenderMapper).qryContractByBelongId(Mockito.anyLong());
        try {
            proTenderServiceImpl.contractEntry(obj);
        } catch (BusinessException e) {
            Assert.assertTrue(8010016 == e.getStatus());
        }
        PowerMockito.doReturn(Constants.NUMBER_ZERO).when(proTenderMapper).qryContractByBelongId(Mockito.anyLong());
        PowerMockito.doReturn(Constants.NUMBER_ZERO).when(proTenderMapper).createContract(Mockito.any());
        try {
            proTenderServiceImpl.contractEntry(obj);
        } catch (BusinessException e) {
            Assert.assertTrue(8010014 == e.getStatus());
        }
        PowerMockito.doReturn(Constants.NUMBER_ONE).when(proTenderMapper).createContract(Mockito.any());
        PowerMockito.doReturn(Constants.NUMBER_ZERO).when(projectService).updateProject(Mockito.any());
        obj.setOriginFileType("投标总结");
        try {
            proTenderServiceImpl.contractEntry(obj);
        } catch (BusinessException e) {
            Assert.assertTrue(8010015 == e.getStatus());
        }
        obj.getData().setProjectBelong("国内");
        try {
            proTenderServiceImpl.contractEntry(obj);
        } catch (BusinessException e) {
            Assert.assertTrue(8010015 == e.getStatus());
        }
        obj.getData().setContractMode1("模式1").setContractMode2("模式2").setMarketProjectType("口径1")
                .setMarketProjectType2("口径2").setProjectType("类型1").setProjectType2("类型2").setProjectType3("类型3")
                .setProjectType4("类型4").setProjectShortName("234");
        try {
            proTenderServiceImpl.contractEntry(obj);
        } catch (BusinessException e) {
            Assert.assertTrue(8010015 == e.getStatus());
        }
        PowerMockito.doReturn(new Project().setId(1l)).when(projectService).setProjectMoney(Mockito.any());
        PowerMockito.doReturn(Constants.NUMBER_ONE).when(projectService).updateProject(Mockito.any());
        Assert.assertTrue(proTenderServiceImpl.contractEntry(obj));
        obj.getData().setIsFirstContract(YesNoEnum.NO.getDictCode());
        Assert.assertTrue(proTenderServiceImpl.contractEntry(obj));
    }

    @Test
    public void tenderEntry() throws Exception {
        MarketProReq<BidSummaryReq> obj = new MarketProReq<>();
        BidSummaryReq req = new BidSummaryReq();
        obj.setAssociatedId(1l).setOriginFileType("presentation1");
        Map<String, Object> map = new HashMap<String, Object>();
        map.put("123", 211);
        map.put("222", "3242");
        req.setProjectBelong("国外").setProvince("湖北").setCity("武汉").setRegion("江夏区").setAddress("中国.湖北.武汉.洪山区.中建三局G5-1")
                .setCountry("中国").setCountryProjectType("国家类型1").setMarketProjectType("市场类型1").setProjectType("公司类型1")
                .setProjectAttachment1(Lists.newArrayList(map)).setInvestmentProjects("是")
                .setNoTaxIncludedMoney(new BigDecimal(1)).setBusinessType("基础设施1").setCustomerName("客户姓名2")
                .setEnterpriseType("客户企业性质").setBusinessType("基础设施1")
                .setEstimatedCost(new BigDecimal(Constants.NUMBER_ONE));
        obj.setData(req);
        try {
            proTenderServiceImpl.tenderEntry(obj);
        } catch (BusinessException e) {
            Assert.assertTrue(8010009 == e.getStatus());
        }
        obj.setOriginFileType("presentation");
        try {
            proTenderServiceImpl.tenderEntry(obj);
        } catch (BusinessException e) {
            Assert.assertTrue(8010037 == e.getStatus());
        }
        obj.getData().setBelongId(1l);
        PowerMockito.doReturn(Constants.NUMBER_ONE).when(proTenderMapper).qryTenderByBelongId(Mockito.anyLong());
        try {
            proTenderServiceImpl.tenderEntry(obj);
        } catch (BusinessException e) {
            Assert.assertTrue(8010036 == e.getStatus());
        }
        PowerMockito.doReturn(Constants.NUMBER_ZERO).when(proTenderMapper).createBidSummary(Mockito.any());
        PowerMockito.doReturn(Constants.NUMBER_ZERO).when(proTenderMapper).qryTenderByBelongId(Mockito.anyLong());
        try {
            proTenderServiceImpl.tenderEntry(obj);
        } catch (BusinessException e) {
            Assert.assertTrue(8010010 == e.getStatus());
        }
        PowerMockito.doNothing().when(proTenderServiceImpl, "createBidOpenRecord", Mockito.any(), Mockito.any());
        PowerMockito.doReturn(Constants.NUMBER_ONE).when(proTenderMapper).createBidSummary(Mockito.any());
        PowerMockito.doReturn(Lists.newArrayList(Constants.NUMBER_ONE, Constants.NUMBER_ONE, Constants.NUMBER_ZERO))
                .when(proTenderMapper).qryContractCount(Mockito.anyLong(), Mockito.any());
        Assert.assertTrue(proTenderServiceImpl.tenderEntry(obj));

        PowerMockito.doReturn(Lists.newArrayList(Constants.NUMBER_ZERO)).when(proTenderMapper)
                .qryContractCount(Mockito.anyLong(), Mockito.any());
        PowerMockito.doReturn(Lists.newArrayList(new Project().setId(1l), new Project().setId(2l))).when(projectService)
                .qryProjectByConId(Mockito.any(), Mockito.anyLong());
        try {
            proTenderServiceImpl.tenderEntry(obj);
        } catch (BusinessException e) {
            Assert.assertTrue(8010007 == e.getStatus());
        }
        PowerMockito.doReturn(null).when(projectService).qryProjectByConId(Mockito.any(), Mockito.anyLong());
        try {
            proTenderServiceImpl.tenderEntry(obj);
        } catch (BusinessException e) {
            Assert.assertTrue(8010007 == e.getStatus());
        }
        PowerMockito.doReturn(Lists.newArrayList(new Project().setId(1l))).when(projectService)
                .qryProjectByConId(Mockito.any(), Mockito.anyLong());
        PowerMockito
                .doReturn(Lists.newArrayList(new Project().setId(1l).setTotalAmount(new BigDecimal(Constants.NUMBER_ONE))))
                .when(projectService).qryProjectByConId(Mockito.any(), Mockito.anyLong());
        PowerMockito.doReturn(Constants.NUMBER_ZERO).when(projectService).updateProject(Mockito.any());
        try {
            proTenderServiceImpl.tenderEntry(obj);
        } catch (BusinessException e) {
            Assert.assertTrue(8010038 == e.getStatus());
        }
        PowerMockito.doReturn(Constants.NUMBER_ONE).when(projectService).updateProject(Mockito.any());
        proTenderServiceImpl.tenderEntry(obj);
        Assert.assertTrue(proTenderServiceImpl.tenderEntry(obj));
    }

    @Test
    public void createBidOpenRecord() throws Exception {
        Method privateMethod =
                ProTenderServiceImpl.class.getDeclaredMethod("createBidOpenRecord", List.class, Long.class);
        privateMethod.setAccessible(true);
        privateMethod.invoke(proTenderServiceImpl, new ArrayList<>(), 1l);
        PowerMockito.doReturn(Constants.NUMBER_ZERO).when(proTenderMapper).batchSaveRecords(Mockito.any());
        try {
            privateMethod.invoke(proTenderServiceImpl, Lists.newArrayList(new BidOpenRecordReq().setBidder("12sdsd")),
                    1l);
        } catch (Exception e) {
            Assert.assertEquals(8010039, ((BusinessException) e.getCause()).getStatus());
        }
        PowerMockito.doReturn(Constants.NUMBER_ONE).when(proTenderMapper).batchSaveRecords(Mockito.any());
        privateMethod.invoke(proTenderServiceImpl, Lists.newArrayList(new BidOpenRecordReq().setBidder("12sdsd")), 1l);
    }

    @Test
    public void updateProjectProgressStatus() throws Exception {
        ProjectProgress progress = new ProjectProgress().setProjectId(12L)
                .setSignTime(System.currentTimeMillis()).setApproveFinishTime(System.currentTimeMillis());
        PowerMockito.doReturn(progress).when(projectProgressService, "selectProjectProgress", Mockito.anyLong());

        PowerMockito.doNothing().when(projectProgressService, "saveProjectProgress", Mockito.any());
        // proTenderServiceImpl.updateProjectProgressStatus(IndContractsTypeEnum.TENDER_SUMMARY, 12L, );

        PowerMockito.doNothing().when(projectProgressService, "updateProjectProgress", Mockito.any());
        // proTenderServiceImpl.updateProjectProgressStatus(IndContractsTypeEnum.PRESENTATION, 12L, );
    }
}
