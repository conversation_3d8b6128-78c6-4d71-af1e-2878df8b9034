//package com.cscec3b.iti.projectmanagement.server.util;
//
//import org.junit.Assert;
//import org.junit.Test;
//import org.junit.jupiter.api.BeforeEach;
//import org.junit.runner.RunWith;
//import org.mockito.InjectMocks;
//import org.mockito.Mock;
//import org.mockito.Mockito;
//import org.powermock.api.mockito.PowerMockito;
//import org.powermock.core.classloader.annotations.PrepareForTest;
//import org.powermock.modules.junit4.PowerMockRunner;
//import org.springframework.data.redis.core.StringRedisTemplate;
//import org.springframework.data.redis.core.ValueOperations;
//
//import com.cscec3b.iti.common.web.config.SpringUtils;
//import com.cscec3b.iti.projectmanagement.api.dto.dto.CurrentOrg;
//import com.cscec3b.iti.projectmanagement.api.dto.response.PmUserInfoResp;
//import com.cscec3b.iti.projectmanagement.server.config.PmUserContextHolder;
//import com.cscec3b.iti.usercenter.server.client.entity.SdkTokenExtends;
//
//import cn.hutool.json.JSONUtil;
//
//@RunWith(PowerMockRunner.class)
//@PrepareForTest({PmUserContextHolder.class, JSONUtil.class, SpringUtils.class})
//public class UserUtilTest {
//
//	@InjectMocks
//	UserUtil userUtil;
//
//	@Mock
//	private StringRedisTemplate stringRedisTemplate;
//
//	@Mock
//	private ValueOperations valueOperations;
//
//	@Mock
//    SdkTokenExtends tokenExtends;
//
//
//	@BeforeEach
//	public void setUp() {
//		userUtil = PowerMockito.spy(userUtil);
//        tokenExtends = new SdkTokenExtends();
//		tokenExtends.setUsername("name").setTokenType(1).setUserUuid("uuid");
//		tokenExtends.setJti("jti").setAppid("appid");
//		PowerMockito.mockStatic(JSONUtil.class);
//		PowerMockito.mockStatic(SpringUtils.class);
//	}
//
//
//	@Test
//	public void userInfo() throws Exception {
//		PowerMockito.mockStatic(PmUserContextHolder.class);
//		PowerMockito.when(PmUserContextHolder.class,"userInfo").thenReturn(tokenExtends);
//		Assert.assertSame(tokenExtends,UserUtil.userInfo());
//	}
//
//	@Test
//	public void userId() throws Exception {
//		PowerMockito.mockStatic(PmUserContextHolder.class);
//		PowerMockito.when(UserUtil.class,"userInfo").thenReturn(tokenExtends);
//		Assert.assertEquals(tokenExtends.getUserUuid(),UserUtil.userId());
//	}
//
//	@Test
//	public void userName() throws Exception {
//		PowerMockito.mockStatic(PmUserContextHolder.class);
//		PowerMockito.when(UserUtil.class,"userInfo").thenReturn(tokenExtends);
//		Assert.assertEquals(tokenExtends.getUsername(),UserUtil.userName());
//	}
//
//	@Test
//	public void curStdOrgId() throws Exception {
//		PmUserInfoResp infoResp = new PmUserInfoResp().setCurrentOrg(new CurrentOrg().setStdOrgCode("stdId"));
//		//PowerMockito.doReturn(infoResp).when(UserUtil.class, "getUserInfoResp");
//		PowerMockito.mockStatic(PmUserContextHolder.class);
//		PowerMockito.mockStatic(JSONUtil.class);
//		PowerMockito.when(UserUtil.class,"userInfo").thenReturn(tokenExtends);
//		PowerMockito.doReturn("jsonStr").when(valueOperations).get(Mockito.anyString());
//		PowerMockito.when(JSONUtil.toBean("{}", PmUserInfoResp.class)).thenReturn(infoResp);
//		//PowerMockito.when(SpringUtils.class,"getBean",Mockito.any(StringRedisTemplate.class)).thenReturn
//		(stringRedisTemplate);
//        PowerMockito.when(stringRedisTemplate.opsForValue()).thenReturn(valueOperations);
//		//PowerMockito.when(UserUtil.class, "getUserInfoResp").thenReturn(infoResp);
//		//Assert.assertEquals(infoResp.getCurrentOrg().getStdOrgId(), UserUtil.curStdOrgId());
//	}
//
//	@Test
//	public void curLeanBuildOrgId() {
//	}
//}
