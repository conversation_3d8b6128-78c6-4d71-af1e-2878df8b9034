package com.cscec3b.iti.projectmanagement.server.service.impl;

import com.cscec3b.iti.common.web.exception.BusinessException;
import com.cscec3b.iti.projectmanagement.api.dto.request.BureauSupplementaryAgreementReq;
import com.cscec3b.iti.projectmanagement.api.dto.request.MarketProReq;
import com.cscec3b.iti.projectmanagement.server.entity.BureauSupplementaryAgreement;
import com.cscec3b.iti.projectmanagement.server.entity.Project;
import com.cscec3b.iti.projectmanagement.server.mapper.BureauSupplementaryAgreementMapper;
import com.cscec3b.iti.projectmanagement.server.service.ProjectService;
import lombok.extern.slf4j.Slf4j;
import org.junit.Assert;
import org.junit.Before;
import org.junit.Test;
import org.junit.runner.RunWith;
import org.mockito.InjectMocks;
import org.mockito.Mock;
import org.mockito.Mockito;
import org.powermock.api.mockito.PowerMockito;
import org.powermock.api.support.membermodification.MemberModifier;
import org.powermock.core.classloader.annotations.PrepareForTest;
import org.powermock.modules.junit4.PowerMockRunner;

import java.math.BigDecimal;
import java.util.ArrayList;
import java.util.List;

@Slf4j
@RunWith(PowerMockRunner.class)
@PrepareForTest({BureauSupplementaryAgreementServiceImpl.class})
public class BureauSupplementaryAgreementServiceImplTest {

    @InjectMocks
    private BureauSupplementaryAgreementServiceImpl bureauSupplementaryAgreementService;

    @Mock
    private BureauSupplementaryAgreementMapper bureauSupplementaryAgreementMapper;

    @Mock
    private ProjectService projectService;

    @Before
    public void setUp() throws Exception {
        bureauSupplementaryAgreementService = PowerMockito.spy(bureauSupplementaryAgreementService);
        MemberModifier.field(BureauSupplementaryAgreementServiceImpl.class, "bureauSupplementaryAgreementMapper")
                .set(bureauSupplementaryAgreementService, bureauSupplementaryAgreementMapper);
        MemberModifier.field(BureauSupplementaryAgreementServiceImpl.class, "projectService")
                .set(bureauSupplementaryAgreementService, projectService);
    }

    @Test
    public void entry() throws Exception {
        MarketProReq<BureauSupplementaryAgreementReq> request = new MarketProReq<>();
        BureauSupplementaryAgreementReq data = new BureauSupplementaryAgreementReq();
        request.setData(data);
        try {
            bureauSupplementaryAgreementService.entry(request);
        } catch (BusinessException e) {
            Assert.assertEquals(8010004, e.getStatus());
        }

        request.setOriginFileType("internal_presentation");
        data.setBelongId(111L);
        List<Project> projectList = new ArrayList<>();
        Project p1 = new Project();
        Project p2 = new Project();
        projectList.add(p1);
        projectList.add(p2);
        PowerMockito.doReturn(projectList).when(projectService, "qryProjectByConId", Mockito.anyInt(), Mockito.anyLong());
        try {
            bureauSupplementaryAgreementService.entry(request);
        } catch (BusinessException e) {
            Assert.assertEquals(8010007, e.getStatus());
        }

        request.setAssociatedId(123L);
        projectList.remove(p1);
        PowerMockito.doReturn(projectList).when(projectService, "qryProjectByConId", Mockito.anyInt(), Mockito.anyLong());
        PowerMockito.doReturn(1).when(bureauSupplementaryAgreementMapper, "selectCountByBelongId", Mockito.anyLong());
        try {
            bureauSupplementaryAgreementService.entry(request);
        } catch (BusinessException e) {
            Assert.assertEquals(8010018, e.getStatus());
        }

        PowerMockito.doReturn(0).when(bureauSupplementaryAgreementMapper, "selectCountByBelongId", Mockito.anyLong());
        PowerMockito.doReturn(0).when(bureauSupplementaryAgreementMapper, "saveBureauSupplementaryAgreement", Mockito.any());
        try {
            bureauSupplementaryAgreementService.entry(request);
        } catch (BusinessException e) {
            Assert.assertEquals(8010008, e.getStatus());
        }
        data.setSupplementAmount(new BigDecimal(100));
        p2.setSelfTotalServiceAmount(new BigDecimal(80)).setTotalAmount(new BigDecimal(90));
        PowerMockito.doReturn(1).when(bureauSupplementaryAgreementMapper, "saveBureauSupplementaryAgreement", Mockito.any());
        PowerMockito.doReturn(0).when(projectService, "updateProject", Mockito.any());
        try {
            bureauSupplementaryAgreementService.entry(request);
        } catch (BusinessException e) {
            Assert.assertEquals(8010008, e.getStatus());
        }

        PowerMockito.doReturn(1).when(projectService, "updateProject", Mockito.any());
        Assert.assertTrue(bureauSupplementaryAgreementService.entry(request));
    }

    @Test
    public void get() throws Exception {
        PowerMockito.doReturn(null).when(bureauSupplementaryAgreementMapper, "getById", Mockito.anyLong());
        try {
            bureauSupplementaryAgreementService.get(10L);
        } catch (BusinessException e) {
            Assert.assertEquals(8010081, e.getStatus());
        }
        BureauSupplementaryAgreement bsa = new BureauSupplementaryAgreement();
        bsa.setId(22L);
        PowerMockito.doReturn(bsa).when(bureauSupplementaryAgreementMapper, "getById", Mockito.anyLong());
        Assert.assertEquals(bsa.getId(), bureauSupplementaryAgreementService.get(10L).getId());
    }
}