package com.cscec3b.iti.projectmanagement.server.service.impl;

import com.cscec3b.iti.projectmanagement.server.constant.Constants;
import com.cscec3b.iti.projectmanagement.server.mapper.IdTableMapper;
import com.cscec3b.iti.projectmanagement.server.service.ProjectService;
import org.junit.Before;
import org.junit.Test;
import org.junit.runner.RunWith;
import org.mockito.InjectMocks;
import org.mockito.Mock;
import org.mockito.MockitoAnnotations;
import org.mockito.junit.MockitoJUnitRunner;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.boot.test.context.SpringBootTest;
import org.springframework.scheduling.annotation.AsyncResult;
import org.springframework.test.context.junit4.SpringRunner;

import java.time.LocalDate;
import java.time.ZoneId;
import java.time.format.DateTimeFormatter;
import java.util.ArrayList;
import java.util.List;
import java.util.concurrent.CompletableFuture;

import static org.junit.Assert.assertEquals;
import static org.mockito.ArgumentMatchers.anyString;
import static org.mockito.Mockito.when;

// @RunWith(MockitoJUnitRunner.class)
@RunWith(SpringRunner.class)
@SpringBootTest
public class IdTableServiceImplTest {

    @Mock
    private IdTableMapper idTableMapper;

    private ProjectService projectService;

    @InjectMocks
    // private IdTableServiceImpl idTableService;

    @Autowired
    private IdTableServiceImpl idTableService;

    @Before
    public void setup() {
        MockitoAnnotations.initMocks(this);
        projectService = new ProjectServiceImpl();
    }

    @Test
    public void testGetIncrementId_PositiveCase() {
        // Mock the necessary methods in the mapper
        when(idTableMapper.getIncrementId(anyString(), anyString())).thenReturn(10L);
        when(idTableMapper.updateIncrementId(anyString(), anyString())).thenReturn(1);

        // Call the method to be tested
        //        Long result = projectService.getCpmProjectKey("prefix", "2022-01-01");

        // Verify the expected behavior
        //        assertEquals(Long.valueOf(10), result);
    }

    @Test
    public void testGetIncrementId_NegativeCase() {
        // Mock the necessary methods in the mapper
        when(idTableMapper.getIncrementId(anyString(), anyString())).thenReturn(null);
        when(idTableMapper.insertOrUpdateIncrementId(anyString())).thenReturn(1);

        // Call the method to be tested
        //        Long result = projectService.getIncrementId("prefix", "2022-01-01");

        // Verify the expected behavior
        //        assertEquals(Long.valueOf(1), result);
    }


    @Before
    public void setUp() {
        // Setup any common configurations here
    }

    @Test
    public void testGetCpmProjectKey() {
        // Mock the getIncrementId method to return a specific value
        Long incrementId = 123L;
        when(idTableService.getIncrementId(Constants.SEQUENCE_PREFIX, null)).thenReturn(incrementId);

        // Generate the expected key
        String curDate = DateTimeFormatter.ofPattern("yyMMdd").withZone(ZoneId.systemDefault()).format(LocalDate.now());
        String sequence = String.format("%03d", incrementId);
        String expectedKey = Constants.SEQUENCE_PREFIX + curDate + sequence;

        // Call the method under test
        String actualKey = idTableService.getCpmProjectKey();

        // Verify the result
        assertEquals(expectedKey, actualKey);
    }

    @Test
    public void testGetCpmProjectKey_Async() {
        // Mock the getIncrementId method to return a specific value
        List<CompletableFuture<String>> futures = new ArrayList<>();
        for (int i = 0; i < 800; i++) {
            final CompletableFuture<String> runAsync = CompletableFuture.supplyAsync(() -> {
                String key = idTableService.getCpmProjectKey();
                System.out.println(key);
                return key;
            });
            futures.add(runAsync);
        }
        CompletableFuture.allOf(futures.toArray(new CompletableFuture[0])).join();

    }
}
