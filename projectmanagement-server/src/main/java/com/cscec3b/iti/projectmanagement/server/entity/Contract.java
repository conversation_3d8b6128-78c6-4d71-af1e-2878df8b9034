package com.cscec3b.iti.projectmanagement.server.entity;

import com.baomidou.mybatisplus.annotation.FieldFill;
import com.baomidou.mybatisplus.annotation.IdType;
import com.baomidou.mybatisplus.annotation.TableField;
import com.baomidou.mybatisplus.annotation.TableId;
import com.baomidou.mybatisplus.annotation.TableName;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;
import lombok.experimental.Accessors;

import java.math.BigDecimal;

/**
 * 市场营销-合同定案表
 */
@ApiModel(description = "市场营销-合同定案表")
@Data
@Accessors(chain = true)
@TableName(value = "contract")
public class Contract {
    /**
     * 主键id
     */
    @TableId(value = "id", type = IdType.AUTO)
    @ApiModelProperty(value = "主键id")
    private Long id;

    /**
     * 发起人单位
     */
    @TableField(value = "submit_person")
    @ApiModelProperty(value = "发起人单位")
    private String submitPerson;

    /**
     * 工程名称
     */
    @TableField(value = "project_name")
    @ApiModelProperty(value = "工程名称")
    private String projectName;

    /**
     * 工程编号
     */
    @TableField(value = "project_code")
    @ApiModelProperty(value = "工程编号")
    private String projectCode;

    /**
     * 工程简称
     */
    @TableField(value = "project_short_name")
    @ApiModelProperty(value = "工程简称")
    private String projectShortName;

    /**
     * 工程属地
     */
    @TableField(value = "project_belong")
    @ApiModelProperty(value = "工程属地")
    private String projectBelong;

    /**
     * 具体地址
     */
    @TableField(value = "address")
    @ApiModelProperty(value = "具体地址")
    private String address;

    /**
     * 工程类型（国家标准）
     */
    @TableField(value = "country_project_type")
    @ApiModelProperty(value = "工程类型（国家标准）")
    private String countryProjectType;

    /**
     * 工程类型（总公司市场口径）
     */
    @TableField(value = "market_project_type")
    @ApiModelProperty(value = "工程类型（总公司市场口径）")
    private String marketProjectType;

    /**
     * 工程类型(总公司市场口径)2
     */
    @TableField(value = "market_project_type2")
    @ApiModelProperty(value = "工程类型(总公司市场口径)2")
    private String marketProjectType2;

    /**
     * 工程类型(总公司综合口径)
     */
    @TableField(value = "project_type")
    @ApiModelProperty(value = "工程类型(总公司综合口径)")
    private String projectType;

    /**
     * 工程类型(总公司综合口径)2
     */
    @TableField(value = "project_type2")
    @ApiModelProperty(value = "工程类型(总公司综合口径)2")
    private String projectType2;

    /**
     * 工程类型(总公司综合口径)3
     */
    @TableField(value = "project_type3")
    @ApiModelProperty(value = "工程类型(总公司综合口径)3")
    private String projectType3;

    /**
     * 工程类型(总公司综合口径)4
     */
    @TableField(value = "project_type4")
    @ApiModelProperty(value = "工程类型(总公司综合口径)4")
    private String projectType4;

    /**
     * 项目附件信息对象
     */
    @TableField(value = "project_attachment")
    @ApiModelProperty(value = "项目附件信息对象")
    private String projectAttachment;

    /**
     * 总分包类别
     */
    @TableField(value = "total_subcontracting_category")
    @ApiModelProperty(value = "总分包类别")
    private String totalSubcontractingCategory;

    /**
     * 结构形式
     */
    @TableField(value = "structural_style")
    @ApiModelProperty(value = "结构形式")
    private String structuralStyle;

    /**
     * 结构形式2
     */
    @TableField(value = "structural_style2")
    @ApiModelProperty(value = "结构形式2")
    private String structuralStyle2;

    /**
     * 是否有钢结构
     */
    @TableField(value = "including_steel")
    @ApiModelProperty(value = "是否有钢结构")
    private String includingSteel;

    /**
     * 最长桩基长度
     */
    @TableField(value = "project_max_length")
    @ApiModelProperty(value = "最长桩基长度")
    private String projectMaxLength;

    /**
     * 最大桩径
     */
    @TableField(value = "project_max_width")
    @ApiModelProperty(value = "最大桩径")
    private String projectMaxWidth;

    /**
     * 合同类型
     */
    @TableField(value = "contract_type")
    @ApiModelProperty(value = "合同类型")
    private String contractType;

    /**
     * 是否装配式
     */
    @TableField(value = "fabricated")
    @ApiModelProperty(value = "是否装配式")
    private String fabricated;

    /**
     * 是否为投融资带动项目
     */
    @TableField(value = "is_investment_financing_driven_projects")
    @ApiModelProperty(value = "是否为投融资带动项目")
    private String isInvestmentFinancingDrivenProjects;

    /**
     * 业务类型
     */
    @TableField(value = "business_type")
    @ApiModelProperty(value = "业务类型")
    private String businessType;

    /**
     * 是否投资项目
     */
    @TableField(value = "investment_projects")
    @ApiModelProperty(value = "是否投资项目")
    private String investmentProjects;

    /**
     * 投资主体
     */
    @TableField(value = "investors")
    @ApiModelProperty(value = "投资主体")
    private String investors;

    /**
     * 客户名称
     */
    @TableField(value = "customer_name")
    @ApiModelProperty(value = "客户名称")
    private String customerName;

    /**
     * 上级相关方/客户母公司
     */
    @TableField(value = "superior_company_name")
    @ApiModelProperty(value = "上级相关方/客户母公司")
    private String superiorCompanyName;

    /**
     * 客户企业性质
     */
    @TableField(value = "enterprise_type")
    @ApiModelProperty(value = "客户企业性质")
    private String enterpriseType;

    /**
     * 建设单位（甲方）联系人
     */
    @TableField(value = "contact_person")
    @ApiModelProperty(value = "建设单位（甲方）联系人")
    private String contactPerson;

    /**
     * 设计单位
     */
    @TableField(value = "designer")
    @ApiModelProperty(value = "设计单位")
    private String designer;

    /**
     * 监理单位
     */
    @TableField(value = "supervisor")
    @ApiModelProperty(value = "监理单位")
    private String supervisor;

    /**
     * 签约主体
     */
    @TableField(value = "signed_subject_value")
    @ApiModelProperty(value = "签约主体")
    private String signedSubjectValue;

    /**
     * 签约主体code
     */
    @TableField(value = "signed_subject_code")
    @ApiModelProperty(value = "签约主体code")
    private String signedSubjectCode;

    /**
     * 实施单位
     */
    @TableField(value = "do_unit")
    @ApiModelProperty(value = "实施单位")
    private String doUnit;

    /**
     * 含税合同总价（人民币）
     */
    @TableField(value = "total_amount")
    @ApiModelProperty(value = "含税合同总价（人民币）")
    private BigDecimal totalAmount;

    /**
     * 不含税金额
     */
    @TableField(value = "no_tax_included_money")
    @ApiModelProperty(value = "不含税金额")
    private BigDecimal noTaxIncludedMoney;

    /**
     * 自行施工不含税金额
     */
    @TableField(value = "mid_amount_self")
    @ApiModelProperty(value = "自行施工不含税金额")
    private BigDecimal midAmountSelf;

    /**
     * 土建不含税金额
     */
    @TableField(value = "self_civil_amount")
    @ApiModelProperty(value = "土建不含税金额")
    private BigDecimal selfCivilAmount;

    /**
     * 安装不含税金额
     */
    @TableField(value = "self_install_amount")
    @ApiModelProperty(value = "安装不含税金额")
    private BigDecimal selfInstallAmount;

    /**
     * 钢结构不含税金额
     */
    @TableField(value = "self_steel_structure_amount")
    @ApiModelProperty(value = "钢结构不含税金额")
    private BigDecimal selfSteelStructureAmount;

    /**
     * 总包服务费
     */
    @TableField(value = "self_total_service_amount")
    @ApiModelProperty(value = "总包服务费")
    private BigDecimal selfTotalServiceAmount;

    /**
     * 暂列金额或甲指分包金额
     */
    @TableField(value = "subcontract_amount")
    @ApiModelProperty(value = "暂列金额或甲指分包金额")
    private BigDecimal subcontractAmount;

    /**
     * 销项税额
     */
    @TableField(value = "project_tax_amount")
    @ApiModelProperty(value = "销项税额")
    private BigDecimal projectTaxAmount;

    /**
     * 合同优化条款
     */
    @TableField(value = "contract_optimize_clause")
    @ApiModelProperty(value = "合同优化条款")
    private String contractOptimizeClause;

    /**
     * 合同优化金额
     */
    @TableField(value = "contract_optimize_amount")
    @ApiModelProperty(value = "合同优化金额")
    private BigDecimal contractOptimizeAmount;

    /**
     * 合同优化率
     */
    @TableField(value = "contract_optimize_ratio")
    @ApiModelProperty(value = "合同优化率")
    private String contractOptimizeRatio;

    /**
     * 暂列金额工作内容
     */
    @TableField(value = "subcontract_content")
    @ApiModelProperty(value = "暂列金额工作内容")
    private String subcontractContent;

    /**
     * 中标项目经理
     */
    @TableField(value = "bid_manager")
    @ApiModelProperty(value = "中标项目经理")
    private String bidManager;

    /**
     * 中标项目经理注册证书编号
     */
    @TableField(value = "bid_manager_code")
    @ApiModelProperty(value = "中标项目经理注册证书编号")
    private String bidManagerCode;

    /**
     * 执行项目经理
     */
    @TableField(value = "excute_manager")
    @ApiModelProperty(value = "执行项目经理")
    private String excuteManager;

    /**
     * 合同项目经理
     */
    @TableField(value = "contract_manager")
    @ApiModelProperty(value = "合同项目经理")
    private String contractManager;

    /**
     * 合同项目经理注册证书编号
     */
    @TableField(value = "contract_manager_code")
    @ApiModelProperty(value = "合同项目经理注册证书编号")
    private String contractManagerCode;

    /**
     * 政府备案项目经理
     */
    @TableField(value = "government_manager")
    @ApiModelProperty(value = "政府备案项目经理")
    private String governmentManager;

    /**
     * 政府备案项目经理注册证书编号
     */
    @TableField(value = "government_manager_code")
    @ApiModelProperty(value = "政府备案项目经理注册证书编号")
    private String governmentManagerCode;

    /**
     * 承包模式
     */
    @TableField(value = "contract_mode1")
    @ApiModelProperty(value = "承包模式")
    private String contractMode1;

    /**
     * 承包模式2
     */
    @TableField(value = "contract_mode2")
    @ApiModelProperty(value = "承包模式2")
    private String contractMode2;

    /**
     * 合同承包范围
     */
    @TableField(value = "contract_scope")
    @ApiModelProperty(value = "合同承包范围")
    private String contractScope;

    /**
     * 发包人指定分包、独立分包的工程
     */
    @TableField(value = "issuer_project")
    @ApiModelProperty(value = "发包人指定分包、独立分包的工程")
    private String issuerProject;

    /**
     * 总工期（天）
     */
    @TableField(value = "count_days")
    @ApiModelProperty(value = "总工期（天）")
    private Integer countDays;

    /**
     * 工期奖罚类型
     */
    @TableField(value = "worker_date_reward_punish")
    @ApiModelProperty(value = "工期奖罚类型")
    private String workerDateRewardPunish;

    /**
     * 工期奖罚条款
     */
    @TableField(value = "worker_reward_punish_appoint")
    @ApiModelProperty(value = "工期奖罚条款")
    private String workerRewardPunishAppoint;

    /**
     * 合同范本类型
     */
    @TableField(value = "contract_style")
    @ApiModelProperty(value = "合同范本类型")
    private String contractStyle;

    /**
     * 质量要求
     */
    @TableField(value = "quality_guarantee")
    @ApiModelProperty(value = "质量要求")
    private String qualityGuarantee;

    /**
     * 质量奖罚类型
     */
    @TableField(value = "reward_punish_type")
    @ApiModelProperty(value = "质量奖罚类型")
    private String rewardPunishType;

    /**
     * 质量奖罚条款
     */
    @TableField(value = "reward_punish_terms")
    @ApiModelProperty(value = "质量奖罚条款")
    private String rewardPunishTerms;

    /**
     * 安全文明施工要求
     */
    @TableField(value = "safety_requirement")
    @ApiModelProperty(value = "安全文明施工要求")
    private String safetyRequirement;

    /**
     * 安全文明施工奖罚条款
     */
    @TableField(value = "safety_reward_punish_terms")
    @ApiModelProperty(value = "安全文明施工奖罚条款")
    private String safetyRewardPunishTerms;

    /**
     * 计价方式
     */
    @TableField(value = "pricing_method")
    @ApiModelProperty(value = "计价方式")
    private String pricingMethod;

    /**
     * 合同形式
     */
    @TableField(value = "contract_form")
    @ApiModelProperty(value = "合同形式")
    private String contractForm;

    /**
     * 人工费是否可调
     */
    @TableField(value = "cost_of_labor_change")
    @ApiModelProperty(value = "人工费是否可调")
    private String costOfLaborChange;

    /**
     * 主材费是否可调
     */
    @TableField(value = "cost_of_labor_change2")
    @ApiModelProperty(value = "主材费是否可调")
    private String costOfLaborChange2;

    /**
     * 是否有预付款
     */
    @TableField(value = "advances_flag")
    @ApiModelProperty(value = "是否有预付款")
    private String advancesFlag;

    /**
     * 进度款付款方式
     */
    @TableField(value = "advances_way")
    @ApiModelProperty(value = "进度款付款方式")
    private String advancesWay;

    /**
     * 月进度付款比例
     */
    @TableField(value = "advances_month_rate")
    @ApiModelProperty(value = "月进度付款比例")
    private String advancesMonthRate;

    /**
     * 竣工验收支付比例
     */
    @TableField(value = "completed_rate")
    @ApiModelProperty(value = "竣工验收支付比例")
    private String completedRate;

    /**
     * 竣工验收收款周期（月）
     */
    @TableField(value = "completed_cycle")
    @ApiModelProperty(value = "竣工验收收款周期（月）")
    private String completedCycle;

    /**
     * 结算比例
     */
    @TableField(value = "settlement_rate")
    @ApiModelProperty(value = "结算比例")
    private String settlementRate;

    /**
     * 结算周期（月）
     */
    @TableField(value = "settlement_cycle")
    @ApiModelProperty(value = "结算周期（月）")
    private String settlementCycle;

    /**
     * 合同编号
     */
    @TableField(value = "contract_code")
    @ApiModelProperty(value = "合同编号")
    private String contractCode;

    /**
     * 独立合同ID
     */
    @TableField(value = "independent_contract_id")
    @ApiModelProperty(value = "独立合同ID")
    private Long independentContractId;

    /**
     * 独立合同类型：1投标总结；2合同定案；3补充协议；4局内部合同定案；5局内部补充协议；
     */
    @TableField(value = "independent_contract_type")
    @ApiModelProperty(value = "独立合同类型：1投标总结；2合同定案；3补充协议；4局内部合同定案；5局内部补充协议；")
    private Integer independentContractType;

    /**
     * 源合同文件的id
     */
    @TableField(value = "origin_file_id")
    @ApiModelProperty(value = "源合同文件的id")
    private Long originFileId;

    /**
     * 保修金
     */
    @TableField(value = "warranty_premium")
    @ApiModelProperty(value = "保修金")
    private String warrantyPremium;

    /**
     * 保修金比例
     */
    @TableField(value = "warranty_premium_rate")
    @ApiModelProperty(value = "保修金比例")
    private String warrantyPremiumRate;

    /**
     * 保修金支付方式
     */
    @TableField(value = "warranty_premium_way")
    @ApiModelProperty(value = "保修金支付方式")
    private String warrantyPremiumWay;

    /**
     * 是否垫资
     */
    @TableField(value = "advances_fund_flag")
    @ApiModelProperty(value = "是否垫资")
    private String advancesFundFlag;

    /**
     * 履约担保方式
     */
    @TableField(value = "guarantee_way")
    @ApiModelProperty(value = "履约担保方式")
    private String guaranteeWay;

    /**
     * 项目及土地是否合法
     */
    @TableField(value = "land_legality_flag")
    @ApiModelProperty(value = "项目及土地是否合法")
    private String landLegalityFlag;

    /**
     * 是否放弃优先受偿权
     */
    @TableField(value = "give_up_compensate_flag")
    @ApiModelProperty(value = "是否放弃优先受偿权")
    private String giveUpCompensateFlag;

    /**
     * 付款比例是否低于百分之八十
     */
    @TableField(value = "pay_rate_less_eighty_flag")
    @ApiModelProperty(value = "付款比例是否低于百分之八十")
    private String payRateLessEightyFlag;

    /**
     * 支付节点时间是否超2个月
     */
    @TableField(value = "node_more_two_month_flag")
    @ApiModelProperty(value = "支付节点时间是否超2个月")
    private String nodeMoreTwoMonthFlag;

    /**
     * 所属源文件id
     */
    @TableField(value = "belong_id")
    @ApiModelProperty(value = "所属源文件id")
    private Long belongId;

    /**
     * 是否首合同
     */
    @TableField(value = "is_first_contract")
    @ApiModelProperty(value = "是否首合同")
    private int isFirstContract;

    /**
     * 实际中标日期
     */
    @TableField(value = "successful_time")
    @ApiModelProperty(value = "实际中标日期")
    private Long successfulTime;

    /**
     * 实际签约日期
     */
    @TableField(value = "actual_signed_time")
    @ApiModelProperty(value = "实际签约日期")
    private Long actualSignedTime;

    /**
     * 合同开工日期
     */
    @TableField(value = "worker_begin_time")
    @ApiModelProperty(value = "合同开工日期")
    private Long workerBeginTime;

    /**
     * 合同竣工日期
     */
    @TableField(value = "worker_end_time")
    @ApiModelProperty(value = "合同竣工日期")
    private Long workerEndTime;

    /**
     * 实际开工日期
     */
    @TableField(value = "real_work_begin_time")
    @ApiModelProperty(value = "实际开工日期")
    private Long realWorkBeginTime;

    /**
     * 预计实际竣工日期
     */
    @TableField(value = "predict_work_end_time")
    @ApiModelProperty(value = "预计实际竣工日期")
    private Long predictWorkEndTime;

    /**
     * 客户级别
     */
    @TableField(value = "customer_level")
    @ApiModelProperty(value = "客户级别")
    private String customerLevel;

    /**
     * 建设单位（甲方）联系人电话
     */
    @TableField(value = "contact_person_mobile")
    @ApiModelProperty(value = "建设单位（甲方）联系人电话")
    private String contactPersonMobile;

    /**
     * 执行项目经理联系方式
     */
    @TableField(value = "excute_manager_code")
    @ApiModelProperty(value = "执行项目经理联系方式")
    private String excuteManagerCode;

    /**
     * 其他
     */
    @TableField(value = "self_other_amount")
    @ApiModelProperty(value = "其他")
    private BigDecimal selfOtherAmount;

    /**
     * 所属办事处
     */
    @TableField(value = "sign_form_office")
    @ApiModelProperty(value = "所属办事处")
    private String signFormOffice;

    /**
     * 省
     */
    @TableField(value = "province")
    @ApiModelProperty(value = "省")
    private String province;

    /**
     * 市
     */
    @TableField(value = "city")
    @ApiModelProperty(value = "市")
    private String city;

    /**
     * 区
     */
    @TableField(value = "region")
    @ApiModelProperty(value = "区")
    private String region;

    /**
     * 国别
     */
    @TableField(value = "country")
    @ApiModelProperty(value = "国别")
    private String country;

    /**
     * 支付方式
     */
    @TableField(value = "pay_type_new")
    @ApiModelProperty(value = "支付方式")
    private String payTypeNew;

    /**
     * 关联的投标总结belongId
     */
    @TableField(value = "pre_file_id")
    @ApiModelProperty(value = "关联的投标总结belongId")
    private Long preFileId;

    /**
     * 前置文件类型：固定为1 投标总结
     */
    @TableField(value = "pre_file_type")
    @ApiModelProperty(value = "前置文件类型：固定为1 投标总结")
    private Integer preFileType;

    /**
     * 云枢执行单位名称
     */
    @TableField(value = "yunshu_execute_unit")
    @ApiModelProperty(value = "云枢执行单位名称")
    private String yunshuExecuteUnit;

    /**
     * 云枢执行单位code
     */
    @TableField(value = "yunshu_execute_unit_code")
    @ApiModelProperty(value = "云枢执行单位code")
    private String yunshuExecuteUnitCode;

    /**
     * 云枢执行单位Id
     */
    @TableField(value = "yunshu_execute_unit_id")
    @ApiModelProperty(value = "云枢执行单位Id")
    private String yunshuExecuteUnitId;

    /**
     * 云枢执行单位id_path
     */
    @TableField(value = "yunshu_execute_unit_id_path")
    @ApiModelProperty(value = "云枢执行单位id_path")
    private String yunshuExecuteUnitIdPath;

    /**
     * 创建时间
     */
    @TableField(value = "create_at", fill = FieldFill.INSERT)
    @ApiModelProperty(value = "创建时间")
    private Long createAt;

    /**
     * 更新时间
     */
    @TableField(value = "update_at", fill = FieldFill.INSERT_UPDATE)
    @ApiModelProperty(value = "更新时间")
    private Long updateAt;

    /**
     * 来源：0：中标未项(市场营销 ); 1: 市场营销(合同管理)
     */
    @TableField(value = "source")
    @ApiModelProperty(value = "来源：0：中标未项(市场营销 ); 1: 市场营销(合同管理)")
    private Integer source;


    /**
     * 工程类型Code（国家标准）
     */
    @TableField(value = "country_project_type_code")
    @ApiModelProperty(value = "工程类型Code（国家标准）")
    private String countryProjectTypeCode;

    /**
     * 工程类型code（总公司市场口径）
     */
    @TableField(value = "market_project_type_code")
    @ApiModelProperty(value = "工程类型code（总公司市场口径）")
    private String marketProjectTypeCode;

    /**
     * 工程类型code（总公司市场口径）2
     */
    @TableField(value = "market_project_type2_code")
    @ApiModelProperty(value = "工程类型code（总公司市场口径）2")
    private String marketProjectType2Code;

    /**
     * 工程类型code(总公司综合口径)
     */
    @TableField(value = "project_type_code")
    @ApiModelProperty(value = "工程类型code(总公司综合口径)")
    private String projectTypeCode;

    /**
     * 工程类型code(总公司综合口径)2
     */
    @TableField(value = "project_type2_code")
    @ApiModelProperty(value = "工程类型code(总公司综合口径)2")
    private String projectType2Code;

    /**
     * 工程类型code(总公司综合口径)3
     */
    @TableField(value = "project_type3_code")
    @ApiModelProperty(value = "工程类型code(总公司综合口径)3")
    private String projectType3Code;

    /**
     * 工程类型code(总公司综合口径)4
     */
    @TableField(value = "project_type4_code")
    @ApiModelProperty(value = "工程类型code(总公司综合口径)4")
    private String projectType4Code;

    /**
     * 工程承包模式Code
     */
    @TableField(value = "contract_mode1_code")
    @ApiModelProperty(value = "工程承包模式Code")
    private String contractMode1Code;

    /**
     * 施工承包模式Code
     */
    @TableField(value = "contract_mode2_code")
    @ApiModelProperty(value = "施工承包模式Code")
    private String contractMode2Code;

    /**
     * 投资主体Code
     */
    @TableField(value = "investors_code")
    @ApiModelProperty(value = "投资主体Code")
    private String investorsCode;

    /**
     * 业务类型Code
     */
    @TableField(value = "business_type_code")
    @ApiModelProperty(value = "业务类型Code")
    private String businessTypeCode;

    /**
     * 客户级别Code
     */
    @TableField(value = "customer_level_code")
    @ApiModelProperty(value = "客户级别Code")
    private String customerLevelCode;

    /**
     * 客户企业性质Code
     */
    @TableField(value = "enterprise_type_code")
    @ApiModelProperty(value = "客户企业性质Code")
    private String enterpriseTypeCode;

    /**
     * 进度款付款方式Code
     */
    @TableField(value = "advances_way_code")
    @ApiModelProperty(value = "进度款付款方式Code")
    private String advancesWayCode;

    /**
     * 保修金支付方式Code
     */
    @TableField(value = "reward_punish_type_code")
    @ApiModelProperty(value = "质量奖罚类型Code")
    private String rewardPunishTypeCode;

    /**
     * 客户id
     */
    @TableField(value = "customer_id")
    @ApiModelProperty(value = "客户id")
    private String customerId;

    /**
     * 客户编号
     */
    @ApiModelProperty(value = "客户编号")
    private String customerCode;

    /**
     * 客户母公司id
     */
    @ApiModelProperty(value = "客户母公司id")
    private String superiorCompanyId;

    /**
     * 统一社会信用代码
     */
    @ApiModelProperty(value = "统一社会信用代码")
    private String businessLicenseCode;

    @ApiModelProperty(value = "是否战新")
    @TableField(value = "if_strategic_new_business")
    private String ifStrategicNewBusiness;

    @ApiModelProperty(value = "战新业务一级分类")
    @TableField(value = "strategic_new_business_type")
    private String strategicNewBusinessType;

    @ApiModelProperty(value = "战新业务二级分类")
    @TableField(value = "strategic_new_business_type2")
    private String strategicNewBusinessType2;

    @ApiModelProperty(value = "战新业务三级分类")
    @TableField(value = "strategic_new_business_type3")
    private String strategicNewBusinessType3;

    @ApiModelProperty(value = "战新业务一级分类code")
    @TableField(value = "strategic_new_business_type_code")
    private String strategicNewBusinessTypeCode;

    @ApiModelProperty(value = "战新业务二级分类code")
    @TableField(value = "strategic_new_business_type2_code")
    private String strategicNewBusinessType2Code;

    @ApiModelProperty(value = "战新业务三级分类code")
    @TableField(value = "strategic_new_business_type3_code")
    private String strategicNewBusinessType3Code;

}