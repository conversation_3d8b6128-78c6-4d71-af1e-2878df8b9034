package com.cscec3b.iti.projectmanagement.server.service;

import com.cscec3b.iti.common.base.page.Page;
import com.cscec3b.iti.projectmanagement.api.dto.request.project.BureauNominalPageParams;
import com.cscec3b.iti.projectmanagement.api.dto.response.BureauNamedProjectRelationship;
import com.cscec3b.iti.projectmanagement.api.dto.response.project.BureauNominalProjectPageResp;
import com.cscec3b.iti.projectmanagement.api.dto.response.project.ProjectDetailResp;

/**
 * <AUTHOR>
 * @date 2023/07/31 10:11
 **/

public interface IBureauNominalProjectService {

    /**
     * 查询局名义总包与分包项目关系
     *
     * @param type        局名义项目类型 1：总包； 2：分包
     * @param projectId   项目id
     * @param financeCode 财商id
     * @return boolean
     * <AUTHOR>
     * @date 2023/07/31 10:27
     */
    BureauNamedProjectRelationship getProjectRelationShip(int type, Long projectId, String financeCode);

    /**
     * 局名义项目与非局名义项目转换<br>
     * 局名义项目转换为非局名义项目时要求下面不能为分包项目
     *
     * @param projectId 项目id
     * @return boolean 结果
     * <AUTHOR>
     * @date 2023/07/31 10:31
     */
    BureauNamedProjectRelationship changeProjectRelation(Long projectId);

    /**
     * 通过财商id查询所有财商已立项的非局名义项目
     *
     * @param financeCode 财商code
     * @return com.cscec3b.iti.projectmanagement.api.dto.response.project.ProjectDetailResp 项目详情
     * <AUTHOR>
     * @date 2023/07/31 10:37
     */
    ProjectDetailResp getByFinanceCode(String financeCode);

    /**
     * 添加非局名义项目到总包项目下
     *
     * @param generalContractorId 总包项目id
     * @param projectId           要添加的非局名义项目id
     * @return java.lang.Boolean
     * <AUTHOR>
     * @date 2023/07/31 10:38
     */
    Boolean addProjectToGeneralContractorProject(Long generalContractorId, Long projectId);

    /**
     * 从总包项目中移除分包项目
     *
     * @param subcontractingId 分包项目id
     * @return java.lang.Boolean 结果
     * <AUTHOR>
     * @date 2023/07/31 10:40
     */
    Boolean removeSubcontractingProjectId(Long subcontractingId);
    
    /**
     * 局名义项目列表查询
     *
     * @param queryParams 查询信息
     * @return {@link Page }<{@link BureauNominalProjectPageResp }>
     * <AUTHOR>
     * @date 2023/08/21
     */
    Page<BureauNominalProjectPageResp> bureauNominalProjectPage(BureauNominalPageParams queryParams);


    /**
     * 局名义项目列表查询-切换云枢组织
     *
     * @param queryParams 查询参数
     * @return BureauNominalProjectPageResp
     * <AUTHOR>
     * @Date 2023/8/24
     */
    Page<BureauNominalProjectPageResp> bureauNominalCloudPivotPage(BureauNominalPageParams queryParams);
}
