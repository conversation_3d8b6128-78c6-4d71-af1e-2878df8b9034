package com.cscec3b.iti.projectmanagement.server.pushservice.event;

import com.cscec3b.iti.projectmanagement.server.entity.Project;
import com.cscec3b.iti.projectmanagement.server.entity.ProjectEventPushRecord;
import com.cscec3b.iti.projectmanagement.server.entity.dto.ProjectFlowEventSubscribeDto;
import com.cscec3b.iti.projectmanagement.server.pushservice.enums.FlowNodeDataTypeEnum;
import com.cscec3b.iti.projectmanagement.server.pushservice.enums.FlowNodeEnum;
import com.cscec3b.iti.projectmanagement.server.pushservice.enums.FlowNodeHandlerEnum;
import lombok.Data;
import lombok.Getter;
import lombok.Setter;
import org.springframework.context.ApplicationEvent;

import java.time.Instant;

/**
 * 项目生命周期流转事件
 *
 * <AUTHOR>
 * @date 2023/09/15 16:59
 **/

@Data
public class ProjectFlowEvent  {



	/**
	 * 消息id
	 */
	private String msgId;

	/**
	 * 消费者信息
	 */
	private ProjectFlowEventSubscribeDto subscribeDto;

	/**
	 * 项目信息
	 */
	private Project project;

	/**
	 * 日志信息
	 */
	private ProjectEventPushRecord pushRecord;

	public ProjectFlowEvent() {
	}

	public ProjectFlowEvent(String msgId, ProjectFlowEventSubscribeDto subscribeDto, Project project,
			ProjectEventPushRecord pushRecord) {
		this.msgId = msgId;
		this.subscribeDto = subscribeDto;
		this.project = project;
		this.pushRecord = pushRecord;
	}
}
