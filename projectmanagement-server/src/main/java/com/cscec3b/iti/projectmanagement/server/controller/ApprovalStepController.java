package com.cscec3b.iti.projectmanagement.server.controller;

import com.cscec3b.iti.common.base.api.GenericityResponse;
import com.cscec3b.iti.common.base.api.ResponseBuilder;
import com.cscec3b.iti.common.base.page.Page;
import com.cscec3b.iti.projectmanagement.api.IApprovalStepApi;
import com.cscec3b.iti.projectmanagement.api.bidapproval.dto.response.BidApprovalDetailResp;
import com.cscec3b.iti.projectmanagement.api.dto.request.approvalstep.ApprovalStepReq;
import com.cscec3b.iti.projectmanagement.api.dto.request.approvalstep.ContractFileUpdateParamReq;
import com.cscec3b.iti.projectmanagement.api.dto.request.project.ProjectOpenHookQueryReq;
import com.cscec3b.iti.projectmanagement.api.dto.response.ContractFileDetailResp;
import com.cscec3b.iti.projectmanagement.api.dto.response.approvalstep.BusinessSystemDataPushInfoResp;
import com.cscec3b.iti.projectmanagement.api.dto.response.approvalstep.MainDataPushInfoResp;
import com.cscec3b.iti.projectmanagement.api.dto.response.project.ProjectOpenHookQueryResp;
import com.cscec3b.iti.projectmanagement.server.bidapprovalservice.IBidApprovalService;
import com.cscec3b.iti.projectmanagement.server.projectapproval.ApprovalStep;
import com.cscec3b.iti.projectmanagement.server.projectapproval.StepServiceFactory;
import com.cscec3b.iti.projectmanagement.server.service.ProjectService;
import io.swagger.annotations.Api;
import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RestController;

import java.util.List;
import java.util.Objects;

@Slf4j
@RestController
@RequestMapping(IApprovalStepApi.PATH)
@Api(tags = "项目立项进度管理")
@RequiredArgsConstructor
public class ApprovalStepController implements IApprovalStepApi {

    /**
     * 中标未立项服务
     */
    private final IBidApprovalService bidApprovalService;

    /**
     * 项目服务类
     */
    private final ProjectService projectService;

    private final StepServiceFactory stepServiceFactory;


    @Override
    public GenericityResponse<ContractFileDetailResp<?>> getFileInfo(Long id) {
        return ResponseBuilder.fromData(bidApprovalService.getFileDetailByBelongId(id));
    }

    @Override
    public GenericityResponse<Boolean> updateFileByBidId(Long id, ContractFileUpdateParamReq updateParams) {
        return ResponseBuilder.fromData(bidApprovalService.updateFileByBidId(id, updateParams));
    }

//    @Override
//    public GenericityResponse<Boolean> submit(ApprovalStepReq approvalStepReq) {
//        final Integer stepNo = approvalStepReq.getStepNo();
//        final String serviceName = ApprovalStepEnum.getByNo(stepNo).getServiceName();
//        final IApprovalStepService stepService = (IApprovalStepService) SpringUtils.getBean(serviceName);
//        if (Objects.isNull(stepService)) {
//            log.error("未找到对应步骤{}的服务", stepNo);
//            return ResponseBuilder.fromData(null);
//        }
//        return ResponseBuilder.fromData(stepService.saveCurrentStep(approvalStepReq));
//    }

    @Override
    public GenericityResponse<Boolean> submitV2(ApprovalStepReq approvalStepReq) {
        return ResponseBuilder.fromData(bidApprovalService.submitV2(approvalStepReq));
    }

    @Override
    public GenericityResponse<Boolean> saveDrafts(ApprovalStepReq approvalStepReq) {
        final Integer stepNo = approvalStepReq.getStepNo();
        ApprovalStep approvalStep = stepServiceFactory.stepMap.get(stepNo);
        if (Objects.isNull(approvalStep)) {
            log.error("未找到对应步骤{}的服务", stepNo);
            return ResponseBuilder.fromData(null);
        }
        approvalStep.saveDrafts(approvalStepReq);
        return ResponseBuilder.fromData(Boolean.TRUE);
    }

    @Override
    public GenericityResponse<BidApprovalDetailResp> getBidApprovalDetail(Long bidApprovalId) {
        return ResponseBuilder.fromData(bidApprovalService.getBidApprovalDetail(bidApprovalId));
    }

    @Override
    public GenericityResponse<Page<ProjectOpenHookQueryResp>> hookProjectPageList(ProjectOpenHookQueryReq hookReqReq) {
        return ResponseBuilder.fromData(projectService.hookProject(hookReqReq));
    }

    @Override
    public GenericityResponse<MainDataPushInfoResp> getMainDataSync(Integer stepNo, Long bidId) {
        return ResponseBuilder.fromData(bidApprovalService.mainDataPushInfo(bidId));
    }

    @Override
    public GenericityResponse<List<BusinessSystemDataPushInfoResp>> getBusinessDataSync(
            ApprovalStepReq approvalStepReq) {
        return ResponseBuilder.fromData(
                bidApprovalService.businessSystemDataPushInfo(approvalStepReq.getBidApprovalId()));
    }

    @Override
    public GenericityResponse<List<Integer>> bidStepInit(Long bidId) {
        return ResponseBuilder.fromData(bidApprovalService.bidStepInit(bidId));
    }
    

    @Override
    public GenericityResponse<List<Integer>> startApproval(Long bidId) {
        return ResponseBuilder.fromData(bidApprovalService.startProjectApproval(bidId));
    }
}
