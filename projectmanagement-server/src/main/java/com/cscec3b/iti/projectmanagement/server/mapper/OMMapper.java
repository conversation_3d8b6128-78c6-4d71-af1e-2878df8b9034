package com.cscec3b.iti.projectmanagement.server.mapper;

import com.cscec3b.iti.projectmanagement.api.dto.response.OMInitProjectResp;
import com.cscec3b.iti.projectmanagement.server.entity.Project;
import org.apache.ibatis.annotations.Mapper;
import org.apache.ibatis.annotations.Param;

@Mapper
public interface OMMapper {

    /**
     * 初始化项目
     *
     * @param project 项目
     * @return {@link Long}
     */
    Long initProject(@Param("project") Project project);

    /**
     * 通过id获取项目
     *
     * @param id id
     * @return {@link OMInitProjectResp}
     */
    OMInitProjectResp getInitProjectById(@Param("id") Long id);

    /**
     * 更新初始化项目
     *
     * @param project 项目
     * @return int
     */
    int updateInitProject(@Param("project") Project project);

    /**
     * 插入项目进展
     *
     * @param projectId 项目id
     * @return int
     */
    int insertIgnoreProjectProgress(@Param("projectId") Long projectId);

    /**
     * 批量添加项目部门id路径
     */
    void batchAddProjectDeptIdPath();
}
