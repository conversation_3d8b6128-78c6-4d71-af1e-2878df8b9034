package com.cscec3b.iti.projectmanagement.server.scheduled;

import com.baomidou.mybatisplus.core.toolkit.Wrappers;
import com.cscec3b.iti.common.redis.lock.annotation.Lock;
import com.cscec3b.iti.projectmanagement.api.dto.request.ContractIdPresentationIdMappingReq;
import com.cscec3b.iti.projectmanagement.api.dto.response.ContractIdPresentationIdMappingResp;
import com.cscec3b.iti.projectmanagement.server.bidapprovalservice.IBidSupplementaryAgreementService;
import com.cscec3b.iti.projectmanagement.server.entity.SupplementaryAgreement;
import com.cscec3b.iti.projectmanagement.server.enums.IndContractsTypeEnum;
import com.cscec3b.iti.projectmanagement.server.feign.MarketFeignService;
import com.cscec3b.iti.projectmanagement.server.feign.MarketFeignService.MarketApiResult;
import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.collections4.CollectionUtils;
import org.apache.commons.lang3.ObjectUtils;
import org.springframework.scheduling.annotation.Scheduled;
import org.springframework.stereotype.Component;

import javax.annotation.Resource;
import java.util.List;
import java.util.Map;
import java.util.stream.Collectors;

/**
 * 市场营销定时更新文件信息
 *
 * <AUTHOR>
 * @date 2024/02/22
 */

@Slf4j
@RequiredArgsConstructor
@Component
public class MarketUpdateFileScheduled {

    @Resource
    private IBidSupplementaryAgreementService bidSupplementaryAgreementService;

    @Resource
    private MarketFeignService marketFeignService;


    /**
     * 每天 10:00, 14:00, 16:00 更新文件信息
     */
    @Scheduled(cron = "0 0 10,14,16 * * ?")
    @Lock
    public void updateFileScheduled() {
        log.info("市场营销定时更新文件信息开始");
        final List<SupplementaryAgreement> list =
                bidSupplementaryAgreementService.list(Wrappers.<SupplementaryAgreement>lambdaQuery()
                        .eq(SupplementaryAgreement::getBelongFileType, IndContractsTypeEnum.AGREEMENT.getDictCode())
                        .isNotNull(SupplementaryAgreement::getEvaluationId).isNull(SupplementaryAgreement::getPreFileId));
        if (CollectionUtils.isNotEmpty(list)) {
            final List<Long> evaluationIds =
                    list.stream().map(SupplementaryAgreement::getEvaluationId).distinct().collect(Collectors.toList());

            final MarketApiResult<List<ContractIdPresentationIdMappingResp>> apiResult =
                    marketFeignService.getContractIdPresentationIdMapping(new ContractIdPresentationIdMappingReq(evaluationIds));
            if (apiResult != null && apiResult.getResultCode().equals("0")) {
                final List<ContractIdPresentationIdMappingResp> data = apiResult.getData();
                if (CollectionUtils.isNotEmpty(data)) {
                    final Map<Long, Long> mappingMap = data.stream()
                            .filter(d -> ObjectUtils.allNotNull(d.getContractId(), d.getContractPresentationId()))
                            .collect(Collectors.toMap(ContractIdPresentationIdMappingResp::getContractId
                                    , ContractIdPresentationIdMappingResp::getContractPresentationId));

                    list.forEach(supplementaryAgreement -> {
                        final Long evaluationId = supplementaryAgreement.getEvaluationId();
                        if (mappingMap.containsKey(evaluationId)) {
                            supplementaryAgreement.setPreFileId(mappingMap.get(evaluationId))
                                    .setPreFileType(IndContractsTypeEnum.AGREEMENT_PRESENTATION.getDictCode());
                        }
                    });
                    bidSupplementaryAgreementService.updateBatchById(list);
                }
            }
        }
        log.info("市场营销定时更新文件信息结束, 本次共更新{}条", list.size());
    }
}
