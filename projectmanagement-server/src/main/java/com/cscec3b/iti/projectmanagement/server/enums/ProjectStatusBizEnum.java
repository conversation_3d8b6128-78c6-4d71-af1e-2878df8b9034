package com.cscec3b.iti.projectmanagement.server.enums;

import lombok.Getter;

import java.util.*;

/**
 * <AUTHOR>
 * @description ProjectStatusBizEnum 施工项目状态(商务)
 * @date 2023/04/3 12:58
 */
@Getter
public enum ProjectStatusBizEnum {
    /**
     * 已结
     */
    SETTLE_ACCOUNT("06", "已结", "settle_account"),

    /**
     * 未结
     */
    UNSETTLED_ACCOUNT("05", "未结", "unsettled_account");


    ProjectStatusBizEnum(String dictCode, String zhCN, String enUS) {
        this.dictCode = dictCode;
        this.zhCN = zhCN;
        this.enUS = enUS;
    }

    final String dictCode;

    final String zhCN;

    final String enUS;

    public String getDictCode() {
        return dictCode;
    }

    public String getZhCN() {
        return zhCN;
    }

    public String getEnUS() {
        return enUS;
    }

    /**
     * getEnumCode
     *
     * @param str str
     * @return {@link ProjectStatusBizEnum}
     */
    public static ProjectStatusBizEnum getEnumCode(String str) {
        ProjectStatusBizEnum[] values = ProjectStatusBizEnum.values();
        for (ProjectStatusBizEnum value : values) {
            if (value.getEnUS().equals(str) || value.getZhCN().equals(str)) {
                return value;
            }
        }
        return null;
    }

    public static ProjectStatusBizEnum getZhCNByCode(String str) {
        return Arrays.stream(ProjectStatusBizEnum.values()).filter(value -> value.getDictCode().equals(str)).findFirst().orElse(null);
    }

    public static List<Map<String, Object>> getAllEnumMap() {
        List<Map<String, Object>> list = new ArrayList<>();
        ProjectStatusBizEnum[] values = ProjectStatusBizEnum.values();
        for (ProjectStatusBizEnum value : values) {
            Map<String, Object> map = new HashMap<>();
            map.put("dictCode", value.getDictCode());
            map.put("zhCN", value.getZhCN());
            map.put("enUS", value.getEnUS());
            list.add(map);
        }
        return list;
    }
}
