package com.cscec3b.iti.projectmanagement.server.converter.excelConverter;

import com.alibaba.excel.converters.Converter;
import com.alibaba.excel.metadata.GlobalConfiguration;
import com.alibaba.excel.metadata.data.WriteCellData;
import com.alibaba.excel.metadata.property.ExcelContentProperty;
import lombok.extern.slf4j.Slf4j;

import java.util.Objects;

import static com.cscec3b.iti.projectmanagement.server.enums.TrueFalseEnum.FALSE;
import static com.cscec3b.iti.projectmanagement.server.enums.TrueFalseEnum.TRUE;

/**
 * <AUTHOR>
 * @Description: 自定义转换器，将枚举类型转换为字符串
 * @date 2024/07/01
 */
@Slf4j
public class TrueFalseConverter implements Converter<String> {

    @Override
    public WriteCellData<?> convertToExcelData(String value, ExcelContentProperty contentProperty,
            GlobalConfiguration globalConfiguration) throws Exception {
        log.info("trueFalseConverter convertToExcelData value:  {}, filedName: {}", value,
                contentProperty.getField().getName());
        if (Objects.nonNull(value)) {
            return new WriteCellData<>(Objects.equals(TRUE.getCode().toString(), value) ? TRUE.getName() :
                    FALSE.getName());
        }
        return null;
    }
}
