package com.cscec3b.iti.projectmanagement.server.service;

import com.cscec3b.iti.common.base.page.Page;
import com.cscec3b.iti.projectmanagement.api.dto.request.civilmilitary.CivilMilitaryIntegrationReq;
import com.cscec3b.iti.projectmanagement.api.dto.request.civilmilitary.QueryCivilMilitaryIntegrationParams;
import com.cscec3b.iti.projectmanagement.api.dto.request.civilmilitary.UpdateCivilMilitaryIntegrationReq;
import com.cscec3b.iti.projectmanagement.api.dto.response.secrecy.SecrecyProjectDetailResp;
import com.cscec3b.iti.projectmanagement.api.dto.response.secreycivilmilitary.CivilMilitaryIntegrationDetailResp;
import com.cscec3b.iti.projectmanagement.api.dto.response.secreycivilmilitary.CivilMilitaryIntegrationResp;
import com.cscec3b.iti.projectmanagement.server.entity.CivilMilitaryIntegration;
import com.baomidou.mybatisplus.extension.service.IService;

import java.util.List;

/**
 * 军民融合服务类
 *
 * <AUTHOR>
 * @date 2024/04/12
 */
public interface CivilMilitaryIntegrationService extends IService<CivilMilitaryIntegration>{


    /**
     * 新建
     *
     * @param civilMilitaryIntegrationReq 军民一体化请求
     * @return {@link Long}
     */
    Long create(CivilMilitaryIntegrationReq civilMilitaryIntegrationReq);

    /**
     * 更新
     *
     * @param updateCivilMilitaryIntegrationReq 更新军民融合请求
     * @return Boolean boolean
     * @description 编辑军民项目
     * @date 2023/02/14 15:29
     * <AUTHOR>
     */
    Boolean updateCivilMilitaryProject(UpdateCivilMilitaryIntegrationReq updateCivilMilitaryIntegrationReq);

    /**
     * 获取详细信息
     *
     * @param id 项目id
     * @return {@link SecrecyProjectDetailResp}
     * @description 查询保密项目详情
     * @date 2023/02/14 15:36
     * <AUTHOR>
     */
    CivilMilitaryIntegrationDetailResp getDetail(Long id);

    /**
     * 保密项目列表
     *
     * @param params 查询参数
     * @return Page<SpecialProjectResp>
     */
    Page<CivilMilitaryIntegrationResp> pageList(QueryCivilMilitaryIntegrationParams params);

    /**
     * 删除军民融合项目
     *
     * @param ids id列表
     * @return boolean
     */
    boolean deleteCivilMilitaryProject(List<Long> ids);

}
