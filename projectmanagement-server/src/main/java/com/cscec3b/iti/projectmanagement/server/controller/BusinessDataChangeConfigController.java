package com.cscec3b.iti.projectmanagement.server.controller;

import com.cscec3b.iti.common.base.api.GenericityResponse;
import com.cscec3b.iti.common.base.api.ResponseBuilder;
import com.cscec3b.iti.common.base.page.Page;
import com.cscec3b.iti.projectmanagement.api.IBusSysDataChangeConfigApi;
import com.cscec3b.iti.projectmanagement.api.dto.request.BusSysDataChangeConfigEditReq;
import com.cscec3b.iti.projectmanagement.api.dto.request.BusSysDataChangeConfigPageReq;
import com.cscec3b.iti.projectmanagement.api.dto.request.BusSysDataChangeConfigReq;
import com.cscec3b.iti.projectmanagement.api.dto.response.BusSysDataChangeConfigResp;
import com.cscec3b.iti.projectmanagement.api.dto.response.BusSysDataListResp;
import com.cscec3b.iti.projectmanagement.server.service.BusinessSystemDataChangeApprovalConfigService;
import io.swagger.annotations.Api;
import lombok.extern.slf4j.Slf4j;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RestController;

import javax.annotation.Resource;
import java.util.List;

@Slf4j
@RestController
@Api(tags = "业务数据变更配置")
@RequestMapping(IBusSysDataChangeConfigApi.PATH)
public class BusinessDataChangeConfigController implements IBusSysDataChangeConfigApi {

    @Resource
    private BusinessSystemDataChangeApprovalConfigService businessDataChangeConfigService;

    /**
     * 添加业务数据变更配置
     *
     * @param configReq {@link BusSysDataChangeConfigReq}
     * @return {@link GenericityResponse}<{@link Boolean}>
     */
    @Override
    public GenericityResponse<Boolean> addDataChangeConfig(final BusSysDataChangeConfigReq configReq) {
        return ResponseBuilder.fromData(businessDataChangeConfigService.addDataChangeConfig(configReq));
    }

    /**
     * 分页列表查询
     *
     * @param configReq 查询参数
     * @return {@link GenericityResponse}<{@link Page}<{@link BusSysDataChangeConfigResp}>>
     */
    @Override
    public GenericityResponse<Page<BusSysDataChangeConfigResp>> listPage(
            final BusSysDataChangeConfigPageReq configReq) {
        return ResponseBuilder.fromData(businessDataChangeConfigService.listPage(configReq));
    }

    /**
     * 编辑
     *
     * @param editReq 配置信息
     * @return {@link GenericityResponse}<{@link Boolean}>
     */
    @Override
    public GenericityResponse<Boolean> editDataChangeConfig(final BusSysDataChangeConfigEditReq editReq) {
        return ResponseBuilder.fromData(businessDataChangeConfigService.editDataChangeConfig(editReq));
    }


    /**
     * 获取业务板块及字段信息
     *
     * @return {@link GenericityResponse}<{@link List}<{@link BusSysDataListResp}>>
     */
    @Override
    public GenericityResponse<List<BusSysDataListResp>> getBusinessSystemData() {
        return ResponseBuilder.fromData(businessDataChangeConfigService.listBusSysData());
    }

    /**
     * 删除
     *
     * @param id 配置id(主键)
     * @return {@link GenericityResponse}<{@link Boolean}>
     */
    @Override
    public GenericityResponse<Boolean> deleteDataChangeConfig(final Long id) {
        return ResponseBuilder.fromData(businessDataChangeConfigService.deleteDataChangeConfig(id));
    }


}
