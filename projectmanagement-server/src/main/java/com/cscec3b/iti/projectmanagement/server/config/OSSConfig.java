package com.cscec3b.iti.projectmanagement.server.config;

import org.springframework.boot.autoconfigure.condition.ConditionalOnBean;
import org.springframework.context.annotation.Bean;
import org.springframework.context.annotation.Configuration;
import org.springframework.context.annotation.Import;

import com.aliyun.oss.ClientBuilderConfiguration;
import com.aliyun.oss.OSS;
import com.aliyun.oss.OSSClientBuilder;
import com.cscec3b.iti.common.web.exception.BusinessException;

import cn.hutool.core.lang.Snowflake;
import cn.hutool.core.util.IdUtil;

/**
 * <AUTHOR>
 * @date 2022-12-2022/12/30 10:49
 */
@Import(OSSProperties.class)
@ConditionalOnBean(OSSProperties.class)
@Configuration
public class OSSConfig {


    /**
     * oss客户端
     *
     * @param properties 属性
     * @return {@link OSS}
     */
    @Bean("ossClient")
    public OSS ossClient(OSSProperties properties) {
        ClientBuilderConfiguration conf = new ClientBuilderConfiguration();
        // 设置OSSClient允许打开的最大HTTP连接数，默认为1024个。
        conf.setMaxConnections(200);
        // 设置Socket层传输数据的超时时间，默认为50000毫秒。
        conf.setSocketTimeout(30000);
        // 设置建立连接的超时时间，默认为50000毫秒。
        conf.setConnectionTimeout(30000);
        // 设置从连接池中获取连接的超时时间（单位：毫秒），默认不超时。
        conf.setConnectionRequestTimeout(1000);
        // 设置连接空闲超时时间。超时则关闭连接，默认为60000毫秒。
        conf.setIdleConnectionTime(50000);
        // 设置失败请求重试次数，默认为3次。
        conf.setMaxErrorRetry(1);
        // 设置是否支持将自定义域名作为Endpoint，默认支持。
        conf.setSupportCname(true);

        OSS ossClient = new OSSClientBuilder().build(properties.getEndpoint(), properties.getAccessKeyId(), properties.getSecretKey());

        //检查桶配置
        if (!ossClient.doesBucketExist(properties.getBucketName())) {
            throw new BusinessException(8010301);
        }

        return ossClient;
    }

    /**
     * 雪花
     *
     * @return {@link Snowflake}
     */
    @Bean
    public Snowflake snowflake() {
        return IdUtil.getSnowflake(1, 1);
    }
}
