package com.cscec3b.iti.projectmanagement.server.pushservice.enums;

import com.cscec3b.iti.common.web.exception.FrameworkException;
import lombok.Getter;

import java.util.Arrays;
import java.util.List;

import static java.util.Arrays.asList;
import static java.util.Collections.singletonList;

/**
 * 项目事件节点枚举<br>
 * 枚举内的code 与{@Link com.cscec3b.iti.model.resp.ProjectArchiveResp}中的属性名保持一致
 *
 *
 * <AUTHOR>
 * @date 2023/09/14 19:31
 **/

@Getter
public enum FlowNodeEnum {

    /**
     *市场营销板块
     */
    MARKETING_SEGMENT("市场营销立项发起", "marketing_segment", "marketingSegment", "市场营销节点", singletonList(FlowNodeHandlerEnum.POST)),
    ORG_REPORT_FINANCE_INTEGRATION("组织填报与财商一体化", "org_report_finance_integration", "marketingSegment", "组织填报与财商一体化",
            singletonList(FlowNodeHandlerEnum.POST)),

    /**
     * 财商板块
     */
    FINANCE_SEGMENT("财商立项完成", "finance_segment", "financeSegment", "财商立项节点", asList(FlowNodeHandlerEnum.PRE, FlowNodeHandlerEnum.POST)),
    /**
     * 财商板块
     */
    FINANCE_SEGMENT_UPDATE("财商信息更新", "finance_segment_update", "financeSegment", "财商立项节点", singletonList(FlowNodeHandlerEnum.POST)),

    /**
     * 智慧工地板块
     */
    SMART_SITE_SEGMENT("智慧工地立项完成", "smart_site_segment","smartSiteSegment", "智慧工地立项节点",
        asList(FlowNodeHandlerEnum.PRE, FlowNodeHandlerEnum.POST)),
    /**
     * 智慧工地更新
     */
    SMART_SITE_SEGMENT_UPDATE("智慧工地更新", "smart_site_segment_update","smartSiteSegment", "智慧工地立项节点",
            singletonList(FlowNodeHandlerEnum.POST)),

    /**
     * 项目中心信息板块
     */
    CPM_SEGMENT("基本信息更新", "cpm_segment", "cpmSegment","项目中心基本信息节点", singletonList(FlowNodeHandlerEnum.POST)),

    /**
     * 标准立项(财务和工地立项完成)
     */
    FINANCE_SMART_SITE_APPROVAL("标准立项完成", "finance_smart_site_approval", "financeSmartSiteApproval", "财商和工地立项完成节点", singletonList(FlowNodeHandlerEnum.POST)),
    /**
     * 挂接项目立项完成 /
     */
    HOOK_PROJECT_APPROVAL("挂接项目立项完成", "hook_project_approval", "hookProjectApproval", "挂接项目立项完成节点",
        singletonList(FlowNodeHandlerEnum.POST)),

    /**
     *特殊立项发起
     */
    SPECIAL_PROJECT("特殊立项发起", "special_project", "specialProject", "特殊立项节点", singletonList(FlowNodeHandlerEnum.POST)),

    /**
     *供应链信息更新
     */
    SUPPLY_SEGMENT("供应链信息更新", "supply_segment", "supplySegment", "供应链节点（云筑网编码）",
            singletonList(FlowNodeHandlerEnum.POST)),

    /**
     *
     */
    FINANCE_PROJECT("财商项目立项", "finance_project", "marketingSegment", "财商立项项目，只推送给财商的项目",
            singletonList(FlowNodeHandlerEnum.POST)),

    SMART_SITE_PROJECT("智慧工地立项项目", "smart_site_project", "marketingSegment", "智慧工地立项项目,只推送给工地的项目",
            singletonList(FlowNodeHandlerEnum.POST)),


    /**
     * 工程项目立项完成
     */
    ENGINE_PROJECT_APPROVAL("工程项目立项完成", "engine_project_approval", "projectTransferSegment", "工程项目立项完成节点", singletonList(FlowNodeHandlerEnum.POST)),
    /**
     * 工程项目绑定施工项目
     */
    ENGINE_BINDING_STANDARD_PROJECT("工程项目绑定施工项目", "engine_binding_standard_project", "projectTransferSegment", "工程项目绑定施工项目节点", singletonList(FlowNodeHandlerEnum.POST)),

    /**
     * 工程项目解绑施工项目
     */
    ENGINE_UNBINDING_STANDARD_PROJECT("工程项目解绑施工项目", "engine_unbinding_standard_project", "projectTransferSegment", "工程项目解绑施工项目节点", singletonList(FlowNodeHandlerEnum.POST)),

    /**
     * 施工项目换绑工程项目
     */
    ENGINE_TRANSFER_STANDARD_PROJECT("施工项目换绑工程项目", "engine_transfer_standard_project", "projectTransferSegment", "施工项目换绑工程项目节点", singletonList(FlowNodeHandlerEnum.POST));

    /**
     * 节点名称
     */
    final String name;

    /**
     * 编码
     */
    final String code;

    /**
     * 字段名
     */
    final String filed;

    /**
     * 描述
     */
    final String description;

    /**
     * 可配置的handler
     */
    final List<FlowNodeHandlerEnum> handlers;

    FlowNodeEnum(String name, String code, String filed, String description, List<FlowNodeHandlerEnum> handlers) {
        this.name = name;
        this.code = code;
        this.filed = filed;
        this.description = description;
        this.handlers = handlers;
    }
    public static String getName(String code){
        for (FlowNodeEnum value : FlowNodeEnum.values()) {
            if (value.getCode().equals(code)){
                return value.getName();
            }
        }
        return "";
    }

    public static FlowNodeEnum getByCode(String code) {
        return Arrays.stream(values()).filter(dataType -> dataType.getCode().equals(code)).findFirst()
                .orElseThrow(() -> new FrameworkException(-1, "节点类型不正确"));
    }
}
