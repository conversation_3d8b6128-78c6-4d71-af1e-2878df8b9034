package com.cscec3b.iti.projectmanagement.server.mapper;

import com.baomidou.mybatisplus.core.mapper.BaseMapper;
import com.cscec3b.iti.projectmanagement.api.dto.request.QueryYunshuStandOrgReq;
import com.cscec3b.iti.projectmanagement.server.entity.YunshuOrgSync;
import com.cscec3b.iti.projectmanagement.server.entity.YunshuStandOrgMapping;
import org.apache.ibatis.annotations.Mapper;
import org.apache.ibatis.annotations.Param;

import java.util.List;
import java.util.Set;

@Mapper
public interface YunshuStandOrgMappingMapper extends BaseMapper<YunshuOrgSync> {
    /**
     * insert record to table
     *
     * @param record the record
     * @return insert count
     */
    int insert(YunshuStandOrgMapping record);

    /**
     * select by primary key
     *
     * @param id primary key
     * @return object by primary key
     */
    YunshuStandOrgMapping selectByPrimaryKey(Long id);

    /**
     * update record selective
     *
     * @param record the updated record
     * @return update count
     */
    int updateByPrimaryKeySelective(YunshuStandOrgMapping record);

    int updateBatchSelective(List<YunshuStandOrgMapping> list);

    int batchInsert(@Param("list") List<YunshuStandOrgMapping> list);

    int getCountByStandOrgIdAndYunshuId(@Param("standUnitId") String standUnitId,
        @Param("yunshuExecuteUnitId") String yunshuExecuteUnitId, @Param("id") Long id);

    List<YunshuStandOrgMapping> pageList(QueryYunshuStandOrgReq req);

    int delete(Set<Long> ids);

    YunshuStandOrgMapping selectByYunshuUnitId(@Param("yunshuUnitId") String yunshuUnitId);

    YunshuStandOrgMapping selectByStandOrgCode(@Param("standOrgCode") String standOrgCode);
}
