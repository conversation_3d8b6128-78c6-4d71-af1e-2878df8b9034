package com.cscec3b.iti.projectmanagement.server.config;

import com.cscec3b.iti.projectmanagement.api.dto.dto.xindun.UserInfo;
import com.cscec3b.iti.projectmanagement.api.dto.dto.xindun.YunshuOrgSyncResp;

import java.util.Optional;

/**
 * @Description: 上下文对象类
 * @author: xinfa
 * @date: 2022.08.18
 */
public class UserContextHolder {

    private UserContextHolder() {
    }


    /**
     * 共享用户线程变量
     */
    public static final InheritableThreadLocal<UserInfo> NEW_USER_CONTEXT = new InheritableThreadLocal<>();

    /**
     * 设置user
     *
     * @param userInfo userInfo
     * <AUTHOR>
     * @date 2023/10/23
     */
    public static void setUser(UserInfo userInfo) {
        NEW_USER_CONTEXT.set(userInfo);
    }

    /**
     * 获取user
     *
     * @return {@link UserInfo }
     * <AUTHOR>
     * @date 2023/10/23
     */
    public static UserInfo getUser() {
        return NEW_USER_CONTEXT.get();
    }


    /**
     * 获取用户id
     *
     * @return userId
     */
    public static String userId() {
        return getUser().getUserId();
    }

    /**
     * user编码
     *
     * @return {@link String }
     * <AUTHOR>
     * @date 2023/10/23
     */
    public static String userCode() {
        return getUser().getUserCode();
    }

    /**
     * 获取用户名称
     *
     * @return userName
     */
    public static String userName() {
        return getUser().getUserName();
    }

    public static String orgId() {
        return Optional.ofNullable(getUser()).map(UserInfo::getOrgInfo).map(YunshuOrgSyncResp::getId).orElse(null);
    }


    /**
     * 清除缓存信息
     */
    public static void remove() {
        NEW_USER_CONTEXT.remove();
    }

}
