package com.cscec3b.iti.projectmanagement.server.service;

import com.cscec3b.iti.projectmanagement.api.dto.response.pagelockinfo.PageLockResp;

public interface IPageLockService {

    /**
     * 检查页面状态，并尝试锁定
     *
     * @param pageId 页面 ID
     * @return {@link PageLockResp }
     */
    PageLockResp checkAndTryLock(String pageId);

    /**
     * 释放页面锁定状态
     *
     * @param pageId 页面 ID
     * @return boolean
     */
    boolean release(String pageId);
}
