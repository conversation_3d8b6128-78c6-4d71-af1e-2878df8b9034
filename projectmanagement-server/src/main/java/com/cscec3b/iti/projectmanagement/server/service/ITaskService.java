package com.cscec3b.iti.projectmanagement.server.service;

import com.cscec3b.iti.common.base.page.Page;
import com.cscec3b.iti.projectmanagement.api.dto.request.task.TaskQry;
import com.cscec3b.iti.projectmanagement.api.dto.request.task.TaskReq;
import com.cscec3b.iti.projectmanagement.api.dto.request.task.TaskStatusReq;
import com.cscec3b.iti.projectmanagement.api.dto.response.task.TaskResp;
import com.cscec3b.iti.projectmanagement.server.entity.Task;
import org.springframework.validation.annotation.Validated;

import javax.validation.Valid;

/**
 * @description 任务
 * <AUTHOR>
 * @date 2022/10/20
 */
@Validated
public interface ITaskService {

    /**
     * @description 创建待办任务
     * <AUTHOR>
     * @date 2022/10/19
     * @param taskReq 任务请求参数
     * @return true:成功; false:失败
     */
    Boolean createTask(@Valid TaskReq taskReq);

    /**
     * @description 更新任务状态为在办
     * <AUTHOR>
     * @date 2022/10/19
     * @param statusReq 任务状态请求参数
     * @return true:成功; false:失败
     */
    Boolean updateStatusDoing(TaskStatusReq statusReq);

    /**
     * @description 更新任务状态为已办
     * <AUTHOR>
     * @date 2022/10/19
     * @param statusReq 任务状态请求参数
     * @return true:成功; false:失败
     */
    Boolean updateStatusDone(TaskStatusReq statusReq);

    /**
     * @description 查询任务列表
     * <AUTHOR>
     * @date 2022/10/19
     * @param taskQry 任务查询条件
     * @return 任务数据
     */
    Page<TaskResp> pageList(TaskQry taskQry);

    /**
     * @description 根据项目id查询待办任务数据
     * <AUTHOR>
     * @date 2022/11/21
     * @param relationId 项目id
     * @return 任务数据
     */
    Task getTaskByProjectId(Long relationId);
}
