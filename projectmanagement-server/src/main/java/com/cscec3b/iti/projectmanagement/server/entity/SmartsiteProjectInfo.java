package com.cscec3b.iti.projectmanagement.server.entity;

import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

import java.util.Date;

/**
 * 智慧工地项目信息总表
 */
@ApiModel(description = "智慧工地项目信息总表")
@Data
public class SmartsiteProjectInfo {
    /**
     * 自增id,立项id
     */
    @ApiModelProperty(value = "自增id,立项id")
    private Integer id;

    /**
     * 与立项表关联
     */
    @ApiModelProperty(value = "与立项表关联")
    private Integer projectApplyId;

    /**
     * 品茗projectId
     */
    @ApiModelProperty(value = "品茗projectId")
    private Integer pinmingProjectId;

    /**
     * 品茗projectId关联departmentId
     */
    @ApiModelProperty(value = "品茗projectId关联departmentId")
    private Integer relationDepartmentId;


    /**
     * 项目名称
     */
    @ApiModelProperty(value = "项目名称")
    private String projectName;

    /**
     * 项目中心id
     */
    @ApiModelProperty(value = "项目中心id")
    private String standardProjectId;

    /**
     * 云枢组织id
     */
    @ApiModelProperty(value = "云枢组织id")
    private String yunshuOrgId;

    /**
     * 云枢treeid
     */
    @ApiModelProperty(value = "云枢treeid")
    private String yunshuTreeId;

    /**
     * 云枢上级单位组织id
     */
    @ApiModelProperty(value = "云枢上级单位组织id")
    private String yunshuParentOrgId;

    /**
     * 云枢上级单位组织name
     */
    @ApiModelProperty(value = "云枢上级单位组织name")
    private String yunshuParentOrgName;

    /**
     * 智慧工地上级单位组织id
     */
    @ApiModelProperty(value = "智慧工地上级单位组织id")
    private Integer departmentId;

    /**
     * 当前修改人id
     */
    @ApiModelProperty(value = "当前修改人id")
    private String modifierId;

    /**
     * 当前修改人姓名
     */
    @ApiModelProperty(value = "当前修改人姓名")
    private String modifierName;

    /**
     * 当前审批人id
     */
    @ApiModelProperty(value = "当前审批人id")
    private String approverId;

    /**
     * 当前审批人姓名
     */
    @ApiModelProperty(value = "当前审批人姓名")
    private String approverName;

    /**
     * 状态 0：待立项，1：已立项，2：待审批
     */
    @ApiModelProperty(value = "状态 0：待立项，1：已立项，2：待审批")
    private Byte status;

    /**
     * 创建时间
     */
    @ApiModelProperty(value = "创建时间")
    private Date createdAt;

    /**
     * 更新时间
     */
    @ApiModelProperty(value = "更新时间")
    private Date updatedAt;

    /**
     * 审批通过的信息集合
     */
    @ApiModelProperty(value = "审批通过的信息集合")
    private String displayInfo;

    @ApiModelProperty(value = "填报率")
    private Integer fillRate;

    /**
     * 项目编码
     */
    @ApiModelProperty(value = "项目编码")
    private String projectNo;

    /**
     * 未填报信息
     */
    @ApiModelProperty(value = "未填报信息")
    private String notFilledInfoList;
}
