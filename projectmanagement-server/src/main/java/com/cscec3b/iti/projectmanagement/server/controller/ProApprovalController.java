package com.cscec3b.iti.projectmanagement.server.controller;

import com.cscec3b.iti.common.base.api.GenericityResponse;
import com.cscec3b.iti.common.base.api.ResponseBuilder;
import com.cscec3b.iti.common.redis.lock.annotation.Lock;
import com.cscec3b.iti.logger.annotations.Logger;
import com.cscec3b.iti.projectmanagement.api.IProApprovalApi;
import com.cscec3b.iti.projectmanagement.api.dto.request.*;
import com.cscec3b.iti.projectmanagement.api.dto.response.project.FinanceResp;
import com.cscec3b.iti.projectmanagement.server.service.BureauContractService;
import com.cscec3b.iti.projectmanagement.server.service.ProTenderService;
import com.cscec3b.iti.projectmanagement.server.service.ProjectService;
import com.cscec3b.iti.projectmanagement.server.service.SupplementaryAgreementService;
import io.swagger.annotations.Api;
import lombok.extern.slf4j.Slf4j;
import org.apache.shenyu.client.springmvc.annotation.ShenyuSpringMvcClient;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RestController;

import javax.annotation.Resource;

/**
 * 项目立项
 */
@RestController
@RequestMapping(IProApprovalApi.PATH)
@Api(tags = "项目立项")
@Slf4j
public class ProApprovalController implements IProApprovalApi {

    @Resource
    private ProTenderService proTenderService;

    @Resource
    private SupplementaryAgreementService supplementaryAgreementService;

    @Resource
    private BureauContractService bureauContractService;

    @Resource
    private ProjectService projectService;

    /**
     * 投标总结立项API
     * @param request
     * @return
     */
    @Override
    @ShenyuSpringMvcClient
    @Logger
    @Lock(lockKey = "#request.data.belongId", waitTime = 3000)
    public GenericityResponse<Boolean> approvalByTender(MarketProReq<BidSummaryReq> request) {
        return new GenericityResponse<>(proTenderService.approvalByTender(request));
    }

    /**
     * 补充协议立项API
     * @param request
     * @return
     */
    @Override
    @ShenyuSpringMvcClient
    @Logger
    @Lock(lockKey = "#request.data.belongId", waitTime = 3000)
    public GenericityResponse<Boolean> approvalBySupplementaryAgreement(MarketProReq<SupplementaryAgreementReq> request) {
        return new GenericityResponse<>(supplementaryAgreementService.approvalBySupplementaryAgreement(request));
    }

    /**
     * 局内分包合同立项API
     * @param request
     * @return
     */
    @Override
    @ShenyuSpringMvcClient
    @Logger
    @Lock(lockKey = "#request.data.belongId", waitTime = 3000)
    public GenericityResponse<Boolean> approvalByBureauContract(MarketProReq<BureauContractReq> request) {
        return new GenericityResponse<>(bureauContractService.approvalByBureauContract(request));
    }

    /**
     * 财商立项数据获取API
     * @param id
     * @param type
     * @return
     */
    @Override
    @ShenyuSpringMvcClient
    @Logger
    public GenericityResponse<FinanceResp> financeGetData(String id, String type) {
        return projectService.financeGetData(id, type);
    }

    /**
     * 财商立项回调API
     * @param financeReq
     * @return
     */
    @Override
    @ShenyuSpringMvcClient
    @Logger
    public GenericityResponse<Boolean> financeUpdateData(FinanceReq financeReq) {
        return projectService.financeUpdateData(financeReq);
    }

    /**
     * 重置非独立性挂接文件
     * @param resetReq
     * @return
     */
    @Override
    @ShenyuSpringMvcClient
    @Logger
    public GenericityResponse<Boolean> resetNonIndependentProject(NonIndependentProjectReq resetReq) {
        return ResponseBuilder.fromData(projectService.resetNonIndependentProject(resetReq));
    }
}
