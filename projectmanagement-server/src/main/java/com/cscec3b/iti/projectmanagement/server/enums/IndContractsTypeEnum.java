package com.cscec3b.iti.projectmanagement.server.enums;

import com.baomidou.mybatisplus.annotation.EnumValue;
import com.cscec3b.iti.common.base.dictionary.IDataDictionary;
import lombok.Getter;

public enum IndContractsTypeEnum implements IDataDictionary {
    /**
     * 投标总结
     */
    TENDER_SUMMARY(1, "投标总结", "tender_summary", " 对外业务"),
    /**
     * 合同定案
     */
    PRESENTATION(2, "合同定案", "presentation", " 对外业务"),
    /**
     * 补充协议
     */
    AGREEMENT_PRESENTATION(3, "补充协议定案", "agreement_presentation", " 对外业务"),

    /**
     * 补充协议
     */
    AGREEMENT(8, "补充协议", "agreement", " 对外业务"),

    /**
     * 无合同续签补充协议
     */
    NO_CONTRACT_AGREEMENT(80, "无合同续签补充协议", "no_contract_agreement", " 对外业务"),

    /**
     * 局内部合同定案
     */
    INTERNAL_PRESENTATION(4, "局内部合同定案", "internal_presentation", "局内部业务"),
    /**
     * 局内部补充协议
     */
    INTERNAL_AGREEMENT(5, "局内部补充协议", "internal_agreement", "局内部业务"),
    /**
     * 局内部无合同续签补充协议
     */
    NO_CONTRACT_INTERNAL_AGREEMENT(50, "局内部无合同续签补充协议", "no_contract_internal_agreement", "局内部业务"),

    SECRECY(6, "保密项目文件", "secrecy", ""),

    CIVIL_MILITARY_INTEGRATION(7, "军民融合项目文件", "civil_military", ""),

    INVESTMENT(9, "投资类文件", "investment", "投资类业务");

    /**
     * 枚举代码
     */
    @EnumValue
    final Integer dictCode;

    /**
     * 中文
     */
    final String zhCN;

    /**
     * 英文
     */
    final String enUS;

    @Getter
    final String type;

    IndContractsTypeEnum(Integer dictCode, String zhCN, String enUS, String type) {
        this.dictCode = dictCode;
        this.zhCN = zhCN;
        this.enUS = enUS;
        this.type = type;
    }

    /**
     * 枚举代码
     *
     * @param str str
     * @return {@link IndContractsTypeEnum}
     */
    public static IndContractsTypeEnum getEnumCode(String str) {
        IndContractsTypeEnum[] values = IndContractsTypeEnum.values();
        for (IndContractsTypeEnum value : values) {
            if (value.getEnUS().equals(str) || value.getZhCN().equals(str)) {
                return value;
            }
        }
        return null;
    }

    /**
     * 得到枚举代码
     *
     * @param code 代码
     * @return {@link IndContractsTypeEnum}
     */
    public static IndContractsTypeEnum getEnumByCode(Integer code) {
        IndContractsTypeEnum[] values = IndContractsTypeEnum.values();
        for (IndContractsTypeEnum value : values) {
            if (value.getDictCode().equals(code)) {
                return value;
            }
        }
        return null;
    }

    @Override
    public Integer getDictCode() {
        return dictCode;
    }

    @Override
    public String getZhCN() {
        return zhCN;
    }

    @Override
    public String getEnUS() {
        return enUS;
    }

    @Override
    public String getDesc() {
        return "独立合同类型枚举类";
    }
}
