package com.cscec3b.iti.projectmanagement.server.service;

import java.util.List;

import com.cscec3b.iti.common.base.page.Page;
import com.cscec3b.iti.projectmanagement.api.dto.request.help.HelpCenterEditReq;
import com.cscec3b.iti.projectmanagement.api.dto.request.help.HelpCenterSaveReq;
import com.cscec3b.iti.projectmanagement.api.dto.response.help.HelpCenterResp;

/**
 * <AUTHOR>
 * @date 2023/1/3 16:03
 */
public interface HelpCenterService {

    /**
     * 首页按发布时间倒序排列的前5条帮助信息
     *
     * @return List<HelpCenterResp>
     * <AUTHOR>
     * @date 2023/01/03 16:08
     */
    List<HelpCenterResp> get();

    /**
     * 列表页
     *
     * @param current   当前页码
     * @param size      每页数量
     * @param title     标题，支持模糊搜索
     * @param beginTime 按发布时间搜索的起始范围
     * @param endTime   按发布时间搜索的结束范围
     * @param status    发布状态 0:未发布；1:已发布
     * @return com.cscec3b.iti.common.base.page.Page<com.cscec3b.iti.projectmanagement.api.dto.response.help.HelpCenterResp>
     * <AUTHOR>
     * @date 2023/01/03 16:08
     */
    Page<HelpCenterResp> getList(int current, int size, String title, Long beginTime, Long endTime, Integer status);

    /**
     * 保存帮助信息
     *
     * @param helpCenterSaveReq 新增帮助信息请求信息
     * @return java.lang.Boolean
     * <AUTHOR>
     * @date 2023/01/03 16:10
     */
    Boolean save(HelpCenterSaveReq helpCenterSaveReq);

    /**
     * 保存草稿信息，只能保存自己的未发布的信息，已发布的信息编辑后会再次发布
     *
     * @param helpCenterEditReq 编辑后的帮助信息请求信息
     * @return java.lang.Boolean
     * <AUTHOR>
     * @date 2023/01/03 16:10
     */
    Boolean edit(HelpCenterEditReq helpCenterEditReq);

    /**
     * 发布帮助信息
     *
     * @param helpCenterEditReq 需要发布的帮助信息请求信息
     * @return java.lang.Boolean
     * <AUTHOR>
     * @date 2023/01/03 16:12
     */
    Boolean publish(HelpCenterEditReq helpCenterEditReq);
    
    
    /**
     * 删除帮助信息
     *
     * @param ids ids
     * @return {@link Boolean }
     * <AUTHOR>
     * @date 2023/08/21
     */
    Boolean delete(Long[] ids);


}
