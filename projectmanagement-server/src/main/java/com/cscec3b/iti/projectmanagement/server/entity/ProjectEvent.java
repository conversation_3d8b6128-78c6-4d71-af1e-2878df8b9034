package com.cscec3b.iti.projectmanagement.server.entity;

import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;
import lombok.experimental.Accessors;

/**
 * 项目信息事件表
 */
@ApiModel(description = "项目信息事件表")
@Data
@Accessors(chain = true)
public class ProjectEvent {
    /**
     * 主键id
     */
    @ApiModelProperty(value = "主键id")
    private Integer id;

    /**
     * 事件code
     */
    @ApiModelProperty(value = "事件code")
    private String eventCode;

    /**
     * 事件名称
     */
    @ApiModelProperty(value = "事件名称")
    private String eventName;

    /**
     * 事件描述
     */
    @ApiModelProperty(value = "事件描述")
    private String eventDesc;

    /**
     * 业务字段
     */
    @ApiModelProperty(value = "业务字段")
    private String businessColumn;

    /**
     * 事件查询API
     */
    @ApiModelProperty(value = "事件查询API")
    private String eventApi;

    /**
     * 创建时间
     */
    @ApiModelProperty(value = "创建时间")
    private Long createAt;

    /**
     * 创建用户
     */
    @ApiModelProperty(value = "创建用户")
    private String createBy;

    /**
     * 修改时间
     */
    @ApiModelProperty(value = "修改时间")
    private Long updateAt;

    /**
     * 修改用户
     */
    @ApiModelProperty(value = "修改用户")
    private String updateBy;

    /**
     * 状态: 0:删除; 1:正常;
     */
    @ApiModelProperty(value = "状态: 0:删除; 1:正常;")
    private Integer status;
}