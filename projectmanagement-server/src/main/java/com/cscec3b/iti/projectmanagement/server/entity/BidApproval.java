package com.cscec3b.iti.projectmanagement.server.entity;

import com.baomidou.mybatisplus.annotation.FieldFill;
import com.baomidou.mybatisplus.annotation.IdType;
import com.baomidou.mybatisplus.annotation.TableField;
import com.baomidou.mybatisplus.annotation.TableId;
import com.baomidou.mybatisplus.annotation.TableName;

import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;
import lombok.experimental.Accessors;

/**
 * 中标未立项表
 */
@ApiModel(description = "中标未立项表")
@Data
@Accessors(chain = true)
@TableName(value = "bid_approval")
public class BidApproval {
    /**
     * id
     */
    @TableId(value = "id", type = IdType.ASSIGN_ID)
    @ApiModelProperty(value = "id")
    private Long id;

    /**
     * 原文件id
     */
    @TableField(value = "belong_id")
    @ApiModelProperty(value = "原文件id")
    private Long belongId;

    /**
     * 立项数据来源，tender_summary：投标总结，presentation：合同定案，agreement：补充协议，internal_presentation：局内部合同定案，internal_agreement
     * ：局内部补充协议
     */
    @TableField(value = "`type`")
    @ApiModelProperty(value = "立项数据来源，tender_summary：投标总结，presentation：合同定案，agreement：补充协议，internal_presentation" +
            "：局内部合同定案，internal_agreement：局内部补充协议, secrecy: 保密文件, civil_military:军民融合项目信息")
    private String type;

    /**
     * 补充协议编号
     */
    @TableField(value = "agreement_code")
    @ApiModelProperty(value = "补充协议编号")
    private String agreementCode;

    /**
     * 局内部合同定案编号
     */
    @TableField(value = "presentation_code")
    @ApiModelProperty(value = "局内部合同定案编号")
    private String presentationCode;

    /**
     * 工程编号
     */
    @TableField(value = "project_code")
    @ApiModelProperty(value = "工程编号")
    private String projectCode;

    @TableField(value = "investment_code")
    @ApiModelProperty(value = "投资编码")
    private String investmentCode;

    /**
     * 工程名称
     */
    @TableField(value = "project_name")
    @ApiModelProperty(value = "工程名称")
    private String projectName;

    /**
     * 业主名称
     */
    @TableField(value = "customer_name")
    @ApiModelProperty(value = "业主名称")
    private String customerName;

    /**
     * Y:国内，N：海外
     */
    @TableField(value = "project_belong")
    @ApiModelProperty(value = "Y:国内，N：海外")
    private String projectBelong;

    /**
     * 省
     */
    @TableField(value = "province")
    @ApiModelProperty(value = "省")
    private String province;

    /**
     * 市
     */
    @TableField(value = "city")
    @ApiModelProperty(value = "市")
    private String city;

    /**
     * 区
     */
    @TableField(value = "region")
    @ApiModelProperty(value = "区")
    private String region;

    /**
     * 国别
     */
    @TableField(value = "country")
    @ApiModelProperty(value = "国别")
    private String country;

    /**
     * 具体地址
     */
    @TableField(value = "address")
    @ApiModelProperty(value = "具体地址")
    private String address;

    /**
     * 工程类型（国家标准）
     */
    @TableField(value = "country_project_type")
    @ApiModelProperty(value = "工程类型（国家标准）")
    private String countryProjectType;

    /**
     * 工程类型（总公司市场口径）
     */
    @TableField(value = "market_project_type")
    @ApiModelProperty(value = "工程类型（总公司市场口径）")
    private String marketProjectType;

    /**
     * 工程类型（总公司市场口径）2
     */
    @TableField(value = "market_project_type2")
    @ApiModelProperty(value = "工程类型（总公司市场口径）2")
    private String marketProjectType2;

    /**
     * 工程类型(总公司综合口径)
     */
    @TableField(value = "project_type")
    @ApiModelProperty(value = "工程类型(总公司综合口径)")
    private String projectType;

    /**
     * 工程类型(总公司综合口径)2
     */
    @TableField(value = "project_type2")
    @ApiModelProperty(value = "工程类型(总公司综合口径)2")
    private String projectType2;

    /**
     * 工程类型(总公司综合口径)3
     */
    @TableField(value = "project_type3")
    @ApiModelProperty(value = "工程类型(总公司综合口径)3")
    private String projectType3;

    /**
     * 工程类型(总公司综合口径)4
     */
    @TableField(value = "project_type4")
    @ApiModelProperty(value = "工程类型(总公司综合口径)4")
    private String projectType4;

    /**
     * 是否创建指挥部,Y:是，N：否
     */
    @TableField(value = "is_create_head")
    @ApiModelProperty(value = "是否创建指挥部,Y:是，N：否")
    private String createHead;

    /**
     * 独立性判断，Y：是，N：否，D：不予立项
     */
    @TableField(value = "is_independ_project")
    @ApiModelProperty(value = "独立性判断，Y：是，N：否，D：不予立项")
    private String independentProject;


    @TableField(value = "is_engineering_project")
    @ApiModelProperty(value = "是否工程性项目，Y：是，N：否")
    private String engineeringProject;

    /**
     * 发起人
     */
    @TableField(value = "submit_person")
    @ApiModelProperty(value = "发起人")
    private String submitPerson;

    /**
     * 发起人姓名
     */
    @TableField(value = "submit_person_name")
    @ApiModelProperty(value = "发起人姓名")
    private String submitPersonName;

    /**
     * 复核人
     */
    @TableField(value = "approval_person")
    @ApiModelProperty(value = "复核人")
    private String approvalPerson;

    /**
     * 复核人姓名
     */
    @TableField(value = "approval_person_name")
    @ApiModelProperty(value = "复核人姓名")
    private String approvalPersonName;

    /**
     * 状态
     */
    @TableField(value = "`status`")
    @ApiModelProperty(value = "状态")
    private String status;

    /**
     * 挂接id
     */
    @TableField(value = "associated_id")
    @ApiModelProperty(value = "挂接id")
    private Long associatedId;

    /**
     * 创建人
     */
    @TableField(value = "create_by", fill = FieldFill.INSERT)
    @ApiModelProperty(value = "创建人")
    private String createBy;

    /**
     * 创建时间
     */
    @TableField(value = "create_at", fill = FieldFill.INSERT)
    @ApiModelProperty(value = "创建时间")
    private Long createAt;

    /**
     * 更新人
     */
    @TableField(value = "update_by", fill = FieldFill.INSERT_UPDATE)
    @ApiModelProperty(value = "更新人")
    private String updateBy;

    /**
     * 修改时间
     */
    @TableField(value = "update_at", fill = FieldFill.INSERT_UPDATE)
    @ApiModelProperty(value = "修改时间")
    private Long updateAt;

    /**
     * 是否删除：0： 未删除； 时间戳为删除时间
     */
    @TableField(value = "deleted")
    @ApiModelProperty(value = "是否删除：0： 未删除； 时间戳为删除时间")
    private Long deleted;

    /**
     * 扩展字段
     */
    @TableField(value = "extension")
    @ApiModelProperty(value = "扩展字段")
    private String extension;

    /**
     * 云枢执行单位id
     */
    @TableField(value = "yunshu_execute_unit_id")
    @ApiModelProperty(value = "云枢执行单位id")
    private String yunshuExecuteUnitId;

    /**
     * 云枢执行单位code
     */
    @TableField(value = "yunshu_execute_unit_code")
    @ApiModelProperty(value = "云枢执行单位code")
    private String yunshuExecuteUnitCode;

    /**
     * 云枢执行单位
     */
    @TableField(value = "yunshu_execute_unit")
    @ApiModelProperty(value = "云枢执行单位")
    private String yunshuExecuteUnit;

    /**
     * 云枢执行单位idPath
     */
    @TableField(value = "yunshu_execute_unit_id_path")
    @ApiModelProperty(value = "云枢执行单位idPath")
    private String yunshuExecuteUnitIdPath;

    /**
     * 项目中心id
     */
    @TableField(value = "cpm_project_id")
    @ApiModelProperty(value = "项目中心id")
    private Long cpmProjectId;

    /**
     * 前置文件id
     */
    @TableField(value = "pre_file_id")
    @ApiModelProperty(value = "前置文件id")
    private Long preFileId;

    /**
     * 立项步骤列表
     */
    @TableField(value = "step_list")
    @ApiModelProperty(value = "立项步骤列表")
    private String stepList;

    /**
     * 立项项目类型
     */
    @TableField(value = "approval_type_id")
    @ApiModelProperty(value = "立项项目类型")
    private Long approvalTypeId;


    /**
     * 当前步骤id
     */
    @TableField(value = "current_step_no")
    @ApiModelProperty(value = "当前步骤id")
    private Integer currentStepNo;
    /**
     * 发起立项时间
     */
    @TableField(value = "approval_begin_time")
    @ApiModelProperty(value = "发起立项时间")
    private Long approvalBeginTime;

    /**
     * 项目部创建类型： 0:挂接项目部； 1: 创建项目部
     */
    @TableField(value = "dept_create_type")
    @ApiModelProperty(value = "项目部创建类型： 0:挂接项目部； 1: 创建项目部")
    private Integer deptCreateType;

    /**
     * 独立时新增项目部的名称
     */
    @TableField(value = "dept_name")
    @ApiModelProperty(value = "独立时新增项目部的名称")
    private String deptName;

    /**
     * 新增项目部的简称
     */
    @TableField(value = "dept_abbr")
    @ApiModelProperty(value = "新增项目部的简称")
    private String deptAbbr;

    /**
     * 独立时新增项目的上级部门id
     */
    @TableField(value = "parent_dept_id")
    @ApiModelProperty(value = "独立时新增项目的上级部门id")
    private String parentDeptId;

    /**
     * 独立立项挂接的项目部id, 或创建成功后的项目部id
     */
    @TableField(value = "dept_id")
    @ApiModelProperty(value = "项目部id", notes = "独立立项挂接的项目部id, 或创建成功后的项目部id")
    private String deptId;

    /**
     * 部门创建者
     */
    @TableField(value = "dept_create_by")
    @ApiModelProperty(value = "项目部创建人")
    private String deptCreateBy;

    /**
     * 步骤版本
     */
    @TableField(value = "step_version")
    @ApiModelProperty(value = "项目立项类型与步骤映射版本")
    private Integer stepVersion;
}