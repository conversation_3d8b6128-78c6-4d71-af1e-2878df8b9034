package com.cscec3b.iti.projectmanagement.server.service.impl;

import java.time.Instant;
import java.util.*;
import java.util.stream.Collectors;

import org.apache.commons.collections.CollectionUtils;
import org.apache.commons.lang3.ObjectUtils;
import org.apache.commons.lang3.StringUtils;
import org.springframework.beans.BeanUtils;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;

import com.baomidou.mybatisplus.core.conditions.query.LambdaQueryWrapper;
import com.baomidou.mybatisplus.core.toolkit.Wrappers;
import com.baomidou.mybatisplus.extension.service.impl.ServiceImpl;
import com.cscec3b.iti.common.base.page.Page;
import com.cscec3b.iti.common.web.exception.BusinessException;
import com.cscec3b.iti.common.web.exception.FrameworkException;
import com.cscec3b.iti.model.resp.ContractFileRelationResp;
import com.cscec3b.iti.projectmanagement.api.bidapproval.dto.request.BidSummaryFileReq;
import com.cscec3b.iti.projectmanagement.api.dto.dto.AttachmentDto;
import com.cscec3b.iti.projectmanagement.api.dto.request.ContractFilePageReq;
import com.cscec3b.iti.projectmanagement.api.dto.request.approvalstep.ContractFileUpdateParamReq;
import com.cscec3b.iti.projectmanagement.api.dto.request.civilmilitary.CivilMilitaryIntegrationReq;
import com.cscec3b.iti.projectmanagement.api.dto.request.civilmilitary.QueryCivilMilitaryIntegrationParams;
import com.cscec3b.iti.projectmanagement.api.dto.request.civilmilitary.UpdateCivilMilitaryIntegrationReq;
import com.cscec3b.iti.projectmanagement.api.dto.request.special.SpecialProjectDto;
import com.cscec3b.iti.projectmanagement.api.dto.response.ContractFileDetailResp;
import com.cscec3b.iti.projectmanagement.api.dto.response.ContractFilePageResp;
import com.cscec3b.iti.projectmanagement.api.dto.response.secreycivilmilitary.CivilMilitaryIntegrationDetailResp;
import com.cscec3b.iti.projectmanagement.api.dto.response.secreycivilmilitary.CivilMilitaryIntegrationResp;
import com.cscec3b.iti.projectmanagement.server.bidapprovalservice.BidFilePreHandler;
import com.cscec3b.iti.projectmanagement.server.constant.Constants;
import com.cscec3b.iti.projectmanagement.server.converter.mapstruct.IConverter;
import com.cscec3b.iti.projectmanagement.server.entity.*;
import com.cscec3b.iti.projectmanagement.server.enums.IndContractsTypeEnum;
import com.cscec3b.iti.projectmanagement.server.enums.OSSPathTypeEnum;
import com.cscec3b.iti.projectmanagement.server.enums.ProjectClassEnum;
import com.cscec3b.iti.projectmanagement.server.mapper.CivilMilitaryIntegrationMapper;
import com.cscec3b.iti.projectmanagement.server.mapper.ProjectMapper;
import com.cscec3b.iti.projectmanagement.server.service.*;
import com.cscec3b.iti.projectmanagement.server.util.LoginUserUtil;
import com.github.pagehelper.PageHelper;
import com.odin.freyr.common.orika.BeanMapUtils;

import cn.hutool.core.bean.BeanUtil;
import cn.hutool.core.util.IdUtil;
import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;


@Slf4j
@Service
@Transactional(rollbackFor = Exception.class)
@RequiredArgsConstructor
public class CivilMilitaryIntegrationServiceImpl extends ServiceImpl<CivilMilitaryIntegrationMapper, CivilMilitaryIntegration>
        implements BidFilePreHandler<BidSummaryFileReq>, CivilMilitaryIntegrationService{


    private static final int DAY = 60 * 60 * 24;
    private static final int INT = 1000;

    /**
     * 附件服务
     */
    private final IAttachmentService attachmentService;

    /**
     * i 转换器
     */
    private final IConverter<CivilMilitaryIntegrationDetailResp, CivilMilitaryIntegrationResp, CivilMilitaryIntegration> iConverter;

    /**
     * 项目映射器
     */
    private final ProjectMapper projectMapper;

    /**
     * id生成器
     */
    private final IdTableService idService;

    /**
     * 项目进度服务
     */
    private final ProjectProgressService projectProgressService;

    /**
     * 流程服务
     */
    private final WfApprovalService wfApprovalService;


    /**
     * @param civilMilitaryIntegrationReq
     * @return Boolean
     * @description 新增特殊立项
     * @date 2023/02/14 15:25
     * <AUTHOR>
     */
    @Override
    @Transactional(rollbackFor = Exception.class)
    public Long create(CivilMilitaryIntegrationReq civilMilitaryIntegrationReq) {
        log.info("创建军民融合项目createSpecialProjectReq:{}",civilMilitaryIntegrationReq);
        final CivilMilitaryIntegration project = new CivilMilitaryIntegration();
        BeanUtil.copyProperties(civilMilitaryIntegrationReq, project);
        //根据项目分类创建不同类型项目记录填充项目属性入库
        fillProjectProperties(project, civilMilitaryIntegrationReq);
        //生成belongId, 生成文件编码
        project.setFileCode(idService.getCommonKey(Constants.CIVIL_MILITARY_SEQUENCE_PREFIX));
        project.setBelongId(IdUtil.getSnowflake().nextId());
        //项目信息入库
        Integer result = this.baseMapper.insert(project);
        //用户有附件信息时，保存附件信息
        if (CollectionUtils.isNotEmpty(civilMilitaryIntegrationReq.getAttachments())) {
            List<Attachment> attachments =
                    this.convertAttachments(civilMilitaryIntegrationReq.getAttachments(), String.valueOf(project.getId()));
            attachmentService.batchSave(attachments);
        }
        return project.getId();
    }

    /**
     * 填充项目属性字段，该项目为建造类项目,全量接收创建请求属性,否则仅设置部分非建造字段
     *
     * @param project   项目
     * @param createReq 新增参数
     * @date 2023/02/20 15:27
     * <AUTHOR>
     */
    private void fillProjectProperties(CivilMilitaryIntegration project, CivilMilitaryIntegrationReq createReq) {
        log.info("填充项目属性字段fillProjectProperties,Project:{},CreateSpecialProjectReq:{}", project, createReq);
        //提取项目分类IdPath的根类别
        String standardType = Arrays.stream(createReq.getStandardTypeCodePath().split(Constants.ID_PATH_CONNECTOR))
                .filter(StringUtils::isNotBlank).collect(Collectors.toList()).get(Constants.NUMBER_ZERO);
        String constructProject = ProjectClassEnum.CONSTRUCTION_PROJECT.getDictCode().toString();

        if (standardType.equals(constructProject)) {
            //建造类项目
            BeanUtils.copyProperties(createReq, project);
            //总工期 = 合同竣工时间-合同开工时间+1天
            if (!Objects.isNull(createReq.getWorkerEndTime()) && !Objects.isNull(createReq.getWorkerBeginTime())
                    && createReq.getWorkerEndTime()>=createReq.getWorkerBeginTime()) {
                long countDays = ((createReq.getWorkerEndTime() - createReq.getWorkerBeginTime()) / DAY / INT) + 1;
                project.setCountDays((int) countDays);
            }else {
                project.setCountDays(null);
            }
            project.setProjectManager(createReq.getContractManager());
        } else {
            //非建造类项目,拷贝部分属性
            SpecialProjectDto dto = new SpecialProjectDto();
            BeanUtils.copyProperties(createReq, dto);
            BeanUtils.copyProperties(dto, project);
            //切换云枢组织
            project.setYunshuExecuteUnit(createReq.getYunshuExecuteUnit())
                    .setYunshuExecuteUnitCode(createReq.getYunshuExecuteUnitCode())
                    .setYunshuExecuteUnitId(createReq.getYunshuExecuteUnitId())
                    .setYunshuExecuteUnitIdPath(createReq.getYunshuExecuteUnitIdPath())
                    .setYunshuExecuteUnitAbbreviation(createReq.getYunshuExecuteUnitAbbreviation());
        }
        project.setCreateAt(Instant.now().toEpochMilli())
                .setUpdateAt(Instant.now().toEpochMilli());
    }

    /**
     * 更新信息
     * @param updateReq updateReq
     * @return Boolean
     * @date 2023/02/14 15:29
     * <AUTHOR>
     */
    @Override
    @Transactional(rollbackFor = Exception.class)
    public Boolean updateCivilMilitaryProject(UpdateCivilMilitaryIntegrationReq updateReq) {
        log.info("更新保密，军民融合项目信息UpdateSpecialProjectReq:{}", updateReq);
        Long id = updateReq.getId();
        CivilMilitaryIntegration existProject = this.baseMapper.selectById(id);
        if (Objects.isNull(existProject)) {
            throw new BusinessException(8010502, new String[]{String.valueOf(id)});
        }

        List<Attachment> oldAttachments = attachmentService
                .getByBusinessIdsAndType(Collections.singletonList(id.toString()), OSSPathTypeEnum.CIVIL_MILITARY.getCode());
        log.info("删除保密-军民融合项目旧附件信息:{}", oldAttachments);
        //如果旧项目存在附件信息，删除旧附件，保存新附件
        if (!Constants.NUMBER_ZERO.equals(oldAttachments.size())) {
            if (!attachmentService.batchDelete(Collections.singletonList(String.valueOf(id)),
                    OSSPathTypeEnum.CIVIL_MILITARY.getCode())) {
                throw new BusinessException(8010404, new String[]{id.toString()});
            }
        }
        List<Attachment> attachments =
                this.convertAttachments(updateReq.getAttachments(), String.valueOf(id));
        log.info("保存保密-军民融合项目附件信息:{}", attachments);
        attachmentService.batchSave(attachments);

        //判断项目分类,更新已存在的项目属性信息
        updateProjectProperties(existProject, updateReq);
        Integer result = this.baseMapper.updateById(existProject);
        if (Constants.NUMBER_ZERO.equals(result)) {
            throw new BusinessException(8010503, new String[]{String.valueOf(id)});
        }
        return true;
    }

    /**
     * 判断已存项目分类与更新请求中的项目分类：
     * 1：若已存为建造、更新为建造 或 已存为非建造、更新为建造则全量更新
     * 2：若已存为建造、更新为非建造则清空项目相关字段，只更新部分的非建造的属性
     * 3：若已存为非建造、更新仍为非建造，则仅更新部分非建造属性
     *
     * @param existProject 已存项目信息
     * @param updateReq    更新字段信息
     * @return
     * @date 2023/02/20 15:14
     * <AUTHOR>
     */
    private void updateProjectProperties(CivilMilitaryIntegration existProject, UpdateCivilMilitaryIntegrationReq updateReq) {
        String constructProject = ProjectClassEnum.CONSTRUCTION_PROJECT.getDictCode().toString();
        //        //已存项目的项目分类
        //        String existProClass = Arrays.stream(existProject.getProjectClassIdPath().split(Constants.ID_PATH_CONNECTOR))
        //                .filter(StringUtils::isNotBlank).collect(Collectors.toList()).get(Constants.NUMBER_ZERO);
        //        //更新项目的项目分类
        //        String reqProClass = Arrays.stream(updateReq.getProjectClassIdPath().split(Constants.ID_PATH_CONNECTOR))
        //                .filter(StringUtils::isNotBlank).collect(Collectors.toList()).get(Constants.NUMBER_ZERO);

        //已存项目的项目标准分类
        String existProClass = Arrays.stream(existProject.getStandardTypeCodePath().split(Constants.ID_PATH_CONNECTOR))
                .filter(StringUtils::isNotBlank).collect(Collectors.toList()).get(Constants.NUMBER_ZERO);
        //更新项目的项目标准分类
        String reqProClass = Arrays.stream(updateReq.getStandardTypeCodePath().split(Constants.ID_PATH_CONNECTOR))
                .filter(StringUtils::isNotBlank).collect(Collectors.toList()).get(Constants.NUMBER_ZERO);

        if (constructProject.equals(existProClass) && constructProject.equals(reqProClass)
                || !constructProject.equals(existProClass) && constructProject.equals(reqProClass)) {
            //已存为建造、更新为建造 或 已存为非建造、更新为建造则全量更新
            BeanUtils.copyProperties(updateReq, existProject);
            //总工期 = 合同竣工时间-合同开工时间+1天
            if (!Objects.isNull(updateReq.getWorkerEndTime()) && !Objects.isNull(updateReq.getWorkerBeginTime())
                    && updateReq.getWorkerEndTime()>=updateReq.getWorkerBeginTime()) {
                long countDays = ((updateReq.getWorkerEndTime() - updateReq.getWorkerBeginTime()) / INT / DAY) + 1;
                existProject.setCountDays((int) countDays);
            } else {
                existProject.setCountDays(null);
            }
            existProject.setProjectManager(updateReq.getContractManager()).setUpdateAt(Instant.now().toEpochMilli());
        } else if (constructProject.equals(existProClass) && !constructProject.equals(reqProClass)) {
            //若已存为建造、更新为非建造则清空项目相关字段，只更新部分的非建造的属性
            //仅有部分属性的非建造项目对象
            SpecialProjectDto dto = new SpecialProjectDto();
            BeanUtils.copyProperties(updateReq, dto);
            Project updateProject = new Project();
            BeanUtils.copyProperties(dto, updateProject);
            updateProject.setId(existProject.getId())
                    .setCreateAt(existProject.getCreateAt())
                    .setUpdateAt(Instant.now().toEpochMilli())
                    .setYunshuExecuteUnit(existProject.getYunshuExecuteUnit())
                    .setYunshuExecuteUnitId(existProject.getYunshuExecuteUnitId())
                    .setYunshuExecuteUnitIdPath(existProject.getYunshuExecuteUnitIdPath())
                    .setYunshuExecuteUnitCode(existProject.getYunshuExecuteUnitCode());

            BeanUtils.copyProperties(updateProject, existProject);
        } else {
            //若已存为非建造、更新仍为非建造，则仅更新部分非建造属性
            SpecialProjectDto dto = new SpecialProjectDto();
            BeanUtils.copyProperties(updateReq, dto);
            BeanUtils.copyProperties(dto, existProject);
            existProject.setUpdateAt(Instant.now().toEpochMilli());
        }
    }

    /**
     * @param id 项目id
     * @return SpecialProjectDetailResp
     * @date 2023/02/14 15:36
     * <AUTHOR>
     */
    @Override
    public CivilMilitaryIntegrationDetailResp getDetail(Long id) {
        CivilMilitaryIntegration project = this.baseMapper.selectById(id);
        if (Objects.isNull(project) || Objects.isNull(project.getId())) {
            // 文件信息为空
            throw new BusinessException(8010502, new String[]{String.valueOf(id)});
        }
        final CivilMilitaryIntegrationDetailResp resp = new CivilMilitaryIntegrationDetailResp();

        final WfApproval approvalInfo =
                wfApprovalService.getOne(Wrappers.<WfApproval>lambdaQuery().eq(WfApproval::getBelongId,
                        project.getId()));
        if (Objects.nonNull(approvalInfo)) {
            resp.setProcInstId(approvalInfo.getProcInstId()).setWorkflowStarter(approvalInfo.getCreateBy())
                    .setBillState(approvalInfo.getBillState());
        } else {
            resp.setWorkflowStarter(project.getCreateBy());
        }
        List<Attachment> attachmentList = attachmentService
                .getByBusinessIdsAndType(Collections.singletonList(id.toString()), OSSPathTypeEnum.CIVIL_MILITARY.getCode());

        BeanUtils.copyProperties(project, resp);

        List<AttachmentDto> attachmentDtoList = attachmentList.stream().map(attachment ->
                BeanUtil.copyProperties(attachment, AttachmentDto.class)).collect(Collectors.toList());
        resp.setContractManager(project.getProjectManager()).setAttachments(attachmentDtoList);
        return resp;
    }

    @Override
    public boolean deleteCivilMilitaryProject(List<Long> ids) {
        this.baseMapper.deleteBatchIds(ids);
        return true;
    }

    /**
     * 特别项目清单
     *
     * @param params 参数
     * @return Page<SpecialProjectResp>
     * @date 2023/02/14 15:51
     * <AUTHOR>
     */
    @Override
    public Page<CivilMilitaryIntegrationResp> pageList(QueryCivilMilitaryIntegrationParams params) {
        final LambdaQueryWrapper<CivilMilitaryIntegration> queryWrapper =
                Wrappers.<CivilMilitaryIntegration>lambdaQuery()
                        .likeRight(CivilMilitaryIntegration::getYunshuExecuteUnitIdPath, params.getYunshuExecuteUnitIdPath())
                        .likeRight(StringUtils.isNotBlank(params.getStandardTypeCodePath()),
                                CivilMilitaryIntegration::getStandardTypeCodePath, params.getStandardTypeCodePath())
                        .like(StringUtils.isNotBlank(params.getProjectAddress()),
                                CivilMilitaryIntegration::getProjectAddress, params.getProjectAddress())
                        .like(StringUtils.isNotBlank(params.getProjectName()), CivilMilitaryIntegration::getProjectName,
                                params.getProjectName())
                        .orderByDesc(CivilMilitaryIntegration::getCreateAt);
        final com.github.pagehelper.Page<CivilMilitaryIntegration> page = PageHelper.startPage(params.getCurrent(),
                params.getSize()).doSelectPage(() -> list(queryWrapper));

        Page<CivilMilitaryIntegrationResp> respPage = new Page<>(page.getTotal(), page.getPageNum(), page.getPageSize());
        final List<CivilMilitaryIntegrationResp> secrecyProjectResps = iConverter.entityList2PageList(page.getResult());
        respPage.setRecords(secrecyProjectResps);
        return respPage;
    }

    /**
     * 附件信息请求对象转换为附件存储实体对象
     *
     * @param attachmentDtos 附件 DTO
     * @param projectId      项目 ID
     * @return List<Attachment>
     * @date 2023/02/16 17:02
     * <AUTHOR>
     */
    private List<Attachment> convertAttachments(List<AttachmentDto> attachmentDtos, String projectId) {
        return attachmentDtos.stream().map(dto -> {
                    Attachment attachment = BeanMapUtils.map(dto, Attachment.class);
                    attachment.setBusinessId(projectId)
                            .setBusinessType(OSSPathTypeEnum.CIVIL_MILITARY.getCode())
                            .setCreateAt(String.valueOf(Instant.now().toEpochMilli()))
                            .setCreateBy(LoginUserUtil.userId());
                    return attachment;
                }
        ).collect(Collectors.toList());
    }


    @Override
    public IndContractsTypeEnum doSaveFile(String preFileId, String curFileType, BidSummaryFileReq data) {
        return IndContractsTypeEnum.CIVIL_MILITARY_INTEGRATION;
    }

    @Override
    public Boolean existFileByBelongId(Long belongId) {
        return Objects.nonNull(getById(belongId));
    }

    @Override
    public Page<ContractFilePageResp> getContractFilePageList(ContractFilePageReq pageReq,
            IndContractsTypeEnum scopeTypeEnum) {
        final com.github.pagehelper.Page<ContractFilePageResp> page =
        PageHelper.startPage(pageReq.getCurrent(), pageReq.getSize()).doSelectPage(() ->
                this.baseMapper.pageList(pageReq, scopeTypeEnum));
        final List<ContractFilePageResp> result = page.getResult();
        return new Page<ContractFilePageResp>(page.getTotal(), pageReq.getCurrent(), pageReq.getSize()).setRecords(result);
    }

    @Override
    public ContractFileRelationResp getFileInfoById(Long id) {
        final CivilMilitaryIntegration militaryIntegration = this.getById(id);
        return genContractFileRelationResp(militaryIntegration);
    }

    /**
     * 填充ContractFileRelationResp
     *
     * @param civilMilitary 军民融合项目
     * @return {@link ContractFileRelationResp}
     */
    private static ContractFileRelationResp genContractFileRelationResp(final CivilMilitaryIntegration civilMilitary) {
        return Optional.ofNullable(civilMilitary).map(bc -> {
            final ContractFileRelationResp contractFileRelationResp = new ContractFileRelationResp();
            contractFileRelationResp.setBelongFileType(IndContractsTypeEnum.CIVIL_MILITARY_INTEGRATION.getDictCode())
                    .setBelongFileTypeCode(IndContractsTypeEnum.CIVIL_MILITARY_INTEGRATION.name())
                    .setBelongFileTypeName(IndContractsTypeEnum.CIVIL_MILITARY_INTEGRATION.getZhCN()).setBelongId(bc.getBelongId())
                    .setFileCode(bc.getId().toString()).setId(bc.getId()).setProjectName(bc.getProjectName());
            return contractFileRelationResp;
        }).orElse(null);
    }

    @Override
    public Long createProject(BidApproval bidApproval) {
        log.info("保密项目立项 bidApproval:{}",bidApproval);
        final IndContractsTypeEnum contractsTypeEnum = IndContractsTypeEnum.getEnumCode(bidApproval.getType());
        if (Objects.isNull(contractsTypeEnum)) {
            throw new FrameworkException(-1, "文件类型错误");
        }
        // 检查文件

        final CivilMilitaryIntegration civilMilitaryIntegration = getById(bidApproval.getBelongId());
        if (ObjectUtils.isEmpty(civilMilitaryIntegration)) {
            throw new BusinessException(8010320);
        }
        // 检查是否重复立项
        final Project projectByBidId =
                projectMapper.selectOne(Wrappers.<Project>lambdaQuery().eq(Project::getIndependentContractId,
                        bidApproval.getId()).eq(Project::getIndependentContractType, contractsTypeEnum.getDictCode()));
        if (Objects.nonNull(projectByBidId)) {
            throw new BusinessException(8010006);
        }
        // 更新文件信息
        final Long independentId = bidApproval.getId();
        civilMilitaryIntegration.setIndependentContractId(independentId)
                .setIndependentContractType(contractsTypeEnum.getDictCode())
                .setIndependent(bidApproval.getIndependentProject());
        this.updateById(civilMilitaryIntegration);

        Project project = new Project();

        BeanUtils.copyProperties(civilMilitaryIntegration, project);
        //根据项目分类创建不同类型项目记录填充项目属性入库

        final String cpmMark = idService.getCpmProjectKey();
        project.setCpmProjectKey(cpmMark).setCpmProjectName(civilMilitaryIntegration.getProjectName())
                .setCpmProjectAbbreviation(civilMilitaryIntegration.getProjectName())
            .setCpmBusinessSegment(project.getMarketingBusinessSegment())
            .setCpmBusinessSegmentCodePath(project.getCpmBusinessSegmentCodePath())
                .setSourceSystem(2);
        //项目信息入库
        Integer result = projectMapper.createProject(project);
        if (Constants.NUMBER_ZERO.equals(result)) {
            throw new BusinessException(8010012);
        }

        //创建项目进度
        projectProgressService.createProgressByApproval(contractsTypeEnum, project, true);

        // 独立 触发项目立项事件
        log.info(":1.插入项目进度表--->2.推送财商系统接口");

        return project.getId();
    }

    @Override
    public ContractFileDetailResp<?> getFileDetailByBelongId(Long belongId) {
        final CivilMilitaryIntegrationDetailResp detail = this.getDetail(belongId);
        return new ContractFileDetailResp<>(detail);
    }

    @Override
    public Boolean hookProject(Long independentContractId, Integer independentContractType, Long belongId,
            String independentProject) {
        throw new FrameworkException(-1, "军民融合项目无需关联其他项目");
    }

    @Override
    public List<CivilMilitaryIntegrationResp> getFileDetail(Integer belongFileType, String belongId, String fileCode,
                                                            Long id, String yunshuExecuteUnitIdPath) {
        List<CivilMilitaryIntegration> civilMilitaryIntegration = this.baseMapper.selectList(Wrappers.<CivilMilitaryIntegration>lambdaQuery()
                .likeRight(CivilMilitaryIntegration::getYunshuExecuteUnitIdPath, yunshuExecuteUnitIdPath)
                .likeRight(StringUtils.isNotBlank(fileCode), CivilMilitaryIntegration::getFileCode, fileCode)
                .eq(StringUtils.isNotBlank(belongId), CivilMilitaryIntegration::getBelongId, belongId)
                .eq(Objects.nonNull(id), CivilMilitaryIntegration::getId, id)
                .orderByDesc(CivilMilitaryIntegration::getUpdateAt)
                .last("limit 20"));
        return BeanMapUtils.mapList(civilMilitaryIntegration, CivilMilitaryIntegration.class, CivilMilitaryIntegrationResp.class);
    }

    @Override
    public boolean updateRelationFileByBelongId(String curFileBelongId, Integer curFileType, String preFileId,
                                                Integer preFileType) {
        throw new FrameworkException(-1, "当前文件不支持关联其他文件");
    }

    @Override
    public boolean commonUpdateData(ContractFileUpdateParamReq paramReq, Long BelongId) {
        return false;
    }

    @Override
    public Map<String, Object> getMapByBelongId(Long belongId) {
        return getMap(
                Wrappers.<CivilMilitaryIntegration>lambdaQuery().eq(CivilMilitaryIntegration::getBelongId, belongId));
    }
}
