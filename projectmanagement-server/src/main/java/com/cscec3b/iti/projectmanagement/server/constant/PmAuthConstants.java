package com.cscec3b.iti.projectmanagement.server.constant;

/**
 * <AUTHOR>
 * @date 2022/10/19 10:05
 **/
public class PmAuthConstants {
    private PmAuthConstants() {
    }

    /**
     * 分隔符
     */
    public static final String SEPARATOR = ":";

    /**
     * 精益建造根节点
     */
    public static final String LEAN_BUILD_ROOT_PATH = "/精益建造管理平台组织";

    /**
     * 用户缓存信息 uc:userinfo:useruuid:jti <br>
     * 缓存时间为refreshToken的有效期 稍短
     *
     */
    public static final String CPM_USER_INFO = "cpm:token:userinfo:";

    /**
     * 用户accessToken 与refreshToken的映射<br>
     * 有效期为accessToken 稍长
     */
    public static final String CPM_JWT_INFO = "cpm:token:jwt:";


}
