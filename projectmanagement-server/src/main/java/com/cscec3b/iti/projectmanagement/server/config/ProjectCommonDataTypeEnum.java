package com.cscec3b.iti.projectmanagement.server.config;

import java.util.Arrays;

import lombok.Getter;

/**
 * 支持的项目数据标准类型枚举及本地文件存储路径
 *
 * <AUTHOR>
 * @date 2023/02/14 10:52
 **/
@Getter
public enum ProjectCommonDataTypeEnum {

    /**
     * 市场营销综合口径映射
     */
    COMMON_PROJECT_TYPE("", "project_type_config.json");

    private final String type;

    private final String fileName;

    ProjectCommonDataTypeEnum(String url, String fileName) {
        this.type = url;
        this.fileName = fileName;
    }

    public static boolean valid(String url) {
        return Arrays.stream(ProjectCommonDataTypeEnum.values()).anyMatch(typeEnum -> typeEnum.type.equals(url));
    }

}
