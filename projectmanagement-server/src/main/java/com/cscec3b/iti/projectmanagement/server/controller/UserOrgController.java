//package com.cscec3b.iti.projectmanagement.server.controller;
//
//import javax.annotation.Resource;
//
//import org.springframework.web.bind.annotation.RequestMapping;
//import org.springframework.web.bind.annotation.RestController;
//
//import com.cscec3b.iti.common.base.api.GenericityResponse;
//import com.cscec3b.iti.common.base.api.ResponseBuilder;
//import com.cscec3b.iti.projectmanagement.server.service.IPmAuthService;
//
//import io.swagger.annotations.Api;
//
///**
// * 用户组织操作相关
// */
//@Api(tags = {"用户组织操作相关"})
//@RestController
//@RequestMapping(IUserOrgController.PATH)
//public class UserOrgController implements IUserOrgController {
//
//	@Resource
//	private IPmAuthService pmAuthService;
//
//
//	/**
//	 * 切换组织
//	 * @param orgId
//	 * @return
//	 */
//	@Override
//	public GenericityResponse<PmUserInfoResp> changeOrg(String orgId) {
//		return ResponseBuilder.fromData(pmAuthService.changeOrg(orgId));
//	}
//
//
//}
