
package com.cscec3b.iti.projectmanagement.server.config;

import java.util.List;
import java.util.Map;

import org.springframework.boot.context.properties.ConfigurationProperties;
import org.springframework.cloud.context.config.annotation.RefreshScope;
import org.springframework.stereotype.Component;

import lombok.Data;

/**
 * 云枢客户端配置
 */
@Data
@RefreshScope
@ConfigurationProperties(prefix = "cscec.yun-shu")
@Component
public class YunShuConfig {

    /**
     * 云枢host
     */
    private String url;

    /**
     * 云枢appKey
     */
    private String appKey;

    /**
     * 云枢secret
     */
    private String secret;

    /**
     * 中建三局组织id
     */
    private String g3DeptId;

    /**
     * 实体机构列表
     */
    private List<Integer> entityOrg;

    /**
     * 项目部，指挥部
     */
    private List<Integer> headquarters;

    /**
     * 同步开关
     */
    private Boolean syncEnabled;

    /**
     * 同步线程数量
     */
    private int syncThreadPoolSize = 3;

    /**
     * 数据处理线程数量
     */
    private int processThreadPoolSize = 1;


    /**
     * 同步批量保存阀值数量
     */
    private int syncBatchSize = 1000;

    /**
     * api调用频率限制，单位秒
     */
    private int apiRateLimit = 10;

    private Map<String,String> smart;

}
