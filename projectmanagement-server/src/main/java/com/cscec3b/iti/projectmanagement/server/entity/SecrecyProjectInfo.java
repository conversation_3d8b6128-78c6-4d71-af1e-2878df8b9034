package com.cscec3b.iti.projectmanagement.server.entity;

import com.baomidou.mybatisplus.annotation.FieldFill;
import com.baomidou.mybatisplus.annotation.IdType;
import com.baomidou.mybatisplus.annotation.TableField;
import com.baomidou.mybatisplus.annotation.TableId;
import com.baomidou.mybatisplus.annotation.TableName;
import lombok.Data;
import lombok.experimental.Accessors;

import java.math.BigDecimal;

/**
 * 保密项目信息
 */
@Data
@Accessors(chain = true)
@TableName(value = "secrecy_project_info")
public class SecrecyProjectInfo {
    /**
     * 主键id
     */
    @TableId(value = "id", type = IdType.ASSIGN_ID)
    private Long id;

    /**
     * 项目级别
     */
    @TableField(value = "project_level")
    private String projectLevel;

    /**
     * 建设单位（甲方）联系人
     */
    @TableField(value = "contact_person")
    private String contactPerson;

    /**
     * 建设单位（甲方）联系人电话
     */
    @TableField(value = "contact_person_mobile")
    private String contactPersonMobile;

    /**
     * 行政区域（地理位置）
     */
    @TableField(value = "region")
    private String region;

    /**
     * 项目地址
     */
    @TableField(value = "project_address")
    private String projectAddress;

    /**
     * 投资主体
     */
    @TableField(value = "investors")
    private String investors;

    /**
     * 合同总金额（元）
     */
    @TableField(value = "contract_amount")
    private BigDecimal contractAmount;

    /**
     * 设计单位
     */
    @TableField(value = "designer")
    private String designer;

    /**
     * 监理单位
     */
    @TableField(value = "supervisor")
    private String supervisor;

    /**
     * 项目经理
     */
    @TableField(value = "project_manager")
    private String projectManager;

    /**
     * 总工期
     */
    @TableField(value = "count_days")
    private Integer countDays;

    /**
     * 客户名称
     */
    @TableField(value = "customer_name")
    private String customerName;

    /**
     * 上级相关方/客户母公司
     */
    @TableField(value = "superior_company_name")
    private String superiorCompanyName;

    /**
     * 签约主体
     */
    @TableField(value = "signed_subject_value")
    private String signedSubjectValue;

    /**
     * 签约主体Code
     */
    @TableField(value = "signed_subject_code")
    private String signedSubjectCode;

    /**
     * 实施单位
     */
    @TableField(value = "do_unit")
    private String doUnit;

    /**
     * 含税合同总价（RMB）
     */
    @TableField(value = "total_amount")
    private BigDecimal totalAmount;

    /**
     * 不含税合同总价（RMB）
     */
    @TableField(value = "no_tax_included_money")
    private BigDecimal noTaxIncludedMoney;

    /**
     * 自行施工不含税金额
     */
    @TableField(value = "mid_amount_self")
    private BigDecimal midAmountSelf;

    /**
     * 土建不含税金额
     */
    @TableField(value = "self_civil_amount")
    private BigDecimal selfCivilAmount;

    /**
     * 安装不含税金额
     */
    @TableField(value = "self_install_amount")
    private BigDecimal selfInstallAmount;

    /**
     * 钢结构不含税金额
     */
    @TableField(value = "self_steel_structure_amount")
    private BigDecimal selfSteelStructureAmount;

    /**
     * 总包服务费
     */
    @TableField(value = "self_total_service_amount")
    private BigDecimal selfTotalServiceAmount;

    /**
     * 其他
     */
    @TableField(value = "self_other_amount")
    private BigDecimal selfOtherAmount;

    /**
     * 销项税额
     */
    @TableField(value = "project_tax_amount")
    private BigDecimal projectTaxAmount;

    /**
     * 暂列金额或甲指分包金额
     */
    @TableField(value = "subcontract_amount")
    private BigDecimal subcontractAmount;

    /**
     * 工期奖罚条款
     */
    @TableField(value = "worker_reward_punish_appoint")
    private String workerRewardPunishAppoint;

    /**
     * 合同承包范围
     */
    @TableField(value = "contract_scope")
    private String contractScope;

    /**
     * 发包人指定分包、独立分包的工程
     */
    @TableField(value = "issuer_project")
    private String issuerProject;

    /**
     * 立项描述
     */
    @TableField(value = "project_desc")
    private String projectDesc;

    /**
     * 云枢执行单位名称
     */
    @TableField(value = "yunshu_execute_unit")
    private String yunshuExecuteUnit;

    /**
     * 云枢执行单位code
     */
    @TableField(value = "yunshu_execute_unit_code")
    private String yunshuExecuteUnitCode;

    /**
     * 云枢执行单位Id
     */
    @TableField(value = "yunshu_execute_unit_id")
    private String yunshuExecuteUnitId;

    /**
     * 云枢执行单位id_path
     */
    @TableField(value = "yunshu_execute_unit_id_path")
    private String yunshuExecuteUnitIdPath;

    /**
     * 云枢执行单位简称
     */
    @TableField(value = "yunshu_execute_unit_abbreviation")
    private String yunshuExecuteUnitAbbreviation;

    /**
     * 项目效果图
     */
    @TableField(value = "effect_pic")
    private String effectPic;

    /**
     * 文件类型：6：保密项目； 7：军民融合项目
     */
    @TableField(value = "belong_file_type")
    private Byte belongFileType;

    /**
     * 创建时间
     */
    @TableField(value = "create_at", fill = FieldFill.INSERT)
    private Long createAt;

    /**
     * 更新时间
     */
    @TableField(value = "update_at", fill = FieldFill.INSERT_UPDATE)
    private Long updateAt;

    /**
     * 局标准分类codePath
     */
    @TableField(value = "standard_type_code_path")
    private String standardTypeCodePath;

    /**
     * 业务版块codePath
     */
    @TableField(value = "business_segment_code_path")
    private String businessSegmentCodePath;

    /**
     * 工程名称
     */
    @TableField(value = "project_name")
    private String projectName;

    /**
     * 所属源文件id
     */
    @TableField(value = "belong_id")
    private Long belongId;

    /**
     * 独立性判断,Y:是独立,N:否
     */
    @TableField(value = "is_independent")
    private String independent;

    /**
     * 独立合同ID
     */
    @TableField(value = "independent_contract_id")
    private Long independentContractId;

    /**
     * 独立合同类型：1投标总结；2合同定案；3补充协议；4局内部合同定案；5局内部补充协议；
     */
    @TableField(value = "independent_contract_type")
    private Integer independentContractType;

    /**
     * 文件编码
     */
    @TableField(value = "file_code")
    private String fileCode;

    /**
     * 实际中标日期
     */
    @TableField(value = "successful_time")
    private Long successfulTime;

    /**
     * 实际签约日期
     */
    @TableField(value = "actual_signed_time")
    private Long actualSignedTime;

    /**
     * 实际进场日期
     */
    @TableField(value = "real_enter_time")
    private Long realEnterTime;

    /**
     * 实际竣工日期
     */
    @TableField(value = "work_end_time")
    private Long workEndTime;

    /**
     * 合同开工日期
     */
    @TableField(value = "worker_begin_time")
    private Long workerBeginTime;

    /**
     * 合同竣工日期
     */
    @TableField(value = "worker_end_time")
    private Long workerEndTime;

    /**
     * 实际开工日期
     */
    @TableField(value = "real_work_begin_time")
    private Long realWorkBeginTime;

    /**
     * 预计实际竣工日期
     */
    @TableField(value = "predict_work_end_time")
    private Long predictWorkEndTime;

    /**
     * 区域 ID 路径
     */
    @TableField(value = "region_id_path")
    private String regionIdPath;

    /**
     * 批准状态
     */
    @TableField(value = "approval_status")
    private Integer approvalStatus;

    /**
     * 审批开始时间
     */
    @TableField(value = "approval_begin_time")
    private Long approvalBeginTime;

    /**
     * 业务板块
     */
    @TableField(value = "financial_business_segment")
    private String financialBusinessSegment;

    /**
     * 工程类型(局标准类型)
     */
    @TableField(value = "standard_type")
    private String standardType;

    /**
     * 新增人
     */
    @TableField(value = "create_by", fill = FieldFill.INSERT)
    private String createBy;

    /**
     * 更新者
     */
    @TableField(value = "update_by", fill = FieldFill.INSERT_UPDATE)
    private String updateBy;
}