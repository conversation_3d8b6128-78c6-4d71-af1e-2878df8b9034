package com.cscec3b.iti.projectmanagement.server.pushservice.event;

import cn.hutool.core.bean.BeanUtil;
import com.cscec3b.iti.common.base.json.JsonUtils;
import com.cscec3b.iti.common.web.exception.BusinessException;
import com.cscec3b.iti.common.web.exception.FrameworkException;
import com.cscec3b.iti.model.resp.ContractFileRelationResp;
import com.cscec3b.iti.model.resp.MarketingSegment;
import com.cscec3b.iti.model.resp.ProjectArchiveResp;
import com.cscec3b.iti.model.resp.SmartSiteSegment;
import com.cscec3b.iti.projectmanagement.server.constant.Constants;
import com.cscec3b.iti.projectmanagement.server.entity.Project;
import com.cscec3b.iti.projectmanagement.server.entity.ProjectFlowEventRecord;
import com.cscec3b.iti.projectmanagement.server.entity.YunshuOrgSync;
import com.cscec3b.iti.projectmanagement.server.entity.dto.ProjectFlowEventSubscribeDto;
import com.cscec3b.iti.projectmanagement.server.pushservice.enums.FlowNodeDataTypeEnum;
import com.cscec3b.iti.projectmanagement.server.pushservice.enums.FlowNodeEnum;
import com.cscec3b.iti.projectmanagement.server.pushservice.enums.FlowNodeHandlerEnum;
import com.cscec3b.iti.projectmanagement.server.pushservice.enums.ProjectEventEnum;
import com.cscec3b.iti.projectmanagement.server.pushservice.service.IEventCallbackService;
import com.cscec3b.iti.projectmanagement.server.pushservice.service.IProjectEventService;
import com.cscec3b.iti.projectmanagement.server.pushservice.service.IProjectEventSubscriberService;
import com.cscec3b.iti.projectmanagement.server.pushservice.service.IProjectFlowEventService;
import com.cscec3b.iti.projectmanagement.server.service.IYunshuOrgSyncService;
import com.cscec3b.iti.projectmanagement.server.service.ProjectService;
import com.cscec3b.iti.projectmanagement.server.util.LoginUserUtil;
import com.g3.G3OrgService;
import com.g3.org.api.dto.resp.CloudPivotResponse;
import com.google.common.base.Joiner;
import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.collections4.CollectionUtils;
import org.apache.commons.lang3.StringUtils;
import org.apache.shenyu.common.utils.UUIDUtils;
import org.springframework.amqp.rabbit.annotation.RabbitListener;
import org.springframework.amqp.rabbit.core.RabbitTemplate;
import org.springframework.context.event.EventListener;
import org.springframework.scheduling.annotation.Async;
import org.springframework.stereotype.Component;

import java.time.Instant;
import java.util.List;
import java.util.Objects;
import java.util.Optional;
import java.util.concurrent.Executor;

import static com.cscec3b.iti.projectmanagement.server.constant.MqConstants.*;

/**
 * 事件监听器
 *
 * <AUTHOR>
 * @date 2023/04/14 16:35
 **/

@Slf4j
@Component
@RequiredArgsConstructor
public class ProjectEventListener {

    private final IProjectEventService projectEventService;

    private final Executor cpmPushTaskExecutor;

    private final IProjectFlowEventService flowEventService;

    private final IEventCallbackService eventCallbackService;


    private final IProjectEventSubscriberService subscriberService;

    private final IYunshuOrgSyncService yunshuOrgSyncService;

    private final RabbitTemplate rabbitTemplate;

    private final ProjectService projectService;

    private final G3OrgService g3OrgService;


    @RabbitListener(queues = PROJECT_EVENT_BUSINESS_QUEUE)
    @Async("cpmTaskExecutor")
    public void projectEventListener(CpmProjectFlowEventForMqEvent event) throws RuntimeException {
        log.info("监听到事件消息：{}", JsonUtils.toJsonStr(event));
        try {
            final FlowNodeEnum nodeEnum = event.getNodeEnum();
            final FlowNodeHandlerEnum handlerEnum = event.getHandlerEnum();
            final FlowNodeDataTypeEnum dataTypeEnum = event.getDataTypeEnum();
            // 获取触发点数据
            final ProjectFlowEventRecord eventRecord =
                    genListenerRecordV2(event, nodeEnum,
                    handlerEnum, dataTypeEnum);
            eventRecord.setManual(event.getManual());
            flowEventService.saveFlowEventRecord(eventRecord);

            // 获取对应消费者
            final List<ProjectFlowEventSubscribeDto> subscribers = subscriberService.getFlowEventSubscribers(nodeEnum,
                    handlerEnum, dataTypeEnum, event.getCustomerId());
            if (CollectionUtils.isNotEmpty(subscribers)) {
                flowEventService.handlerV2(event.getProjectId(), nodeEnum, handlerEnum, eventRecord.getId(), subscribers);
            }
        } catch (Exception e) {
            log.error("消息消费失败: ", e);
            throw new RuntimeException(e);
        }
    }


    /**
     * 项目中心变更事件监听 只用来推送智慧工地事件
     *
     * @param event
     */// @Async("cpmTaskExecutor")
    @EventListener
    public void projectUpdateEventListener(CpmProjectUpdateEvent event) {
        final ProjectEventEnum eventEnum = event.getEventEnum();
        // 项目中心更新事件
        if (!ProjectEventEnum.CPM_UPDATE.equals(eventEnum)) {
            return;
        }
        log.info("监听到项目中心【{}】事件: ", eventEnum.getZhCN());
        cpmPushTaskExecutor.execute(() -> projectEventService.getSubscriber2SendMsgByEvent(eventEnum,
                event.getProjectId(), event.getExcludes()));
    }


    /**
     * 工地立项完成 ，工地更新事件 ，财商立项完成，财商更新事件 监听 <br>
     * 用于同步集团主数据编码，挂接项目不需要同步 暂只监听标准立项事件
     *
     * @param event 事件
     */
//    @EventListener(condition =
//            "(#event.nodeEnum == T(com.cscec3b.iti.projectmanagement.server.pushservice.enums.FlowNodeEnum)" +
//                    ".SMART_SITE_SEGMENT && " +
//                    "#event.handlerEnum == T(com.cscec3b.iti.projectmanagement.server.pushservice.enums" +
//                    ".FlowNodeHandlerEnum).POST && " +
//                    "#event.dataTypeEnum == T(com.cscec3b.iti.projectmanagement.server.pushservice.enums" +
//                    ".FlowNodeDataTypeEnum).CREATE) " +
//                    "|| (#event.nodeEnum == T(com.cscec3b.iti.projectmanagement.server.pushservice.enums" +
//                    ".FlowNodeEnum).FINANCE_SEGMENT &&" +
//                    "#event.handlerEnum == T(com.cscec3b.iti.projectmanagement.server.pushservice.enums" +
//                    ".FlowNodeHandlerEnum).POST &&" +
//                    "#event.dataTypeEnum == T(com.cscec3b.iti.projectmanagement.server.pushservice.enums" +
//                    ".FlowNodeDataTypeEnum).CREATE)" +
//                    "|| (#event.nodeEnum == T(com.cscec3b.iti.projectmanagement.server.pushservice.enums" +
//                    ".FlowNodeEnum).SMART_SITE_SEGMENT_UPDATE &&" +
//                    "#event.handlerEnum == T(com.cscec3b.iti.projectmanagement.server.pushservice.enums" +
//                    ".FlowNodeHandlerEnum).POST &&" +
//                    "#event.dataTypeEnum == T(com.cscec3b.iti.projectmanagement.server.pushservice.enums" +
//                    ".FlowNodeDataTypeEnum).UPDATE)" +
//                    "|| (#event.nodeEnum == T(com.cscec3b.iti.projectmanagement.server.pushservice.enums" +
//                    ".FlowNodeEnum).FINANCE_SEGMENT_UPDATE &&" +
//                    "#event.handlerEnum == T(com.cscec3b.iti.projectmanagement.server.pushservice.enums" +
//                    ".FlowNodeHandlerEnum).POST &&" +
//                    "#event.dataTypeEnum == T(com.cscec3b.iti.projectmanagement.server.pushservice.enums" +
//                    ".FlowNodeDataTypeEnum).UPDATE)")
    @EventListener(condition = "#event.nodeEnum == T(com.cscec3b.iti.projectmanagement.server.pushservice.enums" +
            ".FlowNodeEnum).FINANCE_SMART_SITE_APPROVAL && " +
            "#event.handlerEnum == T(com.cscec3b.iti.projectmanagement.server.pushservice.enums.FlowNodeHandlerEnum)" +
            ".POST && #event.dataTypeEnum == T(com.cscec3b.iti.projectmanagement.server.pushservice.enums" +
            ".FlowNodeDataTypeEnum).CREATE ")
    public void mainDataSyncEvent(CpmProjectFlowEvent event) {
        log.info("监听到主数据【{}】事件: {}", event.getProjectId(), event.getNodeEnum().getName());
        long id = event.getProjectId();
        Project project = projectService.selectById(id);
        String yunshuOrgId = project.getYunshuOrgId();
        String projectFinanceCode = project.getProjectFinanceCode();
        log.info("当前项目信息：云枢id：{}， 财商编码：{}", yunshuOrgId, projectFinanceCode);
        if (StringUtils.isNotBlank(yunshuOrgId) && StringUtils.isNotBlank(projectFinanceCode)) {
            try {
                String updateBy =
                        Optional.ofNullable(LoginUserUtil.userIdOrNull()).orElse(event.getNodeEnum().getName());
                log.info("更新集团主数据编码：云枢id：{}， 财商编码：{}, 更新人：{}", yunshuOrgId, projectFinanceCode, updateBy);
                CloudPivotResponse<Boolean> response = g3OrgService.executeForUpdateCompanyOrgCode(yunshuOrgId,
                        projectFinanceCode, updateBy);
                if (!response.isSuccess()) {
                    log.error("更新集团主数据编码失败：{}", response.getErrmsg());
                }
                log.info("更新集团主数据编码结果：{}", JsonUtils.toJsonStr(response));
            } catch (Exception e) {
                log.error("更新集团主数据编码发生异常：", e);
            }
        }
    }


    @EventListener
    public void projectFlowEventListenerV2(CpmProjectFlowEvent event) {
        final CpmProjectFlowEventForMqEvent mqEvent = BeanUtil.copyProperties(event,
                CpmProjectFlowEventForMqEvent.class);
        rabbitTemplate.convertAndSend(PROJECT_EVENT_EXCHANGE_NAME, PROJECT_EVENT_BUSINESS_ROUTE_KEY, mqEvent,
                msg -> {
                    msg.getMessageProperties().setDelay(10000);
                    return msg;
                });

        // cpmScheduledExecutor.schedule(() -> {
        //     log.info("触发消息：{}", JsonUtils.toJsonStr(event));
        //     final FlowNodeEnum nodeEnum = event.getNodeEnum();
        //     final FlowNodeHandlerEnum handlerEnum = event.getHandlerEnum();
        //     final FlowNodeDataTypeEnum dataTypeEnum = event.getDataTypeEnum();
        //     // 获取触发点数据
        //     final ProjectFlowEventRecord eventRecord = this.genListenerRecordV2(event.getProjectId(), nodeEnum,
        //             handlerEnum, dataTypeEnum);
        //     eventRecord.setManual(event.getManual());
        //     flowEventService.saveFlowEventRecord(eventRecord);
        //
        //     // 获取对应消费者
        //     final List<ProjectFlowEventSubscribeDto> subscribers = subscriberService.getFlowEventSubscribers
        //     (nodeEnum,
        //             handlerEnum, dataTypeEnum, event.getCustomerId());
        //     if (CollectionUtils.isEmpty(subscribers)) {
        //         log.warn("未查询到对应消费者信息 :{}", event.getProjectId());
        //         return;
        //     }
        //     // 延时推送
        //     cpmScheduledExecutor.execute(() -> flowEventService.handlerV2(event.getProjectId(), nodeEnum,
        //     handlerEnum,
        //             eventRecord.getId(), subscribers));
        // }, 5 * 1000L, TimeUnit.MILLISECONDS);
    }


    /**
     * 生成及保存消息
     *
     * @param event    事件信息
     * @param nodeEnum     节点信息
     * @param handlerEnum  触发位置（前后）
     * @param dataTypeEnum 数据类型
     * @return {@link String }
     */
    public ProjectFlowEventRecord genListenerRecordV2(CpmProjectFlowEventForMqEvent event, FlowNodeEnum nodeEnum,
            FlowNodeHandlerEnum handlerEnum, FlowNodeDataTypeEnum dataTypeEnum) {
        Long projectId = event.getProjectId();
        log.info("project flow listener : {}, {}, {}, {}", projectId, nodeEnum.getName(), handlerEnum.getName(),
                dataTypeEnum.getName());
        // 查询并匹配结果 无论更新，立项都要先查询数据(同步执行，在同一个事务内)
        ProjectArchiveResp projectArchive = flowEventService.getProjectArchive(projectId);
        if (Objects.isNull(projectArchive)) {
            throw new FrameworkException(-1, "未能获取到事件消息内容，请检查项目信息是否保存");
        }
        projectArchive.setEngineProjectSegment(event.getTransferSegment());
        if (FlowNodeDataTypeEnum.UPDATE.equals(dataTypeEnum)) {
            projectArchive = eventCallbackService.getProjectArchiveUpdateSegment(projectId, nodeEnum, projectArchive);
        }
        // 填充云枢相关信息
        final SmartSiteSegment smartSiteSegment = projectArchive.getSmartSiteSegment();
        final String yunshuOrgName = Optional.ofNullable(smartSiteSegment).map(SmartSiteSegment::getYunshuOrgId)
                .map(yunshuOrgSyncService::getYunshuOrgByDeptId).map(YunshuOrgSync::getName)
                .orElse(null);
        if (StringUtils.isNotBlank(yunshuOrgName)) {
            smartSiteSegment.setYunshuOrgName(yunshuOrgName);
        }
        // 填充文件列表信息
        MarketingSegment marketingSegment = projectArchive.getMarketingSegment();
        Integer independentContractType = projectArchive.getIndependentContractType();
        Long independentContractId = projectArchive.getIndependentContractId();
        List<ContractFileRelationResp> contractFileRelationResps = Optional.ofNullable(marketingSegment)
                .map(archive ->
                        projectService.projectContractRelationList(independentContractType, independentContractId))
                .orElse(null);
        if (CollectionUtils.isNotEmpty(contractFileRelationResps)) {
            marketingSegment.setContractFileList(contractFileRelationResps);
        }
        // 保存事件信息
        final ProjectFlowEventRecord flowEventRecord = new ProjectFlowEventRecord();
        final String id = UUIDUtils.getInstance().generateShortUuid();
        flowEventRecord.setId(id).setProjectId(projectId)
                .setFlowNodeCode(nodeEnum.getCode()).setFlowHandlerCode(handlerEnum.getCode())
                .setFlowDataTypeCode(dataTypeEnum.getCode()).setProjectArchive(JsonUtils.toJsonStr(projectArchive))
                .setCreateAt(Instant.now().toEpochMilli());
        return flowEventRecord;
    }

    // /**
    //  * 保存事件触发点的消息
    //  *
    //  * @param projectId   项目id
    //  * @param nodeEnum    节点信息
    //  * @param handlerEnum 位置位置信息
    //  * @return {@link String } 消息id
    //  * <AUTHOR>
    //  * @date 2023/09/18
    //  */
    // public ProjectFlowEventRecord getListenerRecord(Long projectId, FlowNodeEnum nodeEnum,
    //         FlowNodeHandlerEnum handlerEnum) {
    //     log.debug("project flow listener : {}, {}, {}", projectId, nodeEnum.getName(), handlerEnum.getName());
    //     // 查询并匹配结果 无论更新，立项都要先查询数据(同步执行，在同一个事务内)
    //     final ProjectArchiveResp projectArchive = flowEventService.getProjectArchive(projectId);
    //     if (Objects.isNull(projectArchive)) {
    //         return null;
    //     }
    //     // 保存事件信息
    //     final ProjectFlowEventRecord flowEventRecord = new ProjectFlowEventRecord();
    //     flowEventRecord.setId(UUIDUtils.getInstance().generateShortUuid()).setProjectId(projectId)
    //             .setFlowNodeCode(nodeEnum.getCode()).setFlowHandlerCode(handlerEnum.getCode())
    //             .setProjectArchive(JsonUtils.toJsonStr(projectArchive)).setCreateAt(Instant.now().toEpochMilli());
    //     return flowEventRecord;
    // }

    /**
     * 提前生成事件推送数据
     *
     * @param projectId    项目id
     * @param flowNodeCode 项目流转节点
     * @param subscriberId 业务系统id
     * @param type         事件类型
     * @return {@link ProjectArchiveResp}
     */
    public ProjectArchiveResp generateEventPushDataInAdvance(final Long projectId,
            final String type, final String flowNodeCode, final Long subscriberId) {
        FlowNodeEnum nodeEnum = FlowNodeEnum.getByCode(flowNodeCode);
        final FlowNodeDataTypeEnum dataTypeEnum = FlowNodeDataTypeEnum.getByCode(type);

        final List<ProjectFlowEventSubscribeDto> subscribeDtoList = subscriberService.getFlowEventSubscribers(nodeEnum,
                FlowNodeHandlerEnum.POST, dataTypeEnum, String.valueOf(subscriberId));
        // 判断业务系统是否有订阅对应板块的更新事件
        if (CollectionUtils.isEmpty(subscribeDtoList)) {
            throw new BusinessException(80107006, new String[]{nodeEnum.getName()});
        }
        CpmProjectFlowEventForMqEvent event = new CpmProjectFlowEventForMqEvent();
        event.setProjectId(projectId);
        final ProjectFlowEventRecord eventRecord = genListenerRecordV2(event, nodeEnum, FlowNodeHandlerEnum.POST,
                dataTypeEnum);
        return JsonUtils.readValue(eventRecord.getProjectArchive(), ProjectArchiveResp.class);
    }


    /**
     * 手动推送事件信息
     *
     * @param projectId    项目id
     * @param flowNodeCode 项目流转节点
     * @param subscriberId 业务系统id
     * @param type         事件类型
     * @return {@link String}
     */
    public String manualPushEvent(final Long projectId, final String type, final String flowNodeCode,
            final Long subscriberId) {


        final FlowNodeEnum nodeEnum = FlowNodeEnum.getByCode(flowNodeCode);
        final FlowNodeDataTypeEnum dataTypeEnum = FlowNodeDataTypeEnum.getByCode(type);

        final CpmProjectFlowEvent flowEvent = new CpmProjectFlowEvent(this, projectId, nodeEnum,
                FlowNodeHandlerEnum.POST, dataTypeEnum, String.valueOf(subscriberId));
        flowEvent.setManual(Constants.NUMBER_ONE);

        projectFlowEventListenerV2(flowEvent);
        return Joiner.on("-").join(subscriberId, projectId);

    }

    /**
     * @param projectId    重新推送事件
     * @param subscribeId  消费者信息
     * @param flowNodeEnum 节点信息
     * @param handlerEnum  触发位置（前后）
     */
    public void restartPushEvent(final Long projectId, final Long subscribeId, final FlowNodeEnum flowNodeEnum,
            final FlowNodeHandlerEnum handlerEnum, FlowNodeDataTypeEnum dataTypeEnum) {
        final CpmProjectFlowEvent flowEvent = new CpmProjectFlowEvent(this, projectId, flowNodeEnum,
                handlerEnum, dataTypeEnum, String.valueOf(subscribeId));
        flowEvent.setManual(Constants.NUMBER_ONE);
        projectFlowEventListenerV2(flowEvent);
    }

}
