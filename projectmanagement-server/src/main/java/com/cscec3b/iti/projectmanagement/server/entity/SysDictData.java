package com.cscec3b.iti.projectmanagement.server.entity;

import com.baomidou.mybatisplus.annotation.FieldFill;
import com.baomidou.mybatisplus.annotation.TableField;
import com.baomidou.mybatisplus.annotation.TableId;
import com.baomidou.mybatisplus.annotation.TableName;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;
import lombok.experimental.Accessors;

/**
 * 字典数据表
 */
@ApiModel(description = "字典数据表")
@Data
@TableName(value = "sys_dict_data")
@Accessors(chain = true)
public class SysDictData {
    /**
     * 字典编码
     */
    @TableId(value = "dict_code")
    @ApiModelProperty(value = "字典编码")
    private Long dictCode;

    /**
     * 字典排序
     */
    @TableField(value = "dict_sort")
    @ApiModelProperty(value = "字典排序")
    private Integer dictSort;

    /**
     * 字典标签(名称)
     */
    @TableField(value = "dict_label")
    @ApiModelProperty(value = "字典标签(名称)")
    private String dictLabel;

    /**
     * 字典键值
     */
    @TableField(value = "dict_value")
    @ApiModelProperty(value = "字典键值")
    private String dictValue;

    /**
     * 上级value
     */
    @TableField(value = "parent_code")
    @ApiModelProperty(value = "上级value")
    private Long parentCode;

    /**
     * 字典类型
     */
    @TableField(value = "dict_type")
    @ApiModelProperty(value = "字典类型")
    private String dictType;

    /**
     * 是否默认（1是 0否）
     */
    @TableField(value = "is_default")
    @ApiModelProperty(value = "是否默认（1是 0否）")
    private Integer byDefault;

    /**
     * 状态: 0正常; 时间戳 删除时间
     */
    @TableField(value = "deleted")
    @ApiModelProperty(value = "状态: 0正常; 时间戳 删除时间")
    private Long deleted;

    /**
     * 创建者
     */
    @TableField(value = "create_by", fill = FieldFill.INSERT)
    @ApiModelProperty(value = "创建者")
    private String createBy;

    /**
     * 创建时间
     */
    @TableField(value = "create_at", fill = FieldFill.INSERT)
    @ApiModelProperty(value = "创建时间")
    private Long createAt;

    /**
     * 更新者
     */
    @TableField(value = "update_by", fill = FieldFill.INSERT_UPDATE)
    @ApiModelProperty(value = "更新者")
    private String updateBy;

    /**
     * 更新时间
     */
    @TableField(value = "update_at", fill = FieldFill.INSERT_UPDATE)
    @ApiModelProperty(value = "更新时间")
    private Long updateAt;

    /**
     * 备注
     */
    @TableField(value = "remark")
    @ApiModelProperty(value = "备注")
    private String remark;

    /**
     * 层级
     */
    @TableField(value = "`level`")
    @ApiModelProperty(value = "层级")
    private Integer level;
}