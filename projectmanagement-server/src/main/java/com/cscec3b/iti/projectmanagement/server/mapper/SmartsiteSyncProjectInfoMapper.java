package com.cscec3b.iti.projectmanagement.server.mapper;

import org.apache.ibatis.annotations.Mapper;
import org.apache.ibatis.annotations.Param;

import com.cscec3b.iti.projectmanagement.server.entity.SmartsiteSyncProjectInfo;

/**
 * 智慧工地同步数据
 *
 * <AUTHOR>
 */
@Mapper
public interface SmartsiteSyncProjectInfoMapper {
    /**
     * 保存智慧工地同步的数据
     * @param record 记录
     * @return int
     */
    int insertOrUpdate(SmartsiteSyncProjectInfo record);
    
    /**
     * 批量更新或保存记录
     * @param record  记录
     * @return int
     */
    int insertOrUpdateSelective(SmartsiteSyncProjectInfo record);

    /**
     * 根据项目id获取记录
     *
     * @param projectId
     * @return {@link SmartsiteSyncProjectInfo }
     */
    SmartsiteSyncProjectInfo getById(@Param("projectId") Long projectId);
}
