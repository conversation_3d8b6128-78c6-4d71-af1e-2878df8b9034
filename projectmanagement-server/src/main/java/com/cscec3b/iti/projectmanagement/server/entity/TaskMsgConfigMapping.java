package com.cscec3b.iti.projectmanagement.server.entity;

import com.baomidou.mybatisplus.annotation.FieldFill;
import com.baomidou.mybatisplus.annotation.IdType;
import com.baomidou.mybatisplus.annotation.TableField;
import com.baomidou.mybatisplus.annotation.TableId;
import com.baomidou.mybatisplus.annotation.TableLogic;
import com.baomidou.mybatisplus.annotation.TableName;

import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

/**
 * 待办任务配置与人员关联
 */
@ApiModel(description = "待办任务配置与人员关联")
@Data
@TableName(value = "task_msg_config_mapping")
public class TaskMsgConfigMapping {
    /**
     * id
     */
    @TableId(value = "id", type = IdType.ASSIGN_ID)
    @ApiModelProperty(value = "id")
    private Long id;

    /**
     * 待办任务配置id
     */
    @TableField(value = "task_msg_config_id")
    @ApiModelProperty(value = "待办任务配置id")
    private Long taskMsgConfigId;

    /**
     * 组织id
     */
    @TableField(value = "org_id")
    @ApiModelProperty(value = "组织id")
    private String orgId;

    /**
     * 选人类型：1: 组织角色； 2:组织
     */
    @TableField(value = "user_chose_type")
    @ApiModelProperty(value = "选人类型：1: 组织角色； 2:组织")
    private Integer userChoseType;

    /**
     * 目标用户
     */
    @TableField(value = "target_users")
    @ApiModelProperty(value = "目标用户")
    private String targetUsers;

    /**
     * 重试周期
     */
    @TableField(value = "retry_cycle")
    @ApiModelProperty(value = "重试周期")
    private String retryCycle;

    /**
     * 是否删除 0:否; 1:是;
     */
    @TableField(value = "deleted")
    @TableLogic(value = "0", delval = "UNIX_TIMESTAMP() * 1000")
    @ApiModelProperty(value = "是否删除 0:否; 时间戳为删除时间")
    private Long deleted;

    /**
     * 创建时间
     */
    @TableField(value = "create_at", fill = FieldFill.INSERT)
    @ApiModelProperty(value = "创建时间")
    private Long createAt;

    /**
     * 创建人
     */
    @TableField(value = "create_by", fill = FieldFill.INSERT)
    @ApiModelProperty(value = "创建人")
    private String createBy;

    /**
     * 更新人
     */
    @TableField(value = "update_at", fill = FieldFill.INSERT_UPDATE)
    @ApiModelProperty(value = "更新人")
    private Long updateAt;

    /**
     * 更新人
     */
    @TableField(value = "update_by", fill = FieldFill.INSERT_UPDATE)
    @ApiModelProperty(value = "更新人")
    private String updateBy;
}