package com.cscec3b.iti.projectmanagement.server.controller;

import javax.servlet.http.HttpServletResponse;

import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RestController;

import com.cscec3b.iti.common.base.api.GenericityResponse;
import com.cscec3b.iti.common.base.api.ResponseBuilder;
import com.cscec3b.iti.common.base.page.Page;
import com.cscec3b.iti.logger.annotations.Logger;
import com.cscec3b.iti.projectmanagement.api.IOperationAndMaintenanceApi;
import com.cscec3b.iti.projectmanagement.api.dto.dto.xindun.YunshuOrgSyncResp;
import com.cscec3b.iti.projectmanagement.api.dto.request.OMCreateOrgReq;
import com.cscec3b.iti.projectmanagement.api.dto.request.OMInitProjectSaveReq;
import com.cscec3b.iti.projectmanagement.api.dto.request.OMInitProjectUpdateReq;
import com.cscec3b.iti.projectmanagement.api.dto.request.OMProjectInitReq;
import com.cscec3b.iti.projectmanagement.api.dto.request.OMProjectUpdateReq;
import com.cscec3b.iti.projectmanagement.api.dto.request.event.ProjectEventFlowReq;
import com.cscec3b.iti.projectmanagement.api.dto.request.file.OMBidApprovalReq;
import com.cscec3b.iti.projectmanagement.api.dto.request.file.OMBidSummaryReq;
import com.cscec3b.iti.projectmanagement.api.dto.request.file.OMBureauContractReq;
import com.cscec3b.iti.projectmanagement.api.dto.request.file.OMBureauSupplementaryAgreementReq;
import com.cscec3b.iti.projectmanagement.api.dto.request.file.OMContractReq;
import com.cscec3b.iti.projectmanagement.api.dto.request.file.OMSupplementaryAgreementReq;
import com.cscec3b.iti.projectmanagement.api.dto.request.project.ProjectQueryParams;
import com.cscec3b.iti.projectmanagement.api.dto.response.OMInitProjectResp;
import com.cscec3b.iti.projectmanagement.api.dto.response.event.ProjectFlowEventLogResp;
import com.cscec3b.iti.projectmanagement.server.service.IOMService;
import com.g3.org.api.dto.resp.org.AddDepartmentResp;

import io.swagger.annotations.Api;

/**
 * 运维支持API
 *
 * <AUTHOR>
 * @date 2023/07/25 15:31
 **/

@RestController
@RequestMapping(IOperationAndMaintenanceApi.PATH)
@Api(tags = "运维支持API")
public class OMController implements IOperationAndMaintenanceApi {

    private final IOMService omService;

	public OMController(IOMService omService) {
        this.omService = omService;
    }

    /**
     * 插入项目信息
     * @param req OMInitProjectSaveReq
     * @return
     */
    @Override
    @Logger
    public GenericityResponse<Long> saveProject(OMInitProjectSaveReq req) {
        return ResponseBuilder.fromData(omService.initProject(req));
    }

    /**
     *
     * @param id id
     * @return
     */
    @Override
    public GenericityResponse<OMInitProjectResp> getProject(Long id) {
        return ResponseBuilder.fromData(omService.getInitProject(id));
    }

    @Override
    @Logger
    public GenericityResponse<Boolean> updateProject(OMInitProjectUpdateReq req) {
        return ResponseBuilder.fromData(omService.updateInitProject(req));
    }

    @Override
    @Logger
    public GenericityResponse<Boolean> saveProjectProgress(Long projectId) {
        return ResponseBuilder.fromData(omService.saveProjectProgress(projectId));
    }

    @Override
    public GenericityResponse<Boolean> batchAddProjectDeptIdPath() {
        return ResponseBuilder.fromData(omService.batchAddProjectDeptIdPath());
    }

    @Override
    public GenericityResponse<Boolean> restartSmartProcess(final Long subscribeId, Long projectId) {
        return ResponseBuilder.fromData(omService.restartSmartProcess(subscribeId, projectId));
    }

    @Override
    public GenericityResponse<Boolean> restartFinanceProcess(Long subscribeId, Long projectId) {
	    return ResponseBuilder.fromData(omService.restartFinanceProcess(subscribeId, projectId));
    }

//    @Override
//    public GenericityResponse<Boolean> restartUcProcess(Long projectId) {
//        return ResponseBuilder.fromData(omService.restartUcProcess(projectId));
//    }

    @Logger
    @Override
    public GenericityResponse<Boolean> reCacheYunshuOrg(String parentId) {
        return ResponseBuilder.fromData(omService.reCacheYunshuOrg(parentId));
    }

    @Override
    public GenericityResponse<Page<ProjectFlowEventLogResp>> getFlowEventRecord(ProjectEventFlowReq req) {
        return ResponseBuilder.fromData(omService.getFlowEventRecord(req));
    }

    @Override
    public GenericityResponse<Long> initProject(OMProjectInitReq initReq) {
        return ResponseBuilder.fromData(omService.insertProject(initReq));
    }

    @Override
    public GenericityResponse<Boolean> updateProject(OMProjectUpdateReq updateReq) {
        return ResponseBuilder.fromData(omService.updateProject(updateReq));
    }

    @Override
    public GenericityResponse<Boolean> updateTenderSummaryFile(OMBidSummaryReq summaryReq) {
        return ResponseBuilder.fromData(omService.updateTenderSummaryFile(summaryReq));
    }

    @Override
    public GenericityResponse<Boolean> updateContractFile(OMContractReq contractReq) {
        return ResponseBuilder.fromData(omService.updateContractFile(contractReq));
    }

    @Override
    public GenericityResponse<Boolean> updateBureauContractFile(OMBureauContractReq bureauContractReq) {
        return ResponseBuilder.fromData(omService.updateBureauContractFile(bureauContractReq));
    }

    @Override
    public GenericityResponse<Boolean> updateBureauSupplementaryAgreementFile(
            OMBureauSupplementaryAgreementReq agreementReq) {
        return ResponseBuilder.fromData(omService.updateBureauSupplementaryAgreementFile(agreementReq));
    }

    @Override
    public GenericityResponse<Boolean> updateAgreementFile(OMSupplementaryAgreementReq agreementReq) {
        return ResponseBuilder.fromData(omService.updateAgreementFile(agreementReq));
    }

    @Override
    public GenericityResponse<Boolean> updateBid(OMBidApprovalReq approvalReq) {
        return ResponseBuilder.fromData(omService.updateBid(approvalReq));
    }

    @Override
    public void exportAllProject(HttpServletResponse response, ProjectQueryParams queryParams) {
        omService.exportAllProject(response, queryParams);
    }


    @Logger
    @Override
    public GenericityResponse<AddDepartmentResp> createOrg(OMCreateOrgReq initReq) {
        return ResponseBuilder.fromData(omService.initOrgToSmartSiteTree(initReq, null));
    }

    @Override
    public GenericityResponse<YunshuOrgSyncResp> getOrgPathName(String orgId) {
        return ResponseBuilder.fromData(omService.getOrgPathName(orgId));
    }

    @Override
    public GenericityResponse<Boolean> exchangeFinanceInfo(Long projectId, Long projectId2) {
        return ResponseBuilder.fromData(omService.exchangeFinanceInfo(projectId, projectId2));
    }
}
