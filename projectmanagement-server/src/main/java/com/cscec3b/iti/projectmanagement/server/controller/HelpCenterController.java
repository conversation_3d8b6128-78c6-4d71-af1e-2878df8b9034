package com.cscec3b.iti.projectmanagement.server.controller;

import java.util.List;

import javax.validation.constraints.Max;
import javax.validation.constraints.Min;

import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RestController;

import com.cscec3b.iti.common.base.api.GenericityResponse;
import com.cscec3b.iti.common.base.api.ResponseBuilder;
import com.cscec3b.iti.common.base.page.Page;
import com.cscec3b.iti.projectmanagement.api.IHelpCenterApi;
import com.cscec3b.iti.projectmanagement.api.dto.request.help.HelpCenterEditReq;
import com.cscec3b.iti.projectmanagement.api.dto.request.help.HelpCenterSaveReq;
import com.cscec3b.iti.projectmanagement.api.dto.response.help.HelpCenterResp;
import com.cscec3b.iti.projectmanagement.server.service.HelpCenterService;

import io.swagger.annotations.Api;

/**
 * 帮助中心
 * <AUTHOR>
 * @date 2023/1/3 15:49
 */
@RestController
@RequestMapping(IHelpCenterApi.PATH)
@Api(tags = "帮助中心")
public class HelpCenterController implements IHelpCenterApi {
    private final HelpCenterService helpCenterService;

    public HelpCenterController(HelpCenterService helpCenterService) {
        this.helpCenterService = helpCenterService;
    }

    /**
     * 查询
     * @return
     */
    @Override
    public GenericityResponse<List<HelpCenterResp>> get() {
        return ResponseBuilder.fromData(helpCenterService.get());
    }
    
    /**
     * 帮助分页列表信息
     *
     * @param current   当前
     * @param size      大小
     * @param title     标题
     * @param beginTime 开始时间
     * @param endTime   结束时间
     * @param status    状态
     * @return {@link GenericityResponse }<{@link Page }<{@link HelpCenterResp }>>
     * <AUTHOR>
     * @date 2023/08/21
     */
    @Override
    public GenericityResponse<Page<HelpCenterResp>> getList(@Min(1) int current, @Min(1) @Max(100) int size, String title, Long beginTime, Long endTime, Integer status) {
        return ResponseBuilder.fromData(helpCenterService.getList(current, size, title, beginTime, endTime, status));
    }
    
    /**
     * 新增帮助中心信息
     *
     * @param helpCenterSaveReq 帮助中心保存要求事情
     * @return {@link GenericityResponse }<{@link Boolean }>
     * <AUTHOR>
     * @date 2023/08/21
     */
    @Override
    public GenericityResponse<Boolean> save(HelpCenterSaveReq helpCenterSaveReq) {
        return ResponseBuilder.fromData(helpCenterService.save(helpCenterSaveReq));
    }
    
    /**
     * 编辑更新帮助中心信息
     *
     * @param helpCenterReq 帮助中心再保险
     * @return {@link GenericityResponse }<{@link Boolean }>
     * <AUTHOR>
     * @date 2023/08/21
     */
    @Override
    public GenericityResponse<Boolean> update(HelpCenterEditReq helpCenterReq) {
        return ResponseBuilder.fromData(helpCenterService.edit(helpCenterReq));
    }
    
    /**
     * 删除帮助中心信息，可批量
     *
     * @param ids id
     * @return {@link GenericityResponse }<{@link Boolean }>
     * <AUTHOR>
     * @date 2023/08/21
     */
    @Override
    public GenericityResponse<Boolean> delete(Long[] ids) {
        return ResponseBuilder.fromData(helpCenterService.delete(ids));
    }
    
    
    /**
     * 发布帮助中心信息
     *
     * @param helpCenterReq 帮助中心
     * @return {@link GenericityResponse }<{@link Boolean }>
     * <AUTHOR>
     * @date 2023/08/21
     */
    @Override
    public GenericityResponse<Boolean> publish(HelpCenterEditReq helpCenterReq) {
        return ResponseBuilder.fromData(helpCenterService.publish(helpCenterReq));
    }
}
