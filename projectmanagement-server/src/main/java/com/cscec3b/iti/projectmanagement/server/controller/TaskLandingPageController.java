package com.cscec3b.iti.projectmanagement.server.controller;

import java.util.Set;
import java.util.stream.Collectors;

import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RestController;

import com.baomidou.mybatisplus.core.toolkit.Wrappers;
import com.cscec3b.iti.common.base.api.GenericityResponse;
import com.cscec3b.iti.common.base.api.ResponseBuilder;
import com.cscec3b.iti.projectmanagement.api.ITaskLandingPageApi;
import com.cscec3b.iti.projectmanagement.api.dto.request.ProjectHookCreateOrgReq;
import com.cscec3b.iti.projectmanagement.api.dto.request.ProjectHookUpdateYunshuOrgReq;
import com.cscec3b.iti.projectmanagement.api.dto.request.TaskAndMessage;
import com.cscec3b.iti.projectmanagement.api.dto.request.TaskAndMessage.UserCodes;
import com.cscec3b.iti.projectmanagement.api.dto.request.engineeringproject.EngineerProjectRecordReq;
import com.cscec3b.iti.projectmanagement.api.dto.request.task.TodoTaskCreateOrgReq;
import com.cscec3b.iti.projectmanagement.api.dto.response.TodoTaskDetailResp;
import com.cscec3b.iti.projectmanagement.api.dto.response.projecthook.ProjectHookInfoResp;
import com.cscec3b.iti.projectmanagement.server.service.IEngineeringProjectService;
import com.cscec3b.iti.projectmanagement.server.service.ITaskLandingPageService;
import com.cscec3b.iti.projectmanagement.server.util.LoginUserUtil;
import com.cscec3b.iti.taskmesage.model.TodoTask;
import com.cscec3b.iti.taskmesage.service.TaskAndMessageService;
import com.cscec3b.iti.taskmesage.service.TodoTaskService;
import com.g3.org.api.dto.resp.org.AddDepartmentResp;
import com.odin.freyr.common.orika.BeanMapUtils;

import io.swagger.annotations.Api;
import lombok.RequiredArgsConstructor;

/**
 * <AUTHOR>
 * @date 2023/6/21 15:52
 */
@RestController
@RequestMapping(ITaskLandingPageApi.PATH)
@RequiredArgsConstructor
@Api(tags = {"任务落地页"})
public class TaskLandingPageController implements ITaskLandingPageApi {

    private final ITaskLandingPageService taskLandingPageService;

    private final TodoTaskService todoTaskService;

    private final TaskAndMessageService taskAndMessageService;

    private final IEngineeringProjectService engineeringProjectService;

    @Override
    public GenericityResponse<Boolean> taskDone(String taskCode) {
        return ResponseBuilder.fromData(taskAndMessageService.doneTaskByTaskCode(taskCode));
    }

    @Override
    public GenericityResponse<Boolean> taskDoneByInstanceId(String bpmInstanceId) {
        return ResponseBuilder.fromData(taskAndMessageService.doneTaskByBpmInstanceId(bpmInstanceId));
    }

    @Override
    public GenericityResponse<TodoTaskDetailResp> taskDetail(String taskCode) {
        final String userCode = LoginUserUtil.userCode();
        final TodoTask one = todoTaskService.getOne(Wrappers.<TodoTask>lambdaQuery()
            .eq(TodoTask::getTaskCode, taskCode).or().eq(TodoTask::getBillId, taskCode)
            .eq(TodoTask::getTargetUser, userCode));
        final TodoTaskDetailResp detailResp = BeanMapUtils.map(one, TodoTaskDetailResp.class);
        return ResponseBuilder.fromData(detailResp);
    }

    @Override
    public GenericityResponse<TodoTaskDetailResp> taskDetailByBpmInstanceId(String bpmInstanceId) {
        final String userCode = LoginUserUtil.userCode();
        final TodoTask one = todoTaskService.getOne(Wrappers.<TodoTask>lambdaQuery()
            .eq(TodoTask::getBillId, bpmInstanceId).or()
            .likeRight(TodoTask::getBpmInstanceId, bpmInstanceId).eq(TodoTask::getTargetUser, userCode));
        final TodoTaskDetailResp detailResp = BeanMapUtils.map(one, TodoTaskDetailResp.class);
        return ResponseBuilder.fromData(detailResp);
    }

    @Override
    public GenericityResponse<ProjectHookInfoResp> projectHookInfo(String billId) {
        return ResponseBuilder.fromData(taskLandingPageService.projectHookInfo(billId));
    }

    @Override
    public GenericityResponse<AddDepartmentResp> orgCreate(ProjectHookCreateOrgReq createOrgReq) {
        return ResponseBuilder
            .fromData(taskLandingPageService.orgCreate(createOrgReq.getBillId(), createOrgReq));
    }

    @Override
    public GenericityResponse<Boolean> taskForUpdateYunshu(ProjectHookUpdateYunshuOrgReq updateYunshuOrgIdReq) {
        return ResponseBuilder.fromData(taskLandingPageService.hookTaskToUpdateYunshuId(updateYunshuOrgIdReq));
    }

    @Override
    public GenericityResponse<Boolean> taskTransfer(TaskAndMessage transferReq) {
        final Set<String> userCodes =
                transferReq.getUserCodes().stream().map(UserCodes::getId).collect(Collectors.toSet());
        // 完成当前任务，给目标用户分配新任务
        taskAndMessageService.switchToDoTask(transferReq.getBillId(), userCodes, LoginUserUtil.userCode());
        return ResponseBuilder.fromData(Boolean.TRUE);
    }

    @Override
    public GenericityResponse<Boolean> engineerRecordDept(EngineerProjectRecordReq recordReq) {
        return ResponseBuilder.fromData(taskLandingPageService.engineerRecordDept(recordReq));
    }

    @Override
    public GenericityResponse<AddDepartmentResp> createDeptOrg(TodoTaskCreateOrgReq createOrgReq) {
        return ResponseBuilder.fromData(taskLandingPageService.createDeptOrg(createOrgReq));
    }
}
