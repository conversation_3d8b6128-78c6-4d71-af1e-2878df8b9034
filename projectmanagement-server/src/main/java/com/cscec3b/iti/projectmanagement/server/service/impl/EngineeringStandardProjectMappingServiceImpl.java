package com.cscec3b.iti.projectmanagement.server.service.impl;

import java.util.ArrayList;
import java.util.HashMap;
import java.util.List;
import java.util.Map;
import java.util.Set;
import java.util.concurrent.CompletableFuture;

import org.springframework.scheduling.annotation.Async;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;
import org.springframework.web.client.RestTemplate;

import com.baomidou.mybatisplus.core.toolkit.Wrappers;
import com.baomidou.mybatisplus.extension.service.impl.ServiceImpl;
import com.cscec3b.iti.projectmanagement.server.entity.EngineeringStandardProjectCheckRecord;
import com.cscec3b.iti.projectmanagement.server.entity.EngineeringStandardProjectMapping;
import com.cscec3b.iti.projectmanagement.server.entity.Project;
import com.cscec3b.iti.projectmanagement.server.entity.ProjectEventSubscribe;
import com.cscec3b.iti.projectmanagement.server.mapper.EngineeringStandardProjectMappingMapper;
import com.cscec3b.iti.projectmanagement.server.mapper.ProjectEventSubscribeMapper;
import com.cscec3b.iti.projectmanagement.server.service.EngineeringStandardProjectCheckRecordService;
import com.cscec3b.iti.projectmanagement.server.service.EngineeringStandardProjectMappingService;

import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;

@Slf4j
@Service
@RequiredArgsConstructor
@Transactional(rollbackFor = Exception.class)
public class EngineeringStandardProjectMappingServiceImpl
    extends ServiceImpl<EngineeringStandardProjectMappingMapper, EngineeringStandardProjectMapping>
    implements EngineeringStandardProjectMappingService {

    private static final Map<String, Integer> systemBitMap = new HashMap<>();

    // 使用静态方法初始化
    static {
        // 初始化系统标识与二进制位的映射关系 (从左往右: 工程, 财商, 商务, 供应链)
        // map Key值为 消费系统的系统标识 appCode (如: 工程：SmartSite, 财商：Finance, 商务:Bms, 供应链:Scm)
        systemBitMap.put("SmartSite", 3); // 1000 (第 3 位)
        systemBitMap.put("Finance", 2); // 0100 (第 2位)
        systemBitMap.put("BMSTransferService", 1); // 0010 (第 1位)
        systemBitMap.put("SCMTransferService", 0); // 0001 (第 0位)
    }

    private final RestTemplate pmRestTemplate;

    private final ProjectEventSubscribeMapper subscribeMapper;

    private final EngineeringStandardProjectCheckRecordService checkRecordService;

    @Async("cpmTaskExecutor")
    @Override
    public void performSystemCheck(Long engineeringStandardProjectId, Project project) {
        final EngineeringStandardProjectMapping standardProjectMapping =
            this.getOne(Wrappers.<EngineeringStandardProjectMapping>lambdaQuery()
                .eq(EngineeringStandardProjectMapping::getStandardProjectId, project.getId())
                .eq(EngineeringStandardProjectMapping::getEngineeringProjectId, engineeringStandardProjectId));
        int systemStatus = 0; // 初始化 systemStatus 为 0

        try {
            final Set<String> appCodes = systemBitMap.keySet();
            final List<ProjectEventSubscribe> subscribes = subscribeMapper.selectList(
                Wrappers.<ProjectEventSubscribe>lambdaQuery().in(ProjectEventSubscribe::getAppCode, appCodes));
            // 使用异步方法获取所有系统中的项目的状态标识
            List<CompletableFuture<EngineeringStandardProjectCheckRecord>> futures = new ArrayList<>();
            Map<String, CompletableFuture<EngineeringStandardProjectCheckRecord>> futureMap = new HashMap<>();

            subscribes.forEach(subscribe -> {
                final String appCode = subscribe.getAppCode();
                CompletableFuture<EngineeringStandardProjectCheckRecord> future = null;
                switch (appCode) {
                    case "SmartSite":
                        future = CompletableFuture.supplyAsync(() -> this.checkRecordService
                            .checkStandardProjectInSmartSite(standardProjectMapping, project, subscribe));
                        break;
                    case "Finance":
                        future = CompletableFuture.supplyAsync(() -> this.checkRecordService
                            .checkStandardProjectInFinance(standardProjectMapping, project, subscribe));
                        break;
                    case "BMSTransferService":
                        future = CompletableFuture.supplyAsync(() -> this.checkRecordService
                            .checkStandardProjectInBms(standardProjectMapping, project, subscribe));
                        break;
                    case "SCMTransferService":
                        future = CompletableFuture.supplyAsync(() -> this.checkRecordService
                            .checkStandardProjectInScm(standardProjectMapping, project, subscribe));
                        break;
                    default:
                        // 不支持的 appCode，跳过处理
                        log.warn("未识别的系统标识: {}", appCode);
                        break;
                }
                if (future != null) {
                    futures.add(future);
                    futureMap.put(appCode, future);
                }
            });
            // 等待异步任务全部完成
            CompletableFuture.allOf(futures.toArray(new CompletableFuture[0])).join();
            // 获取并处理异步任务的结果
            for (Map.Entry<String, CompletableFuture<EngineeringStandardProjectCheckRecord>> entry : futureMap
                .entrySet()) {
                String appCode = entry.getKey();
                CompletableFuture<EngineeringStandardProjectCheckRecord> future = entry.getValue();
                try {
                    EngineeringStandardProjectCheckRecord checkRecord = future.get();
                    if (checkRecord != null) {
                        Integer bitPosition = systemBitMap.get(appCode);
                        if (bitPosition != null) {
                            systemStatus = updateSystemStatus(systemStatus, bitPosition, checkRecord.getStatus());
                        } else {
                            log.warn("未找到系统标识 {} 对应的位信息", appCode);
                        }
                    }
                } catch (Exception e) {
                    log.error("获取系统 {} 的检查结果失败：{}", appCode, e.getMessage());
                    // 在出现异常时，该系统对应的状态可以默认为 false，不影响其他系统的状态
                }
            }

            // 更新 EngineeringStandardProjectMapping 表中的 systemStatus
            this.update(Wrappers.<EngineeringStandardProjectMapping>lambdaUpdate()
                .set(EngineeringStandardProjectMapping::getSystemStatus, systemStatus)
                .eq(EngineeringStandardProjectMapping::getEngineeringProjectId, engineeringStandardProjectId)
                .eq(EngineeringStandardProjectMapping::getStandardProjectId, project.getId()));

            // return systemStatus; // 返回更新后的状态掩码

        } catch (Exception e) {
            log.warn("获取标准项目关联的系统状态失败：{}", e.getMessage());
            log.warn("error", e);
        }
        // return systemStatus;

        String binaryString = String.format("%4s", Integer.toBinaryString(systemStatus)).replace(' ', '0');
        log.info("更新标准项目关联的系统状态成功：{} -> {}", systemStatus, binaryString);
    }

    // 新增方法：更新 systemStatus 的位
    private int updateSystemStatus(int systemStatus, int bitPosition, boolean status) {
        if (status) {
            return systemStatus | (1 << bitPosition); // 设置对应的位为1
        } else {
            return systemStatus & ~(1 << bitPosition); // 设置对应的位为0
        }
        // 计算对应位的十进制值
        // int bitValue = (int) Math.pow(2, bitPosition);
        // if (status) {
        // return systemStatus | bitValue; // 设置对应的位为1
        // } else {
        // return systemStatus & ~bitValue; // 设置对应的位为0
        // }
    }
}
