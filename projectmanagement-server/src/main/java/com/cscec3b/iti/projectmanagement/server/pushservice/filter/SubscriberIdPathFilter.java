package com.cscec3b.iti.projectmanagement.server.pushservice.filter;

import java.util.Objects;

import javax.validation.constraints.NotNull;

import org.springframework.stereotype.Component;

import com.cscec3b.iti.projectmanagement.server.entity.Project;
import com.cscec3b.iti.projectmanagement.server.entity.ProjectEventPushRecord;
import com.cscec3b.iti.projectmanagement.server.entity.dto.ProjectFlowEventSubscribeDto;

/**
 * 消费者数据权限过滤器
 *
 * <AUTHOR>
 * @date 2024/08/20
 */
@Component
public class SubscriberIdPathFilter extends AbstractEventMsgFilter {

    @Override
    protected boolean doFilter(@NotNull Project project, @NotNull ProjectFlowEventSubscribeDto subscriber,
            ProjectEventPushRecord pushRecord) {
        if (Objects.nonNull(project) && Objects.nonNull(subscriber)
            && project.getYunshuExecuteUnitIdPath().startsWith(subscriber.getYunshuExecuteUnitIdPath())) {
            return true;
        } else {
            final String errMsg = String.format("warning: 数据权限不匹配: \n project " + "idPath:%s, " + " \n 消费者数据范围: %s",
                project.getYunshuExecuteUnitIdPath(), subscriber.getYunshuExecuteUnitIdPath());
            pushRecord.setLogLevel(LOG_WARNING).setIsPushSuccess(2).setErrMsg(errMsg);
            return false;
        }

        // return Optional.of(project).map(Project::getYunshuExecuteUnitIdPath)
        // .map(idPath -> idPath.startsWith(subscriber.getYunshuExecuteUnitIdPath()))
        // .filter(Boolean.TRUE::equals)
        // .orElseThrow(() -> {
        // pushRecord.setLogLevel(LOG_WARNING).setIsPushSuccess(Constants.NUMBER_ONE);
        // return new FrameworkException(-1, String.format("warning: 数据权限不匹配: \n project " +
        // "idPath:%s, " +
        // " \n 消费者数据范围: %s",
        // project.getYunshuExecuteUnitIdPath(), subscriber.getYunshuExecuteUnitIdPath()));
        // });
    }


    @Override
    public int getOrder() {
        return 1;
    }
}
