package com.cscec3b.iti.projectmanagement.server.service;

import com.baomidou.mybatisplus.extension.service.IService;
import com.cscec3b.iti.common.base.page.Page;
import com.cscec3b.iti.projectmanagement.api.dto.request.engineeringproject.*;
import com.cscec3b.iti.projectmanagement.api.dto.request.open.OpenEngineerProjectPageReq;
import com.cscec3b.iti.projectmanagement.api.dto.request.open.OpenEngineeringArchiveReq;
import com.cscec3b.iti.projectmanagement.api.dto.request.open.OpenEngineeringMappingArchiveReq;
import com.cscec3b.iti.projectmanagement.api.dto.response.engineeringproject.*;
import com.cscec3b.iti.projectmanagement.api.dto.response.open.OpenEngineerProjectTreeResp;
import com.cscec3b.iti.projectmanagement.api.dto.response.open.OpenEngineeringArchiveResp;
import com.cscec3b.iti.projectmanagement.api.dto.response.open.OpenEngineeringProjectMappingResp;
import com.cscec3b.iti.projectmanagement.api.dto.response.open.OpenEngineeringProjectPageResp;
import com.cscec3b.iti.projectmanagement.server.entity.EngineeringProject;

import java.util.List;

public interface IEngineeringProjectService extends IService<EngineeringProject> {

    /**
     * 新增工程项目，并关联施工项目，同时将传入的施工项目设置为主施工项目
     * 
     * @param req 请求参数
     * @return {@link Boolean }
     */
    Boolean addEngineeringProject(AddEngineeringProjectReq req);

    /**
     * 挂接工程项目到工程
     * 
     * @param req 请求参数
     * @return {@link Boolean }
     */
    Boolean hookEngineering2EngineeringProject(HookEngineering2EngineeringProjectReq req);

    /**
     * 关联施工项目到工程
     * 
     * @param req 请求参数
     * @return {@link Boolean }
     */
    Boolean mappingProject2EngineeringProject(MappingStandard2EngineeringProjectReq req);


    /**
     * 获取工程项目分页列表
     * 
     * @param req 请求参数
     * @return {@link Page }<{@link EngineerProjectPageResp }>
     */
    Page<EngineerProjectPageResp> pageList(EngineerProjectPageReq req, String searchKeyWord);

    /**
     * 挂接时获取工程项目分页列表
     * 
     * @param req 请求参数
     * @return {@link Page }<{@link EngineerProjectPageResp }>
     */
    Page<EngineerProjectPageResp> hookPageList(HookEngineerProjectPageReq req);

    /**
     * 解除施工项目与工程的关联
     * 
     * @param projectId 施工项目id
     * @return {@link Boolean }
     */
    Boolean unmappingProject2EngineeringProject(Long projectId);

    /**
     * 解除工程项目与上级工程项目的关联
     * 
     * @param engineeringProjectId 工程项目id
     * @return {@link Boolean }
     */
    Boolean unmappingEngineering2EngineeringProject(Long engineeringProjectId);

    /**
     * 设置主施工项目
     * 
     * @param projectId 施工项目id
     * @param engineeringProjectId 工程项目id
     * @return {@link Boolean }
     */
    Boolean setMainProject(Long projectId, Long engineeringProjectId);

    /**
     * 通过施工项目获取工程项目信息，包含上下级工程项目信息
     * 
     * @param projectId 工程项目id
     * @return {@link EngineeringProjectInfoResp }
     */
    EngineeringProjectInfoResp getEngineeringProjectInfoByProject(Long projectId);

    /**
     * 通过工程项目id获取关联的施工项目信息
     *
     * @param engineeringProjectId 工程项目id
     * @return {@link MappingStandardProjectResp }
     */
    MappingStandardProjectResp getMappingStandardProjectByEngineeringProject(Long engineeringProjectId);

    /**
     * 通过工程项目id获取关联的施工项目信息
     *
     * @param engineeringProjectId 工程项目id
     * @param getSmartSite 获取 Smart Site
     * @return {@link MappingStandardProjectResp }
     */
    MappingStandardProjectResp getMappingStandardProjectByEngineeringAndSmartSite(Long engineeringProjectId,
        Boolean getSmartSite);
    /**
     * 获取 Open Enginee 项目页面
     *
     * @param openApiKey 开放 API 密钥
     * @param pageReq 页 req
     * @return {@link Page }<{@link OpenEngineeringProjectPageResp }>
     */
    Page<OpenEngineeringProjectPageResp> getOpenEngineeProjectPage(String openApiKey,
        OpenEngineerProjectPageReq pageReq);

    /**
     * 按 Key 获取 Open Engineer Project Tree
     * <p>
     * 返回当前工程项目所在的全量树
     * </p>
     *
     * @param openApiKey 开放 API 密钥
     * @param key 钥匙
     * @return {@link OpenEngineerProjectTreeResp }
     */
    OpenEngineerProjectTreeResp getOpenEngineerProjectTreeByKey(String openApiKey, String key);

    /**
     * 发送待办任务
     *
     * @param engineerIds 工程师 ID
     * @return {@link Boolean }
     */
    Boolean sendTodoTask(List<Long> engineerIds);

    /**
     * 设置项目部
     *
     * @param recordReq 记录请求
     * @return {@link Boolean }
     */
    Boolean recordDept(EngineerProjectRecordReq recordReq);

    /**
     * 公司视图页面列表
     *
     * @param req 请求
     * @return {@link Page }<{@link CompanyViewEngineeringProjectResp }>
     */
    Page<CompanyViewEngineeringProjectResp> companyViewPageList(CompanyViewEngineerProjectReq req);

    /**
     * 获取公司视图工程项目树
     *
     * @param engineerId 工程师 ID
     * @return {@link CompanyViewEngineeringProjectResp }
     */
    List<CompanyViewEngineeringProjectTreeResp> getCompanyViewEngineeringProjectTree(Long engineerId);

    /**
     * 交换施工项目与工程项目的关联
     *
     * @param req 要求
     * @return {@link Boolean }
     */
    Boolean transferProjectMapping(TransferProjectMappingReq req);

    /**
     * @param openApiKey openapikey
     * @param pageReq    分页请求
     * @return {@link Page }<{@link OpenEngineeringArchiveResp }>
     */
    Page<OpenEngineeringArchiveResp> getEngineeringArchivedPage(String openApiKey, OpenEngineeringArchiveReq pageReq);

    /**
     * 以公司视角查询工程信息（带权限验证）
     *
     * @param openApiKey 开放 API 密钥
     * @param req 请求参数
     * @return {@link Page }<{@link CompanyViewEngineeringProjectResp }>
     */
    Page<CompanyViewEngineeringProjectResp> getCompanyViewEngineeringProjectPage(String openApiKey, CompanyViewEngineerProjectReq req);

    /**
     * 获取公司视图工程项目树（带权限验证）
     *
     * @param openApiKey 开放 API 密钥
     * @param engineerId 工程师 ID
     * @return {@link List }<{@link CompanyViewEngineeringProjectTreeResp }>
     */
    List<CompanyViewEngineeringProjectTreeResp> getCompanyViewEngineeringProjectTreeOpen(String openApiKey, Long engineerId);

    /**
     * 通过施工项目获取工程项目关联关系
     *
     * @param openApiKey openapikey
     * @param req        分页请求参数
     * @return {@link OpenEngineeringProjectMappingResp }
     */
    Page<OpenEngineeringProjectMappingResp> getEnginAndStandardProjectMappingInfo(String openApiKey, OpenEngineeringMappingArchiveReq req);
}