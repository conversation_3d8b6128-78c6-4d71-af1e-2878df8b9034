package com.cscec3b.iti.projectmanagement.server.service.impl;

import com.baomidou.mybatisplus.core.toolkit.Wrappers;
import com.baomidou.mybatisplus.extension.service.impl.ServiceImpl;
import com.cscec3b.iti.common.base.json.JsonUtils;
import com.cscec3b.iti.common.base.page.Page;
import com.cscec3b.iti.common.web.exception.FrameworkException;
import com.cscec3b.iti.model.resp.ProjectBindingSegment;
import com.cscec3b.iti.projectmanagement.api.dto.dto.smartsite.HookDto;
import com.cscec3b.iti.projectmanagement.api.dto.dto.smartsite.ProjectDetailOutResp;
import com.cscec3b.iti.projectmanagement.api.dto.request.engineeringproject.*;
import com.cscec3b.iti.projectmanagement.api.dto.request.open.OpenEngineerProjectPageReq;
import com.cscec3b.iti.projectmanagement.api.dto.request.open.OpenEngineeringArchiveReq;
import com.cscec3b.iti.projectmanagement.api.dto.request.open.OpenEngineeringMappingArchiveReq;
import com.cscec3b.iti.projectmanagement.api.dto.response.engineeringproject.*;
import com.cscec3b.iti.projectmanagement.api.dto.response.open.OpenEngineerProjectTreeResp;
import com.cscec3b.iti.projectmanagement.api.dto.response.open.OpenEngineeringArchiveResp;
import com.cscec3b.iti.projectmanagement.api.dto.response.open.OpenEngineeringProjectMappingResp;
import com.cscec3b.iti.projectmanagement.api.dto.response.open.OpenEngineeringProjectPageResp;
import com.cscec3b.iti.projectmanagement.server.config.UcOpenApiProperties;
import com.cscec3b.iti.projectmanagement.server.config.UcOpenApiProperties.TaskConfig;
import com.cscec3b.iti.projectmanagement.server.constant.Constants;
import com.cscec3b.iti.projectmanagement.server.entity.EngineeringProject;
import com.cscec3b.iti.projectmanagement.server.entity.EngineeringStandardProjectMapping;
import com.cscec3b.iti.projectmanagement.server.entity.Project;
import com.cscec3b.iti.projectmanagement.server.entity.dto.ClientRequestDataPermission;
import com.cscec3b.iti.projectmanagement.server.mapper.EngineeringProjectMapper;
import com.cscec3b.iti.projectmanagement.server.mapper.ProjectEventSubscribeMapper;
import com.cscec3b.iti.projectmanagement.server.mapper.ProjectMapper;
import com.cscec3b.iti.projectmanagement.server.pushservice.enums.FlowNodeDataTypeEnum;
import com.cscec3b.iti.projectmanagement.server.pushservice.enums.FlowNodeEnum;
import com.cscec3b.iti.projectmanagement.server.pushservice.enums.FlowNodeHandlerEnum;
import com.cscec3b.iti.projectmanagement.server.pushservice.event.CpmProjectFlowEvent;
import com.cscec3b.iti.projectmanagement.server.pushservice.service.IEventCallbackService;
import com.cscec3b.iti.projectmanagement.server.pushservice.util.ProjectTransferEventBuilder;
import com.cscec3b.iti.projectmanagement.server.service.EngineeringStandardProjectMappingService;
import com.cscec3b.iti.projectmanagement.server.service.IEngineeringProjectService;
import com.cscec3b.iti.projectmanagement.server.service.IOpenApiDataScopeService;
import com.cscec3b.iti.projectmanagement.server.util.LoginUserUtil;
import com.cscec3b.iti.taskmesage.dto.TaskAndMsgDto;
import com.cscec3b.iti.taskmesage.mapper.TodoTaskMapper;
import com.cscec3b.iti.taskmesage.model.TodoTask;
import com.cscec3b.iti.taskmesage.service.TaskAndMessageService;
import com.cscec3b.iti.tg.common.base.util.BeanMapUtils;
import com.github.pagehelper.PageHelper;
import com.google.common.collect.Sets;
import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.collections4.CollectionUtils;
import org.apache.commons.lang3.StringUtils;
import org.apache.groovy.util.Maps;
import org.springframework.context.ApplicationEventPublisher;
import org.springframework.http.HttpStatus;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;

import java.time.Instant;
import java.time.LocalDateTime;
import java.util.*;
import java.util.stream.Collectors;

@Slf4j
@Service
@RequiredArgsConstructor
@Transactional(rollbackFor = Exception.class)
public class EngineeringProjectServiceImpl extends ServiceImpl<EngineeringProjectMapper, EngineeringProject>
    implements IEngineeringProjectService {

    private final static String ENGINEER_PROJECT_KEY_PREFIX = "G";

    private final UcOpenApiProperties openApiProperties;

    private final EngineeringStandardProjectMappingService engineeringStandardProjectMappingService;

    private final ProjectMapper projectMapper;

    private final IEventCallbackService eventCallbackService;

    private final ProjectEventSubscribeMapper subscribeMapper;

    private final TaskAndMessageService taskAndMessageService;

    private final TodoTaskMapper todoTaskMapper;

    private final ApplicationEventPublisher publisher;

    private final IOpenApiDataScopeService dataScopeService;

    @Override
    public Boolean addEngineeringProject(AddEngineeringProjectReq req) {
        final int nameCount = this.count(Wrappers.<EngineeringProject>lambdaQuery()
            .eq(EngineeringProject::getEngineeringName, req.getEngineeringName()));
        if (nameCount > 0) {
            throw new FrameworkException(-1, "系统中已存在同名的工程项目，请检查后重试");
        }
        // // 编码不能重复
        // final int codeCount = this.count(Wrappers.<EngineeringProject>lambdaQuery()
        // .eq(EngineeringProject::getEngineeringCode, req.getEngineeringCode()));
        // if (codeCount > 0) {
        // throw new FrameworkException(-1, "系统中已存在该编码的工程项目，请检查后重试");
        // }
        final Project project = checkProject(req.getProjectId());
        // 检查项目是否已关联到工程项目
        final int count =
            engineeringStandardProjectMappingService.count(Wrappers.<EngineeringStandardProjectMapping>lambdaQuery()
                .eq(EngineeringStandardProjectMapping::getStandardProjectId, req.getProjectId()));
        if (count > 0) {
            throw new FrameworkException(-1, "该施工项目已关联到标准工程项目，如需重新绑定请先解除关联");
        }
        // 生成标识 ： 使用CpmProjectKey 替换P为GP
        final String cpmProjectKey = project.getCpmProjectKey();
        String engineeringKey = ENGINEER_PROJECT_KEY_PREFIX + cpmProjectKey;
        if (!engineeringKey.equals(req.getEngineeringKey())) {
            throw new FrameworkException(-1, "工程项目标识未正确匹配，请刷新后重试");
        }
        // 新增工程项目并添加 主施工项目标识
        final EngineeringProject engineeringProject = EngineeringProject.builder()
            .engineeringName(req.getEngineeringName())
                .engineeringCode(req.getEngineeringCode()).mainProjectId(req.getProjectId())
            .mappingExecuteUnitId(project.getYunshuExecuteUnitId())
                .engineeringKey(engineeringKey).build();
        this.save(engineeringProject);
        // 生成关联表数据
        final EngineeringStandardProjectMapping engineeringStandardProjectMapping =
            EngineeringStandardProjectMapping.builder().engineeringProjectId(engineeringProject.getId())
                .standardProjectId(req.getProjectId()).main(true).build();
        engineeringStandardProjectMappingService.save(engineeringStandardProjectMapping);
        // 检查当前项目是否挂接项目，如果是挂接，则要将关联项目添加进来
        // 异步处理 检查施工项目在各系统关联状态
        engineeringStandardProjectMappingService.performSystemCheck(engineeringProject.getId(), project);
        return Boolean.TRUE;
    }

    @Override
    public Boolean hookEngineering2EngineeringProject(HookEngineering2EngineeringProjectReq req) {
        final Integer hookType = req.getHookType();
        final EngineeringProject currentEngineeringProject = this.getById(req.getCurrentEngineeringProjectId());
        if (Objects.isNull(currentEngineeringProject)) {
            throw new FrameworkException(-1, "当前工程项目不存在，请检查后重试");
        }
        final EngineeringProject targetEngineeringProject = this.getById(req.getTargetEngineeringProjectId());
        if (Objects.isNull(targetEngineeringProject)) {
            throw new FrameworkException(-1, "目标工程项目不存在，请检查后重试");
        }
        switch (hookType) {
            case 1:
                // 挂接上级工程项目，只需要修改当前工程项目的上级id；
                final Long currentParentId = currentEngineeringProject.getParentId();
                if (Objects.nonNull(currentParentId) && !currentParentId.equals(0L)
                    && !currentParentId.equals(targetEngineeringProject.getId())) {
                    throw new FrameworkException(-1, "当前工程项目已关联其他上级工程项目，如需重新绑定请先解除关联");
                }
                currentEngineeringProject.setParentId(targetEngineeringProject.getId());
                this.updateById(currentEngineeringProject);
                break;
            case 2:
                // 挂接子工程项目，将子工程项目的上级id修改为当前工程项目id
                final Long targetParentId = targetEngineeringProject.getParentId();
                if (Objects.nonNull(targetParentId) && !targetParentId.equals(0L)
                    && !targetParentId.equals(currentEngineeringProject.getId())) {
                    throw new FrameworkException(-1, "目标工程项目已关联其他上级工程项目，如需重新绑定请先解除关联");
                }
                targetEngineeringProject.setParentId(currentEngineeringProject.getId());
                this.updateById(targetEngineeringProject);
                break;
        }
        return Boolean.TRUE;
    }

    @Override
    public Boolean mappingProject2EngineeringProject(MappingStandard2EngineeringProjectReq req) {
        // 关联施工项目到工程项目，如果工程项目之前没有关联过施工项目，则当前施工项目为主施工项目
        final EngineeringProject engineeringProject = this.getById(req.getEngineeringProjectId());
        if (Objects.isNull(engineeringProject)) {
            throw new FrameworkException(-1, "工程项目不存在，请检查后重试");
        }
        final Project project = checkProject(req.getProjectId());
        // 检查项目是否已关联到工程项目
        final int count =
            engineeringStandardProjectMappingService.count(Wrappers.<EngineeringStandardProjectMapping>lambdaQuery()
                .eq(EngineeringStandardProjectMapping::getStandardProjectId, req.getProjectId()));
        if (count > 0) {
            throw new FrameworkException(-1, "该施工项目已关联到其他工程项目，如需重新绑定请先解除关联");
        }
        // 检查主施工项目
        final List<EngineeringStandardProjectMapping> standardProjectMappings =
            engineeringStandardProjectMappingService.list(Wrappers.<EngineeringStandardProjectMapping>lambdaQuery()
                .eq(EngineeringStandardProjectMapping::getEngineeringProjectId, req.getEngineeringProjectId()));

        if (Objects.isNull(engineeringProject.getMainProjectId()) || engineeringProject.getMainProjectId().equals(0L)
            || standardProjectMappings.isEmpty()) {
            engineeringProject.setMainProjectId(project.getId());
        }
        this.updateById(engineeringProject);
        // 生成关联表数据
        final EngineeringStandardProjectMapping engineeringStandardProjectMapping =
            EngineeringStandardProjectMapping.builder().engineeringProjectId(req.getEngineeringProjectId())
                .standardProjectId(req.getProjectId()).main(standardProjectMappings.isEmpty()).build();
        engineeringStandardProjectMappingService.save(engineeringStandardProjectMapping);
        // 触发施工项目绑定事件
        ProjectBindingSegment transferData = ProjectTransferEventBuilder.buildBindingData(
                project, engineeringProject, engineeringStandardProjectMapping);

        publisher.publishEvent(new CpmProjectFlowEvent(this, req.getProjectId(), transferData,
                FlowNodeEnum.ENGINE_BINDING_STANDARD_PROJECT, FlowNodeHandlerEnum.POST, FlowNodeDataTypeEnum.CREATE));
        // 异步处理 检查施工项目在各系统关联状态
        engineeringStandardProjectMappingService.performSystemCheck(engineeringProject.getId(), project);
        return Boolean.TRUE;
    }

    @Override
    public Page<EngineerProjectPageResp> pageList(EngineerProjectPageReq req, String searchKeyWord) {
        // 获取
        final com.github.pagehelper.Page<EngineerProjectPageResp> pageResps = PageHelper
            .startPage(req.getCurrent(), req.getSize()).doSelectPage(() -> this.baseMapper.getList(req));
        final Page<EngineerProjectPageResp> page =
            new Page<>(pageResps.getTotal(), pageResps.getPageNum(), pageResps.getPageSize());
        page.setRecords(pageResps.getResult());
        return page;
    }

    @Override
    public Page<EngineerProjectPageResp> hookPageList(HookEngineerProjectPageReq req) {
        if (Objects.isNull(req.getCurrentEngineeringProjectId())) {
            final com.github.pagehelper.Page<EngineerProjectPageResp> pageResps =
                PageHelper.startPage(req.getCurrent(), req.getSize())
                    .doSelectPage(() -> this.baseMapper.getHookList(null, req.getSearchKeyWord(), Arrays.asList()));
            final Page<EngineerProjectPageResp> respPage =
                new Page<>(pageResps.getTotal(), pageResps.getPageNum(), pageResps.getPageSize());
            respPage.setRecords(pageResps.getResult());
            return respPage;
        }
        // 2:挂接上级工程项目；1:挂接下级工程项目
        // 挂接上级时，不能展示其子孙节点；
        // 挂接子节点时，不能展示其祖先节点；
        return req.getHookLevel() == 1 ? hookChildPageList(req) : hookParentPageList(req);
    }

    /**
     * 获取挂接下级工程项目列表
     * 
     * @param req
     * @return {@link Page }<{@link EngineerProjectPageResp }>
     */
    private Page<EngineerProjectPageResp> hookChildPageList(HookEngineerProjectPageReq req) {
        // 获取所有祖先节点ID（包括自己）
        List<Long> excludeIds = this.baseMapper.getAllAncestorIds(req.getCurrentEngineeringProjectId());
        excludeIds.add(req.getCurrentEngineeringProjectId());

        final com.github.pagehelper.Page<EngineerProjectPageResp> pageResps =
            PageHelper.startPage(req.getCurrent(), req.getSize()).doSelectPage(
                () -> this.baseMapper.getHookList(req.getHookLevel(), req.getSearchKeyWord(), excludeIds));
        final Page<EngineerProjectPageResp> respPage =
            new Page<>(pageResps.getTotal(), pageResps.getPageNum(), pageResps.getPageSize());
        respPage.setRecords(pageResps.getResult());
        return respPage;
    }

    /**
     * 获取挂接上工程项目列表
     * 
     * @param req
     * @return {@link Page }<{@link EngineerProjectPageResp }>
     */
    private Page<EngineerProjectPageResp> hookParentPageList(HookEngineerProjectPageReq req) {
        // 获取所有子孙节点ID（包括自己）
        List<Long> excludeIds = new ArrayList<>();
        List<String> excludeIdStrs = this.baseMapper.getAllDescendantIds(req.getCurrentEngineeringProjectId());
        if (CollectionUtils.isNotEmpty(excludeIdStrs)) {
            String combinedIdsStr = String.join(",", excludeIdStrs);
            final List<Long> ids = Arrays.stream(combinedIdsStr.split(",")).map(String::trim).map(Long::valueOf) // 转换为
                .collect(Collectors.toList());
            excludeIds.addAll(ids);
        }
        excludeIds.add(req.getCurrentEngineeringProjectId());

        final com.github.pagehelper.Page<EngineerProjectPageResp> pageResps =
            PageHelper.startPage(req.getCurrent(), req.getSize()).doSelectPage(
                () -> this.baseMapper.getHookList(req.getHookLevel(), req.getSearchKeyWord(), excludeIds));
        final Page<EngineerProjectPageResp> respPage =
            new Page<>(pageResps.getTotal(), pageResps.getPageNum(), pageResps.getPageSize());
        respPage.setRecords(pageResps.getResult());
        return respPage;

    }

    @Override
    public Boolean unmappingProject2EngineeringProject(Long projectId) {
        // 检查施工项目是否存在
        final Project project = checkProject(projectId);

        // 获取当前施工项目与工程项目的关联关系
        final EngineeringStandardProjectMapping mappingServiceOne =
            engineeringStandardProjectMappingService.getOne(Wrappers.<EngineeringStandardProjectMapping>lambdaQuery()
                .eq(EngineeringStandardProjectMapping::getStandardProjectId, projectId));
        if (Objects.isNull(mappingServiceOne)) {
            throw new FrameworkException(-1, "当前施工项目未关联到工程项目，请检查后重试");
        }

        final Long engineeringProjectId = mappingServiceOne.getEngineeringProjectId();
        final EngineeringProject engineeringProject = this.getById(engineeringProjectId);

        // 获取当前工程项目的所有关联施工项目
        final List<EngineeringStandardProjectMapping> mappingList =
            engineeringStandardProjectMappingService.list(Wrappers.<EngineeringStandardProjectMapping>lambdaQuery()
                .eq(EngineeringStandardProjectMapping::getEngineeringProjectId, engineeringProjectId));

        // 找出主施工项目
        final EngineeringStandardProjectMapping mainStandardProject =
            mappingList.stream().filter(EngineeringStandardProjectMapping::getMain).findFirst().orElse(null);

        // 新增逻辑：查找与当前施工项目关联的其他施工项目
        final List<Long> associatedProjectIds = findAssociatedProjects(project); // 调用方法获取关联施工项目ID

        // 合并需要解除关联的施工项目ID
        // 过滤出与当前工程项目关联的施工项目
        final List<Long> allProjectIdsToUnmap =
            mappingList.stream().map(EngineeringStandardProjectMapping::getStandardProjectId)
                .filter(associatedProjectIds::contains).collect(Collectors.toList());
        allProjectIdsToUnmap.add(projectId); // 包含当前施工项目

        // 检查主施工项目是否是最后一个解除关联的
        // if (mainStandardProject != null &&
        // allProjectIdsToUnmap.contains(mainStandardProject.getStandardProjectId())
        // && mappingList.size() > allProjectIdsToUnmap.size()) {
        // // throw new FrameworkException(-1, "解除主施工项目的关联前请先解除其他非主施工项目关联");
        // // 清理所有：
        //
        // }
        // 如果allProjectIdsToUnmap里包含主施工项目，则解除所有关联
        if (mainStandardProject != null && allProjectIdsToUnmap.contains(mainStandardProject.getStandardProjectId())) {
            // 解除所有关联
            engineeringStandardProjectMappingService.removeByIds(
                mappingList.stream().map(EngineeringStandardProjectMapping::getId).collect(Collectors.toList()));
            // 触发施工项目解绑事件
            mappingList.forEach(mapping -> {
                // 获取每个施工项目的详细信息
                Project mappingProject = projectMapper.selectById(mapping.getStandardProjectId());
                if (mappingProject != null) {
                    // 使用新的事件构建器构建完整的解绑事件数据
                    ProjectBindingSegment transferData = ProjectTransferEventBuilder.buildUnbindingData(
                            mappingProject, engineeringProject, mapping);

                    publisher.publishEvent(new CpmProjectFlowEvent(this, mapping.getStandardProjectId(), transferData,
                            FlowNodeEnum.ENGINE_UNBINDING_STANDARD_PROJECT, FlowNodeHandlerEnum.POST,
                            FlowNodeDataTypeEnum.UPDATE));
                }
            });
        } else {
            // 解除当前施工项目及其兄弟、影子项目的关联
            allProjectIdsToUnmap.forEach(associatedProjectId -> {
                // 获取关联关系信息
                EngineeringStandardProjectMapping associatedMapping =
                        engineeringStandardProjectMappingService.getOne(Wrappers.<EngineeringStandardProjectMapping>lambdaQuery()
                                .eq(EngineeringStandardProjectMapping::getStandardProjectId, associatedProjectId)
                                .eq(EngineeringStandardProjectMapping::getEngineeringProjectId, engineeringProjectId));

                engineeringStandardProjectMappingService
                    .remove(Wrappers.<EngineeringStandardProjectMapping>lambdaQuery()
                        .eq(EngineeringStandardProjectMapping::getStandardProjectId, associatedProjectId));

                // 获取施工项目详细信息并触发解绑事件
                Project associatedProject = projectMapper.selectById(associatedProjectId);
                if (associatedProject != null && associatedMapping != null) {
                    // 使用新的事件构建器构建完整的解绑事件数据
                    ProjectBindingSegment transferData = ProjectTransferEventBuilder.buildUnbindingData(
                            associatedProject, engineeringProject, associatedMapping);

                    publisher.publishEvent(new CpmProjectFlowEvent(this, associatedProjectId, transferData,
                            FlowNodeEnum.ENGINE_UNBINDING_STANDARD_PROJECT, FlowNodeHandlerEnum.POST,
                            FlowNodeDataTypeEnum.UPDATE));
                }
            });
        }

        // 如果当前施工项目是主施工项目，则更新工程项目的主施工项目信息
        if (mainStandardProject != null && projectId.equals(mainStandardProject.getStandardProjectId())) {
            this.update(null,
                Wrappers.<EngineeringProject>lambdaUpdate().set(EngineeringProject::getMainProjectId, null)
                    .set(EngineeringProject::getParentId, Constants.NUMBER_ZERO)
                    .set(EngineeringProject::getUpdateBy, LoginUserUtil.userCode())
                    .set(EngineeringProject::getUpdateAt, Instant.now().toEpochMilli())
                    .set(EngineeringProject::getDeleted, Instant.now().toEpochMilli())
                    .eq(EngineeringProject::getId, engineeringProjectId));
        }

        return Boolean.TRUE;
    }

    /**
     * 查找关联项目(兄弟项目：工地挂接的项目；影子项目：财商编码一致的项目)
     *
     * @param project 项目
     * @return {@link List }<{@link Long }>
     */
    private List<Long> findAssociatedProjects(Project project) {
        List<Long> ids = new ArrayList<>();
        // 获取兄弟项目
        try {
            final ProjectDetailOutResp detailOutResp =
                eventCallbackService.getProjectInitInfo2Update(project.getCpmProjectKey());
            final List<HookDto> hookDTOList = detailOutResp.getHookDTOList();
            if (CollectionUtils.isNotEmpty(hookDTOList)) {
                final List<Long> stdIds = hookDTOList.stream().map(HookDto::getStandardProjectId)
                    .filter(StringUtils::isNotBlank).map(Long::valueOf).filter(stdId -> !stdId.equals(project.getId()))
                    .collect(Collectors.toList());
                ids.addAll(stdIds);
            }
        } catch (Exception e) {
            log.warn("获取工地挂接项目失败：{}", e.getMessage());
        }
        // 获取影子项目
        try {
            final List<Long> financeIds = projectMapper.selectList(Wrappers.<Project>lambdaQuery()
                            .eq(Project::getProjectFinanceCode, project.getProjectFinanceCode())
                            .ne(Project::getId, project.getId()))
                .stream().map(Project::getId).collect(Collectors.toList());
            ids.addAll(financeIds);
        } catch (Exception e) {
            log.error("获取财商孪生项目失败：{}", e.getMessage());
        }
        return ids;
    }

    @Override
    public Boolean unmappingEngineering2EngineeringProject(Long engineeringProjectId) {
        final EngineeringProject engineeringProject = this.getById(engineeringProjectId);
        if (Objects.isNull(engineeringProject)) {
            throw new FrameworkException(-1, "工程项目不存在，请检查后重试");
        }
        engineeringProject.setParentId(0L);
        return this.updateById(engineeringProject);
    }

    @Override
    public Boolean setMainProject(Long projectId, Long engineeringProjectId) {
        final Project project = checkProject(projectId);
        final EngineeringProject engineeringProject = this.getById(engineeringProjectId);
        if (Objects.isNull(engineeringProject)) {
            throw new FrameworkException(-1, "工程项目不存在，请检查后重试");
        }
        final List<EngineeringStandardProjectMapping> mappingList =
            engineeringStandardProjectMappingService.list(Wrappers.<EngineeringStandardProjectMapping>lambdaQuery()
                .eq(EngineeringStandardProjectMapping::getEngineeringProjectId, engineeringProjectId));
        if (CollectionUtils.isEmpty(mappingList)) {
            throw new FrameworkException(-1, "当前施工项目未关联任何工程项目，请检查后重试");
        }
        mappingList.forEach(mapping -> {
            mapping.setMain(projectId.equals(mapping.getStandardProjectId()));
        });
        engineeringStandardProjectMappingService.updateBatchById(mappingList);
        engineeringProject.setMainProjectId(projectId);
        return this.updateById(engineeringProject);
    }

    @Override
    public EngineeringProjectInfoResp getEngineeringProjectInfoByProject(Long projectId) {
        final EngineeringProjectInfoResp projectInfoResp = new EngineeringProjectInfoResp();
        final Project project = checkProject(projectId);
        final List<EngineeringStandardProjectMapping> mappingList =
            engineeringStandardProjectMappingService.list(Wrappers.<EngineeringStandardProjectMapping>lambdaQuery()
                .eq(EngineeringStandardProjectMapping::getStandardProjectId, projectId));
        if (CollectionUtils.isEmpty(mappingList)) {
            return projectInfoResp;
        }
        final Long engineeringProjectId = mappingList.get(0).getEngineeringProjectId();
        if (Objects.isNull(engineeringProjectId) || engineeringProjectId.equals(0L)) {
            return projectInfoResp;
        }
        EngineeringProject current = this.getById(engineeringProjectId);
        projectInfoResp.setCurrentProject(BeanMapUtils.map(current, EngineeringProjectVo.class));

        // 获取上级
        if (current.getParentId() != null && current.getParentId() != 0) {
            final EngineeringProject parent = this.getById(current.getParentId());
            projectInfoResp.setParentProject(BeanMapUtils.map(parent, EngineeringProjectVo.class));
        }

        // 获取下级
        final List<EngineeringProject> childList =
            this.list(Wrappers.<EngineeringProject>lambdaQuery().eq(EngineeringProject::getParentId, current.getId()));
        if (CollectionUtils.isNotEmpty(childList)) {
            projectInfoResp.setChildProjects(
                BeanMapUtils.mapList(childList, EngineeringProject.class, EngineeringProjectVo.class));
        }

        return projectInfoResp;
    }

    @Override
    public MappingStandardProjectResp getMappingStandardProjectByEngineeringProject(Long engineeringProjectId) {
        return this.baseMapper.getStandardProjectByEngineeringId(engineeringProjectId);
    }

    @Override
    public MappingStandardProjectResp getMappingStandardProjectByEngineeringAndSmartSite(Long engineeringProjectId,
        Boolean getSmartSite) {
        final String userCode = LoginUserUtil.userCode();
        final TodoTask todoTask = todoTaskMapper.selectOne(Wrappers.<TodoTask>lambdaQuery()
            .eq(TodoTask::getBillId, engineeringProjectId).eq(TodoTask::getTargetUser, userCode));
        if (Objects.isNull(todoTask)) {
            throw new FrameworkException(HttpStatus.FORBIDDEN.value(), "未获取到待办任务信息或无权限查看该任务信息)");
        }
        final MappingStandardProjectResp standardProjects =
            this.baseMapper.getStandardProjectByEngineeringId(engineeringProjectId);
        final List<StandardProjectResp> projectRespList = standardProjects.getProjectRespList();
        if (CollectionUtils.isNotEmpty(projectRespList)) {
            processSmartSiteStatus(standardProjects, getSmartSite);
        }
        return standardProjects;
    }

    /**
     * 处理 Smart Site 状态
     * <P>
     * 同时防止异常导致事务回滚
     * </P>
     *
     * @param standardProjects 标准项目
     * @param getSmartSite 获取 Smart Site
     */
    private void processSmartSiteStatus(MappingStandardProjectResp standardProjects, Boolean getSmartSite) {
        if (Boolean.TRUE.equals(getSmartSite)) {
            final List<StandardProjectResp> projectRespList = standardProjects.getProjectRespList();
            if (CollectionUtils.isNotEmpty(projectRespList)) {
                projectRespList.parallelStream().forEach(projectResp -> {
                    try {
                        final ProjectDetailOutResp detailOutResp =
                            eventCallbackService.getProjectInitInfo2Update(projectResp.getCpmProjectKey());
                        final List<HookDto> hookDTOList = detailOutResp.getHookDTOList();
                        if (CollectionUtils.isNotEmpty(hookDTOList)) {
                            projectResp.setSmartSiteStatus(true);
                        }
                    } catch (Exception e) {
                        log.warn("获取工地挂接项目失败：{}", e.getMessage());
                    }
                });
            }
        }
    }

    /**
     * 检查项目是否存在
     * 
     * @param projectId 项目id
     * @return {@link Project }
     */
    private Project checkProject(Long projectId) {
        Project project = projectMapper.selectById(projectId);
        if (Objects.isNull(project)) {
            throw new FrameworkException(-1, "施工项目不存在，请检查后重试");
        }
        return project;
    }

    @Override
    public Page<OpenEngineeringProjectPageResp> getOpenEngineeProjectPage(String openApiKey,
        OpenEngineerProjectPageReq pageReq) {
        // 检查客户端数据权限
        final ClientRequestDataPermission dataPermission =
            subscribeMapper.getClientRequestDataPermission(openApiKey, pageReq.getExecuteUnitId());
        if (Objects.isNull(dataPermission) || !dataPermission.isAuthorized()) {
            throw new FrameworkException(-1, "您无权限访问该组织的数据，请联系管理员");
        }
        // 有效的权限权限路径
        final String effectiveIdPath = dataPermission.getEffectiveIdPath();
        final com.github.pagehelper.Page<OpenEngineeringProjectPageResp> pageResps =
            PageHelper.startPage(pageReq.getCurrent(), pageReq.getSize())
                .doSelectPage(() -> this.baseMapper.getOpenEngineeringProjectPage(effectiveIdPath, pageReq));
        final Page<OpenEngineeringProjectPageResp> page =
            new Page<>(pageResps.getTotal(), pageResps.getPageNum(), pageResps.getPageSize());
        page.setRecords(pageResps.getResult());

        return page;
    }

    @Override
    public OpenEngineerProjectTreeResp getOpenEngineerProjectTreeByKey(String openApiKey, String key) {
        // 查询当前工程项目
        EngineeringProject currentProject = this.getOne(Wrappers.<EngineeringProject>lambdaQuery()
            .eq(EngineeringProject::getEngineeringKey, key).eq(EngineeringProject::getDeleted, 0));
        if (Objects.isNull(currentProject)) {
            throw new FrameworkException(-1, "未找到对应的工程项目，请检查后重试");
        }
        // 检查客户端数据权限
        final ClientRequestDataPermission dataPermission =
            subscribeMapper.getClientRequestDataPermission(openApiKey, currentProject.getMappingExecuteUnitId());
        if (Objects.isNull(dataPermission) || !dataPermission.isAuthorized()) {
            throw new FrameworkException(-1, "您无权限访问该组织的数据，请联系管理员");
        }

        // 获取全量树的ID列表
        List<Long> fullTreeIds = this.baseMapper.getFullTreeIds(currentProject.getEngineeringKey());

        // 查询全量树的所有工程项目
        List<OpenEngineerProjectTreeResp> fullTreeProjects = this.baseMapper.getTreeList(fullTreeIds);

        // 构建树结构

        return buildProjectTree(fullTreeProjects);
    }

    @Override
    public Boolean sendTodoTask(List<Long> engineerIds) {
        // 判断是否已发送过待办任务
        if (CollectionUtils.isEmpty(engineerIds)) {
            return Boolean.FALSE;
        }
        final List<String> longIds = engineerIds.stream().map(String::valueOf).collect(Collectors.toList());
        final List<String> noTaskIds = taskAndMessageService.checkExistTask(longIds);
        if (CollectionUtils.isEmpty(noTaskIds)) {
            log.info("已发送过待办任务");
            return Boolean.TRUE;
        }
        final List<EngineeringProject> engineeringProjects = this.listByIds(noTaskIds);
        final String appLink =
            "/project-management/supplemental-admission/list?taskCode=%s&billId=%s&operation=operation";
        final String webLink =
            "/project-management/supplemental-admission/list?taskCode=%s&billId=%s&operation=operation";
        final String engineerMsgConfig = Optional.ofNullable(openApiProperties.getTaskConfig())
            .map(TaskConfig::getMsgConfig).map(m -> m.get("engineer-msg-config")).orElse(null);
        final String host =
            Optional.ofNullable(openApiProperties.getTaskConfig()).map(TaskConfig::getHost).orElse(null);
        final String title = "工程项目信息补录";
        engineeringProjects.forEach(e -> {
            if (StringUtils.isBlank(e.getContactUserId())) {
                return;
            }
            final TaskAndMsgDto dto = TaskAndMsgDto.builder().taskCode(String.valueOf(e.getId()))
                .webLink(host + String.format(webLink, e.getId(), e.getId()))
                .appLink(host + String.format(appLink, e.getId(), e.getId()))
                .configCode(engineerMsgConfig)
                .payload(JsonUtils.toJsonStr(Maps.of("engineerName", e.getEngineeringName())))
                .targetUsers(Sets.newHashSet(e.getContactUserId())).billId(String.valueOf(e.getId()))
                .billType("engineering").billTypeName("工程项目").startUserName("项目中心").title(title)
                .startTime(LocalDateTime.now()).build();
            taskAndMessageService.sendTodoTaskAndMsg(dto);
        });
        return Boolean.TRUE;
    }

    @Override
    public Boolean recordDept(EngineerProjectRecordReq recordReq) {
        final Long engineerProjectId = recordReq.getEngineerProjectId();
        final EngineeringProject engineeringProject = this.getById(engineerProjectId);
        if (Objects.isNull(engineeringProject)) {
            throw new FrameworkException(-1, "工程项目不存在，请检查后重试");
        }
        final Long projectId = recordReq.getDeptMappingStandardProjectId();
        final Project project = checkProject(projectId);
        if (StringUtils.isBlank(project.getYunshuOrgId())) {
            throw new FrameworkException(-1, "该施工项目未关联到云枢，请检查后重试");
        }
        // 检查项目是否已关联到工程项目
        final List<EngineeringStandardProjectMapping> mappingList =
            engineeringStandardProjectMappingService.list(Wrappers.<EngineeringStandardProjectMapping>lambdaQuery()
                .eq(EngineeringStandardProjectMapping::getEngineeringProjectId, engineerProjectId));
        final boolean present = mappingList.stream().map(EngineeringStandardProjectMapping::getStandardProjectId)
            .anyMatch(id -> id.equals(projectId));
        if (!present) {
            throw new FrameworkException(-1, "该施工项目未关联到工程项目，请检查后重试");
        }
        // 设置项目部信息
        engineeringProject.setProjectDeptId(project.getYunshuOrgId());
        if (recordReq.getSyncAsMainStandardProject()) {
            engineeringProject.setMainProjectId(projectId);
            // 设置主施工项目
            mappingList.forEach(mapping -> {
                mapping.setMain(projectId.equals(mapping.getStandardProjectId()));
            });
            engineeringStandardProjectMappingService.updateBatchById(mappingList);
        }
        this.updateById(engineeringProject);
        return true;
    }

    @Override
    public Page<CompanyViewEngineeringProjectResp> companyViewPageList(CompanyViewEngineerProjectReq req) {
        Integer current = req.getCurrent();
        Integer size = req.getSize();
        com.github.pagehelper.Page<CompanyViewEngineeringProjectResp> pages = PageHelper.startPage(current, size)
                .doSelectPage(() -> this.baseMapper.getCompanyViewList(req));
        Page<CompanyViewEngineeringProjectResp> respPage = new Page<>(pages.getTotal(), current, size);
        respPage.setRecords(pages.getResult());
        return respPage;
    }

    @Override
    public List<CompanyViewEngineeringProjectTreeResp> getCompanyViewEngineeringProjectTree(Long engineerId) {
        List<CompanyViewEngineeringProjectTreeResp> projectList = this.baseMapper.getCompanyViewEngineeringProjectTree(engineerId);
        // 先将关联的施工项目转换为树结构
        projectList.forEach(project -> {
            if (project.getMappingStandardProjectResp() != null) {
                project.setMappingStandardProjectResp(convertStandardProjectsToTree(project.getMappingStandardProjectResp(), 0));
            }
        });
        // 再将工程项目列表转换为树结构
        return convertProjectsToTree(projectList, 0);
    }

    /**
     * 将关联的施工项目列表转换为树结构
     *
     * @param standardProjects 关联的施工项目列表
     * @return 转换后的树结构列表
     */
    private List<CompanyViewStandardProjectResp> convertStandardProjectsToTree(List<CompanyViewStandardProjectResp> standardProjects, int level) {
        if (standardProjects == null || standardProjects.isEmpty()) {
            return new ArrayList<>();
        }

        Map<String, CompanyViewStandardProjectResp> projectMap = new HashMap<>();
        List<CompanyViewStandardProjectResp> rootProjects = new ArrayList<>();

        // 构建映射表
        for (CompanyViewStandardProjectResp project : standardProjects) {
            projectMap.put(project.getProjectFinanceCode(), project);
        }

        // 构建树结构
        for (CompanyViewStandardProjectResp project : standardProjects) {
            String parentCode = project.getParentCode();
            project.setLevel(level);
            if (parentCode == null || parentCode.isEmpty() || !projectMap.containsKey(parentCode)) {
                // 若父节点不存在，将当前节点作为根节点
                rootProjects.add(project);
            } else {
                CompanyViewStandardProjectResp parent = projectMap.get(parentCode);
                if (parent.getChildren() == null) {
                    parent.setChildren(new ArrayList<>());
                }
                parent.getChildren().add(project);
            }
        }


        // 递归设置子节点层级
        for (CompanyViewStandardProjectResp root : rootProjects) {
            setChildrenLevel(root, level + 1);
        }

        return rootProjects;
    }

    /**
     * 递归设置子节点层级
     *
     * @param parent 父节点
     * @param level  当前层级
     */
    private void setChildrenLevel(CompanyViewStandardProjectResp parent, int level) {
        if (parent.getChildren() != null) {
            for (CompanyViewStandardProjectResp child : parent.getChildren()) {
                child.setLevel(level);
                setChildrenLevel(child, level + 1);
            }
        }
    }

    /**
     * 将工程项目列表转换为树结构
     *
     * @param projectList 工程项目列表
     * @return 转换后的树结构根节点
     */
    private List<CompanyViewEngineeringProjectTreeResp> convertProjectsToTree(List<CompanyViewEngineeringProjectTreeResp> projectList, int level) {
        if (projectList == null || projectList.isEmpty()) {
            return null;
        }

        Map<Long, CompanyViewEngineeringProjectTreeResp> projectMap = new HashMap<>();
        List<CompanyViewEngineeringProjectTreeResp> rootProjects = new ArrayList<>();

        // 构建映射表
        for (CompanyViewEngineeringProjectTreeResp project : projectList) {
            projectMap.put(project.getId(), project);
        }

        // 构建树结构
        for (CompanyViewEngineeringProjectTreeResp project : projectList) {
            Long parentId = project.getParentId();
            project.setLevel(level);
            if (parentId == null || parentId == 0L || !projectMap.containsKey(parentId)) {
                // 若父节点不存在，将当前节点作为根节点
                rootProjects.add(project);
            } else {
                CompanyViewEngineeringProjectTreeResp parent = projectMap.get(parentId);
                if (parent.getChildren() == null) {
                    parent.setChildren(new ArrayList<>());
                }
                parent.getChildren().add(project);
            }
        }

        // 递归设置子节点层级
        for (CompanyViewEngineeringProjectTreeResp root : rootProjects) {
            setEngineeringChildrenLevel(root, level + 1);
        }

        return rootProjects;
    }

    /**
     * 递归设置工程项目子节点层级
     *
     * @param parent 父节点
     * @param level  当前层级
     */
    private void setEngineeringChildrenLevel(CompanyViewEngineeringProjectTreeResp parent, int level) {
        if (parent.getChildren() != null) {
            for (CompanyViewEngineeringProjectTreeResp child : parent.getChildren()) {
                child.setLevel(level);
                setEngineeringChildrenLevel(child, level + 1);
            }
        }
    }
    /**
     * 构建工程项目树结构（基于全量树的工程项目列表）
     *
     * @param fullTreeProjects 全量树的所有工程项目
     * @return {@link OpenEngineerProjectTreeResp}
     */
    private OpenEngineerProjectTreeResp buildProjectTree(List<OpenEngineerProjectTreeResp> fullTreeProjects) {
        // 基于parentId，递归构建tree结构
        if (CollectionUtils.isEmpty(fullTreeProjects)) {
            return null;
        }
        // 创建一个映射表，用于快速查找节点
        final java.util.Map<Long, OpenEngineerProjectTreeResp> nodeMap =
            new java.util.HashMap<>(fullTreeProjects.size());
        for (OpenEngineerProjectTreeResp node : fullTreeProjects) {
            nodeMap.put(node.getId(), node);
        }

        // 根节点
        OpenEngineerProjectTreeResp root = null;

        // 遍历所有节点，将子节点添加到父节点的children列表中
        for (OpenEngineerProjectTreeResp node : fullTreeProjects) {
            // 获取父节点ID
            Long parentId = node.getParentId();

            // 如果父节点ID为0或null，则该节点为根节点
            if (parentId == null || parentId == 0L) {
                root = node;
                continue;
            }

            // 获取父节点
            OpenEngineerProjectTreeResp parentNode = nodeMap.get(parentId);
            if (parentNode != null) {
                // 初始化父节点的children列表（如果为null）
                if (parentNode.getChildren() == null) {
                    parentNode.setChildren(new ArrayList<>());
                }
                // 将当前节点添加到父节点的children列表中
                parentNode.getChildren().add(node);
            }
        }
        // 返回根节点
        return root;
    }

    /**
     * 施工项目换绑
     *
     * @param req 要求
     * @return {@link Boolean }
     */
    @Override
    public Boolean transferProjectMapping(TransferProjectMappingReq req) {
        // 获取相关项目信息用于构建事件数据
        final Project project = checkProject(req.getProjectId());
        final EngineeringProject sourceEngineering = this.getById(req.getCurrentEngineeringProjectId());
        final EngineeringProject targetEngineering = this.getById(req.getTargetEngineeringProjectId());

        // 1.解除当前工程项目与施工项目的关联
        this.unmappingProject2EngineeringProject(req.getProjectId());

        // 2.关联施工项目到目标工程项目
        MappingStandard2EngineeringProjectReq engineeringProjectReq = new MappingStandard2EngineeringProjectReq();
        engineeringProjectReq.setEngineeringProjectId(req.getTargetEngineeringProjectId());
        engineeringProjectReq.setProjectId(req.getProjectId());
        this.mappingProject2EngineeringProject(engineeringProjectReq);

        // 3.触发施工项目换绑事件
        ProjectBindingSegment transferData = ProjectTransferEventBuilder.buildTransferData(
                project, sourceEngineering, targetEngineering);
        publisher.publishEvent(new CpmProjectFlowEvent(this, project.getId(), transferData,
                FlowNodeEnum.ENGINE_TRANSFER_STANDARD_PROJECT, FlowNodeHandlerEnum.POST, FlowNodeDataTypeEnum.UPDATE));

        return Boolean.TRUE;
    }

    @Override
    public Page<OpenEngineeringArchiveResp> getEngineeringArchivedPage(String openApiKey, OpenEngineeringArchiveReq pageReq) {
        String dataIdPath = dataScopeService.getAppDataIdPathByAppKey(openApiKey, null);
        final com.github.pagehelper.Page<OpenEngineeringArchiveResp> pageResps =
                PageHelper.startPage(pageReq.getCurrent(), pageReq.getSize())
                        .doSelectPage(() -> this.baseMapper.getEngineeringArchivedPage(dataIdPath, pageReq));
        final Page<OpenEngineeringArchiveResp> page =
                new Page<>(pageResps.getTotal(), pageResps.getPageNum(), pageResps.getPageSize());
        page.setRecords(pageResps.getResult());
        return page;
    }

    @Override
    public Page<OpenEngineeringProjectMappingResp> getEnginAndStandardProjectMappingInfo(String openApiKey, OpenEngineeringMappingArchiveReq req) {
        String dataIdPath = dataScopeService.getAppDataIdPathByAppKey(openApiKey, null);
        final com.github.pagehelper.Page<OpenEngineeringProjectMappingResp> pageResps =
                PageHelper.startPage(req.getCurrent(), req.getSize())
                        .doSelectPage(() -> this.baseMapper.getEnginAndStandardProjectMappingInfo(dataIdPath, req));
        final Page<OpenEngineeringProjectMappingResp> respPage =
                new Page<>(pageResps.getTotal(), pageResps.getPageNum(), pageResps.getPageSize());
        respPage.setRecords(pageResps.getResult());
        return respPage;
    }
}
