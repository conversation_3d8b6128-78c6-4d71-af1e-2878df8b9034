package com.cscec3b.iti.projectmanagement.server.service.impl;

import cn.hutool.core.bean.BeanUtil;
import com.cscec3b.iti.common.base.dictionary.YesNoEnum;
import com.cscec3b.iti.common.base.page.Page;
import com.cscec3b.iti.common.web.exception.BusinessException;
import com.cscec3b.iti.projectmanagement.api.dto.dto.AttachmentDto;
import com.cscec3b.iti.projectmanagement.api.dto.request.special.CreateSpecialProjectReq;
import com.cscec3b.iti.projectmanagement.api.dto.request.special.QuerySpecialProjectParams;
import com.cscec3b.iti.projectmanagement.api.dto.request.special.SpecialProjectDto;
import com.cscec3b.iti.projectmanagement.api.dto.request.special.UpdateSpecialProjectReq;
import com.cscec3b.iti.projectmanagement.api.dto.response.special.SpecialProjectDetailResp;
import com.cscec3b.iti.projectmanagement.api.dto.response.special.SpecialProjectResp;
import com.cscec3b.iti.projectmanagement.server.config.SequenceConfig;
import com.cscec3b.iti.projectmanagement.server.constant.Constants;
import com.cscec3b.iti.projectmanagement.server.entity.Attachment;
import com.cscec3b.iti.projectmanagement.server.entity.Project;
import com.cscec3b.iti.projectmanagement.server.entity.ProjectProgress;
import com.cscec3b.iti.projectmanagement.server.enums.OSSPathTypeEnum;
import com.cscec3b.iti.projectmanagement.server.enums.ProjectClassEnum;
import com.cscec3b.iti.projectmanagement.server.enums.ProjectEnum;
import com.cscec3b.iti.projectmanagement.server.enums.SourceSystemEnum;
import com.cscec3b.iti.projectmanagement.server.enums.WarningStatusEnum;
import com.cscec3b.iti.projectmanagement.server.mapper.ProjectMapper;
import com.cscec3b.iti.projectmanagement.server.pushservice.enums.FlowNodeDataTypeEnum;
import com.cscec3b.iti.projectmanagement.server.pushservice.enums.FlowNodeEnum;
import com.cscec3b.iti.projectmanagement.server.pushservice.enums.FlowNodeHandlerEnum;
import com.cscec3b.iti.projectmanagement.server.pushservice.enums.ProjectEventEnum;
import com.cscec3b.iti.projectmanagement.server.pushservice.event.CpmProjectFlowEvent;
import com.cscec3b.iti.projectmanagement.server.pushservice.event.CpmProjectUpdateEvent;
import com.cscec3b.iti.projectmanagement.server.service.IAttachmentService;
import com.cscec3b.iti.projectmanagement.server.service.ProjectProgressService;
import com.cscec3b.iti.projectmanagement.server.service.ProjectService;
import com.cscec3b.iti.projectmanagement.server.service.SpecialProjectService;
import com.cscec3b.iti.projectmanagement.server.util.LoginUserUtil;
import com.github.pagehelper.PageHelper;
import com.odin.freyr.common.orika.BeanMapUtils;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.lang3.StringUtils;
import org.springframework.beans.BeanUtils;
import org.springframework.context.ApplicationEventPublisher;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;

import javax.annotation.Resource;
import java.time.Instant;
import java.util.ArrayList;
import java.util.Arrays;
import java.util.Collections;
import java.util.List;
import java.util.Objects;
import java.util.stream.Collectors;


/**
 * <AUTHOR>
 * @description SpecialProjectServiceImpl
 * @date 2023/02/14 15:55
 */
@Slf4j
@Service
public class SpecialProjectServiceImpl implements SpecialProjectService {
    private static final int DAY = 60 * 60 * 24;
    private static final int INT = 1000;
    @Resource
    private ProjectService projectService;

    @Resource
    private ProjectProgressService projectProgressService;

    @Resource
    private ProjectMapper projectMapper;

    @Resource
    private IAttachmentService attachmentService;

    @Resource
    private ApplicationEventPublisher publisher;

    @Resource
    private SequenceConfig sequenceConfig;

    /**
     * @param createSpecialProjectReq
     * @return Boolean
     * @description 新增特殊立项
     * @date 2023/02/14 15:25
     * <AUTHOR>
     */
    @Override
    @Transactional(rollbackFor = Exception.class)
    public Boolean create(CreateSpecialProjectReq createSpecialProjectReq) {
        log.info("创建特殊立项CreateSpecialProjectReq:{}",createSpecialProjectReq);
        Project project = new Project();
        //根据项目分类创建不同类型项目记录填充项目属性入库
        fillProjectProperties(project, createSpecialProjectReq);

        // 映射云枢组织信息
//        projectService.executeUnitMapping(project);

//        projectService.fillFinancialBusinessSegment(project);

        final String cpmMark = projectService.getCpmProjectKey();
        final String cpmProjectAbbreviation = StringUtils.isBlank(createSpecialProjectReq.getProjectAbbreviation()) ?
                createSpecialProjectReq.getProjectName() : createSpecialProjectReq.getProjectAbbreviation();
        project.setCpmProjectKey(cpmMark).setCpmProjectName(createSpecialProjectReq.getProjectName())
                .setCpmProjectAbbreviation(cpmProjectAbbreviation);
        //项目信息入库
        Integer result = projectMapper.createProject(project);
        if (Constants.NUMBER_ZERO.equals(result)) {
            throw new BusinessException(8010012);
        }
        //用户有附件信息时，保存附件信息
        if (!Constants.NUMBER_ZERO.equals(createSpecialProjectReq.getAttachments().size())) {
            List<Attachment> attachments =
                    this.convertAttachments(createSpecialProjectReq.getAttachments(), String.valueOf(project.getId()));
            attachmentService.batchSave(attachments);
        }
        //创建项目进度
         projectProgressService.createProgressBySpecial(project.getId());

        // 独立非重大，创建项目部项目，触发项目立项事件
        log.info("特殊立项:1.插入项目进度表--->2.推送财商系统接口");
        // 触发项目流转事件(特殊立项 后置事件)
        publisher.publishEvent(new CpmProjectFlowEvent(this, project.getId(), FlowNodeEnum.SPECIAL_PROJECT,
                FlowNodeHandlerEnum.POST, FlowNodeDataTypeEnum.CREATE));

        // 1、插入项目进度表  2、调用财商系统立项
//        publisher.publishEvent(new CpmProjectApprovalEvent(this, ProjectEventEnum.INITIATION, project.getId()));
        return true;
    }

    /**
     * 填充项目属性字段，该项目为建造类项目,全量接收创建请求属性,否则仅设置部分非建造字段
     *
     * @param project
     * @param createReq
     * @return
     * @date 2023/02/20 15:27
     * <AUTHOR>
     */
    private void fillProjectProperties(Project project, CreateSpecialProjectReq createReq) {
        log.info("填充项目属性字段fillProjectProperties,Project:{},CreateSpecialProjectReq:{}", project, createReq);
        //提取项目分类IdPath的根类别
        String standardType = Arrays.stream(createReq.getStandardTypeCodePath().split(Constants.ID_PATH_CONNECTOR))
                .filter(StringUtils::isNotBlank).collect(Collectors.toList()).get(Constants.NUMBER_ZERO);
        String constructProject = ProjectClassEnum.CONSTRUCTION_PROJECT.getDictCode().toString();

        if (standardType.equals(constructProject)) {
            //建造类项目
            BeanUtils.copyProperties(createReq, project);
            //总工期 = 合同竣工时间-合同开工时间+1天
            if (!Objects.isNull(createReq.getWorkerEndTime()) && !Objects.isNull(createReq.getWorkerBeginTime())
                    && createReq.getWorkerEndTime()>=createReq.getWorkerBeginTime()) {
                long countDays = ((createReq.getWorkerEndTime() - createReq.getWorkerBeginTime()) / DAY / INT) + 1;
                project.setCountDays((int) countDays);
            }else {
                project.setCountDays(null);
            }
            project.setProjectManager(createReq.getContractManager());
        } else {
            //非建造类项目,拷贝部分属性
            SpecialProjectDto dto = new SpecialProjectDto();
            BeanUtils.copyProperties(createReq, dto);
            BeanUtils.copyProperties(dto, project);
            //切换云枢组织
            project.setYunshuExecuteUnit(createReq.getYunshuExecuteUnit())
                    .setYunshuExecuteUnitCode(createReq.getYunshuExecuteUnitCode())
                    .setYunshuExecuteUnitId(createReq.getYunshuExecuteUnitId())
                    .setYunshuExecuteUnitIdPath(createReq.getYunshuExecuteUnitIdPath())
                    .setYunshuExecuteUnitAbbreviation(createReq.getYunshuExecuteUnitAbbreviation());
        }
        // 手动填充财商编码
        project.setMarketingBusinessSegment(createReq.getFinancialBusinessSegment())
                .setMarketingBusinessSegmentCodePath(createReq.getBusinessSegmentCodePath())
                .setCpmBusinessSegment(createReq.getFinancialBusinessSegment())
                .setCpmBusinessSegmentCodePath(createReq.getBusinessSegmentCodePath());
        project.setSourceSystem(SourceSystemEnum.SPECIAL_PROJECT.getDictCode())
                .setCreateAt(Instant.now().toEpochMilli())
                .setUpdateAt(Instant.now().toEpochMilli())
                .setProjectStatus(ProjectEnum.PROJECT_APPROVAL.getCode());
        project.setProjectName(createReq.getProjectName());
    }

    /**
     * 更新特殊立项
     * @param updateReq updateReq
     * @return Boolean
     * @date 2023/02/14 15:29
     * <AUTHOR>
     */
    @Override
    @Transactional(rollbackFor = Exception.class)
    public Boolean update(UpdateSpecialProjectReq updateReq) {
        log.info("更新特殊立项UpdateSpecialProjectReq:{}", updateReq);
        Long projectId = updateReq.getId();
        Project existProject = projectMapper.selectById(projectId);
        if (Objects.isNull(existProject)) {
            throw new BusinessException(8010502, new String[]{String.valueOf(projectId)});
        }
        final String cpmProjectKey = existProject.getCpmProjectKey();
        final String reqCpmProjectKey = updateReq.getCpmProjectKey();
        // 只有数据表里为空，才能新增，不为空时不能编辑
        if (StringUtils.isNotBlank(cpmProjectKey) && StringUtils.isNotBlank(reqCpmProjectKey)
                && !Objects.equals(cpmProjectKey, reqCpmProjectKey)) {
            throw new BusinessException(80107002);
        }
        // 判断唯一性
        final int countIdByCpmProjectKey = projectMapper.getCountIdByCpmProjectKey(reqCpmProjectKey, projectId);
        if (countIdByCpmProjectKey == 1) {
            throw new BusinessException(80107003);
        }

        List<Attachment> oldAttachments = attachmentService
                .getByBusinessIdsAndType(Collections.singletonList(projectId.toString()), OSSPathTypeEnum.SPECIAL_PROJECT.getCode());
        log.info("删除特殊立项旧附件信息:{}", oldAttachments);
        //如果旧项目存在附件信息，删除旧附件，保存新附件
        if (!Constants.NUMBER_ZERO.equals(oldAttachments.size())) {
            if (!attachmentService.batchDelete(Collections.singletonList(String.valueOf(projectId)),
                    OSSPathTypeEnum.SPECIAL_PROJECT.getCode())) {
                throw new BusinessException(8010404, new String[]{projectId.toString()});
            }
        }
        List<Attachment> attachments =
                this.convertAttachments(updateReq.getAttachments(), String.valueOf(projectId));
        log.info("保存特殊立项新附件信息:{}", attachments);
        attachmentService.batchSave(attachments);

        //判断项目分类,更新已存在的项目属性信息
        updateProjectProperties(existProject, updateReq);
        // 手动填充财商编码
        existProject.setMarketingBusinessSegment(updateReq.getFinancialBusinessSegment())
                .setMarketingBusinessSegmentCodePath(updateReq.getBusinessSegmentCodePath())
                .setCpmBusinessSegment(updateReq.getFinancialBusinessSegment())
                .setCpmBusinessSegmentCodePath(updateReq.getBusinessSegmentCodePath());
        Integer result = projectMapper.updateProjectById(existProject);
        if (Constants.NUMBER_ZERO.equals(result)) {
            throw new BusinessException(8010503, new String[]{String.valueOf(projectId)});
        }
        publisher.publishEvent(new CpmProjectUpdateEvent(this, ProjectEventEnum.CPM_UPDATE, projectId, new ArrayList<>()));
        return true;
    }

    /**
     * 判断已存项目分类与更新请求中的项目分类：
     * 1：若已存为建造、更新为建造 或 已存为非建造、更新为建造则全量更新
     * 2：若已存为建造、更新为非建造则清空项目相关字段，只更新部分的非建造的属性
     * 3：若已存为非建造、更新仍为非建造，则仅更新部分非建造属性
     *
     * @param existProject 已存项目信息
     * @param updateReq    更新字段信息
     * @return
     * @date 2023/02/20 15:14
     * <AUTHOR>
     */
    private void updateProjectProperties(Project existProject, UpdateSpecialProjectReq updateReq) {
        String constructProject = ProjectClassEnum.CONSTRUCTION_PROJECT.getDictCode().toString();
//        //已存项目的项目分类
//        String existProClass = Arrays.stream(existProject.getProjectClassIdPath().split(Constants.ID_PATH_CONNECTOR))
//                .filter(StringUtils::isNotBlank).collect(Collectors.toList()).get(Constants.NUMBER_ZERO);
//        //更新项目的项目分类
//        String reqProClass = Arrays.stream(updateReq.getProjectClassIdPath().split(Constants.ID_PATH_CONNECTOR))
//                .filter(StringUtils::isNotBlank).collect(Collectors.toList()).get(Constants.NUMBER_ZERO);

        //已存项目的项目标准分类
        String existProClass = Arrays.stream(existProject.getStandardTypeCodePath().split(Constants.ID_PATH_CONNECTOR))
                .filter(StringUtils::isNotBlank).collect(Collectors.toList()).get(Constants.NUMBER_ZERO);
        //更新项目的项目标准分类
        String reqProClass = Arrays.stream(updateReq.getStandardTypeCodePath().split(Constants.ID_PATH_CONNECTOR))
                .filter(StringUtils::isNotBlank).collect(Collectors.toList()).get(Constants.NUMBER_ZERO);
        //特殊立项工程名称不可编辑
//        existProject.setProjectName(updateReq.getProjectName());
        existProject.setCpmProjectKey(updateReq.getCpmProjectKey());
        if (constructProject.equals(existProClass) && constructProject.equals(reqProClass)
                || !constructProject.equals(existProClass) && constructProject.equals(reqProClass)) {
            //已存为建造、更新为建造 或 已存为非建造、更新为建造则全量更新
            BeanUtils.copyProperties(updateReq, existProject);
            //总工期 = 合同竣工时间-合同开工时间+1天
            if (!Objects.isNull(updateReq.getWorkerEndTime()) && !Objects.isNull(updateReq.getWorkerBeginTime())
                    && updateReq.getWorkerEndTime()>=updateReq.getWorkerBeginTime()) {
                long countDays = ((updateReq.getWorkerEndTime() - updateReq.getWorkerBeginTime()) / INT / DAY) + 1;
                existProject.setCountDays((int) countDays);
            } else {
                existProject.setCountDays(null);
            }
            existProject.setProjectManager(updateReq.getContractManager()).setUpdateAt(Instant.now().toEpochMilli());
        } else if (constructProject.equals(existProClass) && !constructProject.equals(reqProClass)) {
            //若已存为建造、更新为非建造则清空项目相关字段，只更新部分的非建造的属性
            //仅有部分属性的非建造项目对象
            SpecialProjectDto dto = new SpecialProjectDto();
            BeanUtils.copyProperties(updateReq, dto);
            Project updateProject = new Project();
            BeanUtils.copyProperties(dto, updateProject);
            updateProject.setId(existProject.getId())
                    .setSourceSystem(existProject.getSourceSystem())
                    .setCreateAt(existProject.getCreateAt())
                    .setUpdateAt(Instant.now().toEpochMilli())
                    .setProjectStatus(existProject.getProjectStatus())
                    .setProjectFinanceName(existProject.getProjectFinanceName())
                    .setProjectFinanceCode(existProject.getProjectFinanceCode())
                    .setProjectFinanceAbbreviation(existProject.getProjectFinanceAbbreviation())
                    .setA8ProjectCode(existProject.getA8ProjectCode())
                    .setProjectDeptId(existProject.getProjectDeptId())
                    .setProjectDeptType(existProject.getProjectDeptType())
                    .setProjectDeptName(existProject.getProjectDeptName())
                    .setProjectDeptIdPath(existProject.getProjectDeptIdPath())
                    .setYunshuOrgId(existProject.getYunshuOrgId())
//                    .setProjectName(existProject.getProjectName())
                    .setYunshuExecuteUnit(existProject.getYunshuExecuteUnit())
                    .setYunshuExecuteUnitId(existProject.getYunshuExecuteUnitId())
                    .setYunshuExecuteUnitIdPath(existProject.getYunshuExecuteUnitIdPath())
                    .setYunshuExecuteUnitCode(existProject.getYunshuExecuteUnitCode());


            BeanUtils.copyProperties(updateProject, existProject);
        } else {
            //若已存为非建造、更新仍为非建造，则仅更新部分非建造属性
            SpecialProjectDto dto = new SpecialProjectDto();
            BeanUtils.copyProperties(updateReq, dto);
            BeanUtils.copyProperties(dto, existProject);
            existProject.setUpdateAt(Instant.now().toEpochMilli());
        }
    }

    /**
     * @param id 项目id
     * @return SpecialProjectDetailResp
     * @date 2023/02/14 15:36
     * <AUTHOR>
     */
    @Override
    public SpecialProjectDetailResp getDetail(Long id) {
        Project project = projectMapper.selectById(id);
        if (Objects.isNull(project) || Objects.isNull(project.getId())) {
            // 特殊立项为空
            throw new BusinessException(8010502, new String[]{String.valueOf(id)});
        }
        List<Attachment> attachmentList = attachmentService
                .getByBusinessIdsAndType(Collections.singletonList(id.toString()), OSSPathTypeEnum.SPECIAL_PROJECT.getCode());

        SpecialProjectDetailResp resp = BeanUtil.copyProperties(project, SpecialProjectDetailResp.class);
        List<AttachmentDto> attachmentDtoList = attachmentList.stream().map(attachment ->
                BeanUtil.copyProperties(attachment, AttachmentDto.class)).collect(Collectors.toList());

        resp.setContractManager(project.getProjectManager()).setAttachments(attachmentDtoList);
        return resp;
    }

    /**
     * @param params
     * @return Page<SpecialProjectResp>
     * @date 2023/02/14 15:51
     * <AUTHOR>
     */
    @Override
    public Page<SpecialProjectResp> specialProjectList(QuerySpecialProjectParams params) {
        com.github.pagehelper.Page<SpecialProjectResp> page = PageHelper.startPage(params.getCurrent(), params.getSize())
                .doSelectPage(() -> projectMapper.specialProjectList(params));
        Page<SpecialProjectResp> respPage = new Page<>(page.getTotal(), page.getPageNum(), page.getPageSize());
        respPage.setRecords(page.getResult());
        return respPage;
    }

    /**
     * 附件信息请求对象转换为附件存储实体对象
     *
     * @param attachmentDtos
     * @param projectId
     * @return List<Attachment>
     * @date 2023/02/16 17:02
     * <AUTHOR>
     */
    private List<Attachment> convertAttachments(List<AttachmentDto> attachmentDtos, String projectId) {
        return attachmentDtos.stream().map(dto -> {
                    Attachment attachment = BeanMapUtils.map(dto, Attachment.class);
                    attachment.setBusinessId(projectId)
                            .setBusinessType(OSSPathTypeEnum.SPECIAL_PROJECT.getCode())
                            .setCreateAt(String.valueOf(Instant.now().toEpochMilli()))
                            .setCreateBy(LoginUserUtil.userId());
                    return attachment;
                }
        ).collect(Collectors.toList());
    }

    /**
     * 更新项目进度
     *
     * @param progress
     * @date 2023/02/17 16:20
     * <AUTHOR>
     */
    private void updateProgress(ProjectProgress progress) {
        //未签约、未立项、未触发预警
        progress.setSignStatus(YesNoEnum.NO.getDictCode()).setApproveStatus(YesNoEnum.NO.getDictCode());
        progress.setWarnStatus(WarningStatusEnum.NOT_TRIGGERED.getDictCode());
        projectProgressService.saveProjectProgress(progress);
    }


    /**
     * @param params 查询参数
     * @return
     */
    @Override
    public Page<SpecialProjectResp> specialProjectListCloudPivot(QuerySpecialProjectParams params) {
        com.github.pagehelper.Page<SpecialProjectResp> page = PageHelper.startPage(params.getCurrent(), params.getSize())
                .doSelectPage(() -> projectMapper.specialProjectListCloudPivot(params));
        Page<SpecialProjectResp> respPage = new Page<>(page.getTotal(), page.getPageNum(), page.getPageSize());
        respPage.setRecords(page.getResult());
        return respPage;
    }
}
