package com.cscec3b.iti.projectmanagement.server.enums;

import com.cscec3b.iti.common.base.dictionary.IDataDictionary;
import lombok.Getter;

import java.util.Optional;

/**
 * <AUTHOR>
 * @description SourceSystemEnum
 * @date 2023/02/14 16:58
 */
@Getter
public enum SourceSystemEnum implements IDataDictionary {
    /**
     * 市场营销
     */
    // 系统来源
    MARKETING(1, "市场营销", "marketing"),
    
    /**
     * 特殊立项
     */
    SPECIAL_PROJECT(2, "特殊立项", "special_project");

    SourceSystemEnum(Integer dictCode, String zhCN, String enUS) {
        this.dictCode = dictCode;
        this.zhCN = zhCN;
        this.enUS = enUS;
    }

    final Integer dictCode;

    final String zhCN;

    final String enUS;

    @Override
    public Integer getDictCode() {
        return dictCode;
    }

    @Override
    public String getZhCN() {
        return zhCN;
    }

    @Override
    public String getEnUS() {
        return enUS;
    }

    /**
     * 增加说明
     */
    @Override
    public String getDesc() {
        return "来源系统枚举类";
    }

    public static SourceSystemEnum getByCode(Integer code) {
        for (SourceSystemEnum value : SourceSystemEnum.values()) {
            if (value.getDictCode().equals(code)) {
                return value;
            }
        }
        return null;
    }

    public static String getZhcnByCode(Integer code) {
        return Optional.ofNullable(code).map(SourceSystemEnum::getByCode).map(SourceSystemEnum::getZhCN).orElse(null);
    }
}
