//package com.cscec3b.iti.projectmanagement.server.service;
//
//import cn.hutool.json.JSONUtil;
//import com.cscec3b.iti.common.base.json.JsonUtils;
//import com.cscec3b.iti.common.web.config.SpringUtils;
//import com.cscec3b.iti.common.web.exception.BusinessException;
//import com.cscec3b.iti.common.web.exception.FrameworkException;
//import com.cscec3b.iti.projectmanagement.api.dto.request.approvalstep.ApprovalStepReq;
//import com.cscec3b.iti.projectmanagement.server.entity.BidApproval;
//import com.cscec3b.iti.projectmanagement.server.entity.Project;
//import com.cscec3b.iti.projectmanagement.server.enums.ApprovalStepEnum;
//import com.cscec3b.iti.projectmanagement.server.mapper.BidApprovalMapper;
//import com.cscec3b.iti.projectmanagement.server.mapper.ProjectMapper;
//import com.cscec3b.iti.projectmanagement.server.pushservice.enums.FlowNodeEnum;
//import com.cscec3b.iti.projectmanagement.server.pushservice.enums.FlowNodeHandlerEnum;
//import com.cscec3b.iti.projectmanagement.server.pushservice.event.CpmProjectFlowEvent;
//import com.cscec3b.iti.projectmanagement.server.util.LoginUserUtil;
//import com.fasterxml.jackson.core.type.TypeReference;
//import lombok.RequiredArgsConstructor;
//import lombok.extern.slf4j.Slf4j;
//import org.apache.commons.collections4.CollectionUtils;
//import org.apache.commons.lang3.ObjectUtils;
//import org.springframework.context.event.EventListener;
//import org.springframework.stereotype.Component;
//
//import java.time.Instant;
//import java.util.List;
//import java.util.Objects;
//import java.util.Optional;
//
//@Slf4j
//@Component
//@RequiredArgsConstructor
//public abstract class AbstractApprovalStepService implements IApprovalStepService {
//
//    protected BidApprovalMapper bidApprovalMapper;
//
//    protected ProjectMapper projectMapper;
//
//
//    @Override
//    public abstract Integer currentStep();
//
//
//    @Override
//    public Integer nextStep(BidApproval bidApproval) {
//        if (ObjectUtils.isEmpty(bidApproval)) {
//            throw new BusinessException(8010320);
//        }
//        // 步骤为空时取默认步骤
//        final String stetJsonStr =
//                Optional.of(bidApproval).map(BidApproval::getStepList).orElseGet(() -> JSONUtil.toJsonStr(STEP_LIST));
//        final List<Integer> steps = JsonUtils.readValue(stetJsonStr, new TypeReference<List<Integer>>() {
//        });
//        if (CollectionUtils.isEmpty(steps)) {
//            throw new FrameworkException(-1, "步骤列表数据异常");
//        }
//        log.info("steps : {}", steps);
//        final Integer currentStepNo = bidApproval.getCurrentStepNo();
//        log.info("currentStepNo : [{}]", currentStepNo);
//        // 如果当前已经是最后一步了,则返回结束步骤
//        if (ApprovalStepEnum.END.getNo().equals(currentStepNo)) {
//            log.info("currentStepNo : [{}] is last step, return End", currentStepNo);
//            return ApprovalStepEnum.END.getNo();
//        } else if (currentStepNo.equals(steps.get(steps.size() - 1))) {
//            log.info("currentStepNo : [{}] is last step, return End", currentStepNo);
//            return ApprovalStepEnum.END.getNo();
//        } else {
//            log.info("currentStepNo : [{}] is not last step, return next step", currentStepNo);
//            return steps.get(steps.indexOf(currentStepNo) + 1);
//        }
//    }
//
//    @Override
//    public boolean saveCurrentStep(ApprovalStepReq stepReq) {
//        return true;
//    }
//
//
//    @Override
//    public void submitApprovalStep(BidApproval bidApproval) {
//        // 校验当前步骤信息是否完成
//        final String name = this.getClass().getName();
//        log.info("submitApprovalStep_name : [{}], submitApprovalStep", name);
//        if (checkCurrentStep(bidApproval)) {
//            // 保存当前步骤信息
//            getBidApprovalMapper().updateById(bidApproval);
//            // 进入下一步骤
//            final Integer nextStep = nextStep(bidApproval);
//            final String serviceName = ApprovalStepEnum.getByNo(nextStep).getServiceName();
//            Optional.ofNullable(SpringUtils.getBean(serviceName)).map(IApprovalStepService.class::cast)
//                    .ifPresent(service -> service.executeNextStep(bidApproval));
//        }
//    }
//
//    @Override
//    public boolean checkCurrentStep(BidApproval bidApproval) {
//        return true;
//    }
//
//    /**
//     * 自动执行当前步骤
//     * <br>1. 将立项步骤置为当前步骤
//     *
//     * @param bidApproval 中标未立项
//     */
//    public void initCurrentStep(BidApproval bidApproval) {
//        final Integer currentStep = currentStep();
//        log.info("initCurrentStep : [{}]", currentStep);
//        bidApproval.setCurrentStepNo(currentStep).setUpdateAt(Instant.now().toEpochMilli())
//                .setUpdateBy(LoginUserUtil.userId());
//    }
//
//    @Override
//    public boolean isAutoSubmit() {
//        final String name = this.getClass().getName();
//        log.info("isAutoSubmit_name : [{}], isAutoSubmit: {}", name, false);
//        return false;
//    }
//
//    @Override
//    public void preEvent(BidApproval bidApproval) {
//        final String name = this.getClass().getName();
//        log.info("执行{}-preEvent", name);
//    }
//
//    /**
//     * 预执行下一步骤
//     *
//     * @param bidApproval 中标未立项
//     */
//    @Override
//    public void executeNextStep(BidApproval bidApproval) {
//        final String name = this.getClass().getName();
//        log.info("executeNextStep : [{}]", name);
//        this.initCurrentStep(bidApproval);
//        // 先执行前置事件
//        this.preEvent(bidApproval);
//        if (this.isAutoSubmit()) {
//            this.submitApprovalStep(bidApproval);
//        }
//        getBidApprovalMapper().updateById(bidApproval);
//    }
//
//    /**
//     * @return {@link BidApprovalMapper}
//     */
//    protected BidApprovalMapper getBidApprovalMapper() {
//        if (this.bidApprovalMapper == null) {
//            this.bidApprovalMapper = SpringUtils.getBean(BidApprovalMapper.class);
//        }
//        return this.bidApprovalMapper;
//    }
//
//    /**
//     * 监听财商立项完成事件，并执行子类的具体逻辑
//     *
//     * @param event
//     */
//    @EventListener(CpmProjectFlowEvent.class)
//    public void financeApproveEvent(CpmProjectFlowEvent event) {
//        final FlowNodeEnum nodeEnum = event.getNodeEnum();
//        final FlowNodeHandlerEnum handlerEnum = event.getHandlerEnum();
//        // 监听财商立项 或 标准立项 完成事件，
//        if ((FlowNodeEnum.FINANCE_SEGMENT.equals(nodeEnum) || FlowNodeEnum.FINANCE_SMART_SITE_APPROVAL.equals
//        (nodeEnum))
//                && FlowNodeHandlerEnum.POST.equals(handlerEnum)) {
//            final Project project = projectMapper.selectById(event.getProjectId());
//            final Long bidApprovalId = project.getIndependentContractId();
//            BidApproval bidApproval = bidApprovalMapper.selectById(bidApprovalId);
//            // todo 执行对应步骤提交事件
//            // 财商事件完成后，然后判断项目部信息是否完成，如果完成执行提交当前步骤动作
//            if (Objects.nonNull(bidApproval) && Objects.equals(bidApproval.getCurrentStepNo(),
//                    currentStep()) && Objects.nonNull(bidApproval.getCpmProjectId())) {
//                this.submitApprovalStep(bidApproval);
//            }
//        }
//    }
//
//    /**
//     * 标准立项完成事件监听器
//     *
//     * @param event 事件
//     */
//    @EventListener(CpmProjectFlowEvent.class)
//    public void approveEvent(CpmProjectFlowEvent event) {
//        final FlowNodeEnum nodeEnum = event.getNodeEnum();
//        final FlowNodeHandlerEnum handlerEnum = event.getHandlerEnum();
//        // 只监听标准立项事件 并只处理中标未立项相关的逻辑
//        if (FlowNodeEnum.FINANCE_SMART_SITE_APPROVAL.equals(nodeEnum) && FlowNodeHandlerEnum.POST.equals
//        (handlerEnum)) {
//            final Project project = projectMapper.selectById(event.getProjectId());
//            final Long bidApprovalId = project.getIndependentContractId();
//            final BidApproval bidApproval = bidApprovalMapper.selectById(bidApprovalId);
//            if (Objects.nonNull(bidApproval)) {
//                this.submitApprovalStep(bidApproval);
//            }
//        }
//    }
//
////    /**
////     * 标准立项完成事件监听器, 及组织同步完成
////     *
////     * @param event 事件
////     */
////    @EventListener(CpmProjectFlowEvent.class)
////    public void approveEvent(CpmProjectFlowEvent event) {
////        final FlowNodeEnum nodeEnum = event.getNodeEnum();
////        final FlowNodeHandlerEnum handlerEnum = event.getHandlerEnum();
////        // 财商立项监听, 财商立项完成后 取财商编码到六统一同步组织信息
////        if (FlowNodeEnum.FINANCE_SEGMENT.equals(nodeEnum) && FlowNodeHandlerEnum.POST.equals(handlerEnum)) {
////            final Project project = projectMapper.selectById(event.getProjectId());
////            final String projectFinanceCode = project.getProjectFinanceCode();
////            // todo 云枢组织同步 如果同步成功则 更新立项进度，并进入下一步，不成功则加入定时任务扫描中
////            // todo 去EHR同步组织数据，
////            if (true) {
////                final Long bidApprovalId = project.getIndependentContractId();
////                final BidApproval bidApproval = bidApprovalMapper.selectById(bidApprovalId);
////                if (Objects.nonNull(bidApproval)) {
////                    this.submitApprovalStep(bidApproval);
////                }
////            }
////        }
////    }
//
//}
