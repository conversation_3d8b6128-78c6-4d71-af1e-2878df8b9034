package com.cscec3b.iti.projectmanagement.server.config;

import lombok.Data;
import org.springframework.boot.context.properties.ConfigurationProperties;
import org.springframework.cloud.context.config.annotation.RefreshScope;
import org.springframework.stereotype.Component;

/**
 * Dop-Auth 配置信息
 *
 * <AUTHOR>
 * @date 2023/10/26 10:25
 **/

@Data
@RefreshScope
@Component
@ConfigurationProperties(prefix = "dop-auth")
public class DopAuthProperties {

    private String authUrl;

    private String clientId;

    private String contextPath;

    private String unAuthRedirectUrl;

}
