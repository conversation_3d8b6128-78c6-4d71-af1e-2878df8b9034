package com.cscec3b.iti.projectmanagement.server.mapper;

import com.cscec3b.iti.projectmanagement.api.dto.dto.EventBusinessColumn;
import org.apache.ibatis.annotations.Mapper;
import org.apache.ibatis.annotations.Param;

import java.util.List;
import java.util.Map;
import java.util.Set;

@Mapper
public interface EventBusinessColumnMapper {
    
    /**
     * 获取数据表字段信息
     *
     * @param tables 表信息
     * @return {@link List }<{@link EventBusinessColumn }>
     * <AUTHOR>
     * @date 2023/08/21
     */
    List<EventBusinessColumn> databaseColumns(@Param("tables") Set<String> tables);
    
    /**
     * 获取项目信息
     *
     * @param columns   列
     * @param projectId 项目id
     * @return {@link List }<{@link Map }<{@link String }, {@link Object }>>
     * <AUTHOR>
     * @date 2023/08/21
     */
    List<Map<String, Object>> getProjectInfo(@Param("columns") List<String> columns, @Param("id") int projectId);
    
    /**
     * 投标总结信息
     *
     * @param columns   列
     * @param projectId 项目id
     * @return {@link List }<{@link Map }<{@link String }, {@link Object }>>
     * <AUTHOR>
     * @date 2023/08/21
     */
    List<Map<String, Object>> getBidSummaryInfo(@Param("columns") List<String> columns, @Param("id") int projectId);
    
    /**
     * 得到局合同信息
     *
     * @param columns   列
     * @param projectId 项目id
     * @return {@link List }<{@link Map }<{@link String }, {@link Object }>>
     * <AUTHOR>
     * @date 2023/08/21
     */
    List<Map<String, Object>> getBureauContractInfo(@Param("columns") List<String> columns, @Param("id") int projectId);
    
    /**
     * 局补充协议信息
     *
     * @param columns   列
     * @param projectId 项目id
     * @return {@link List }<{@link Map }<{@link String }, {@link Object }>>
     * <AUTHOR>
     * @date 2023/08/21
     */
    List<Map<String, Object>> getBureauSupplementaryAgreementInfo(@Param("columns") List<String> columns,
        @Param("id") int projectId);
    
    /**
     * 获取合同信息
     *
     * @param columns   列
     * @param projectId 项目id
     * @return {@link List }<{@link Map }<{@link String }, {@link Object }>>
     * <AUTHOR>
     * @date 2023/08/21
     */
    List<Map<String, Object>> getContractInfo(@Param("columns") List<String> columns, @Param("id") int projectId);
    
    /**
     * 得到补充协议信息
     *
     * @param columns   列
     * @param projectId 项目id
     * @return {@link List }<{@link Map }<{@link String }, {@link Object }>>
     * <AUTHOR>
     * @date 2023/08/21
     */
    List<Map<String, Object>> getSupplementaryAgreementInfo(@Param("columns") List<String> columns,
        @Param("id") int projectId);
    
    /**
     * 开标记录信息
     *
     * @param columns   列
     * @param projectId 项目id
     * @return {@link List }<{@link Map }<{@link String }, {@link Object }>>
     * <AUTHOR>
     * @date 2023/08/21
     */
    List<Map<String, Object>> getBidOpeningRecordsInfo(@Param("columns") List<String> columns,
        @Param("id") int projectId);

}
