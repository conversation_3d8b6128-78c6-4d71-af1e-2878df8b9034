package com.cscec3b.iti.projectmanagement.server.config;

import com.baomidou.mybatisplus.core.handlers.MetaObjectHandler;
import com.cscec3b.iti.projectmanagement.server.util.LoginUserUtil;
import org.apache.ibatis.reflection.MetaObject;
import org.springframework.stereotype.Component;

import java.time.Instant;
import java.util.Optional;

/**
 * <AUTHOR>
 * @Description: 自动填充
 * @date 2023/11/28
 */
@Component
public class MyMetaObjectHandler implements MetaObjectHandler {


    @Override
    public void insertFill(MetaObject metaObject) {
        final long currentTime = Instant.now().toEpochMilli();
        this.strictInsertFill(metaObject, "createAt", () -> currentTime, Long.class);
        this.strictInsertFill(metaObject, "updateAt", () -> currentTime, Long.class);
        final String currUserId = Optional.ofNullable(LoginUserUtil.userId()).orElse("system");
        this.strictInsertFill(metaObject, "createBy", () -> currUserId, String.class);
        this.strictInsertFill(metaObject, "updateBy", () -> currUserId, String.class);
    }

    @Override
    public void updateFill(MetaObject metaObject) {
        final long currentTime = Instant.now().toEpochMilli();
        this.strictUpdateFill(metaObject, "updateAt", () -> currentTime, Long.class);
        final String currUserId = Optional.ofNullable(LoginUserUtil.userId()).orElse("system");
        this.strictUpdateFill(metaObject, "updateBy", () -> currUserId, String.class);
    }
}
