package com.cscec3b.iti.projectmanagement.server.config;

import java.util.Arrays;

/**
 * 支持的公共数据标准类型枚举及本地文件存储路径
 *
 * <AUTHOR>
 * @date 2023/02/14 10:52
 *
 *       新增一种公共数据标准类型支持样例（如币种 /common/currency），
 *       步骤一：把common-data-json文件夹下的对应的json文件（common-currency.json）复制到projectmanagement-server\src\main\resources\common_data文件夹下；
 *       步骤二：把 《 //COMMON_CURRENCY("/common/currency", "common-currency.json"), 》 这一行的注释去掉
 **/

public enum CommonDataTypeEnum {
    
    /**
     *
     */
    // COMMON_CURRENCY("/common/currency", "common-currency.json"),
    // COMMON_BUSINESS_UNIT("/common/business_unit", "common-business_unit.json"),
    // COMMON_NATIONAL_ECONOMIC_INDUSTRY("/common/national_economic_industry",
    // "common-national_economic_industry.json"),
    // COMMON_DOMESTIC_AND_FOREIGN("/common/domestic_and_foreign", "common-domestic_and_foreign.json"),
    // COMMON_STATES("/common/states", "common-states.json"),
    // COMMON_CERTIFICATE_TYPE("/common/certificate_type", "common-certificate_type.json"),
    // COMMON_MERCHANT_TYPE("/common/merchant_type", "common-merchant_type.json"),
    // COMMON_GROUP_UNIT("/common/group_unit", "common-group_unit.json"),
    // COMMON_MERCHANT_NATURE("/common/merchant_nature", "common-merchant_nature.json"),
    // COMMON_INDUSTRY("/common/industry", "common-industry.json"),
    // COMMON_PROPERTY_TYPE("/common/property_type", "common-property_type.json"),
    // COMMON_COUNTRY_DIRECTORY("/common/country_directory", "common-country_directory.json"),
    /**
     *
     */
    COMMON_ADMINISTRATIVE_REGION("/common/administrative_region", "common-administrative_region.json"),
    // FINANCE_INTEREST_RATE_TYPE("/finance/interest_rate_type", "finance-interest_rate_type.json"),
    // FINANCE_ACCOUNTING_STANDARD("/finance/accounting_standard", "finance-accounting_standard.json"),
    // FINANCE_PAYMENT_METHOD("/finance/payment_method", "finance-payment_method.json"),
    // FINANCE_BILLING_METHOD("/finance/billing_method", "finance-billing_method.json"),
    // FINANCE_DEPOSIT_TYPE("/finance/deposit_type", "finance-deposit_type.json"),
    // REGULATED_CATEGORY("/regulated_category", "regulated_category.json"),
    // FINANCE_GUARANTY_STYLE("/finance/guaranty_style", "finance-guaranty_style.json"),
    // FINANCE_GUARANTEE_ENTERPRISE_NATURE("/finance/guarantee_enterprise_nature",
    // "finance-guarantee_enterprise_nature.json"),
    // FINANCE_CREDIT_RATING("/finance/credit_rating", "finance-credit_rating.json"),
    // FINANCE_FINANCING_PERIOD("/finance/financing_period", "finance-financing_period.json"),
    // FINANCE_TYPES_OF_GUARANTEE("/finance/types_of_guarantee", "finance-types_of_guarantee.json"),
    // FINANCE_GUARANTEE_CATEGORY("/finance/guarantee_category", "finance-guarantee_category.json"),
    // FINANCE_CAPITAL_SOURCE("/finance/capital_source", "finance-capital_source.json"),
    // FINANCE_PAPER_TYPE("/finance/paper_type", "finance-paper_type.json"),
    // FINANCE_TYPE_OF_COUNTERSECURED_ASSET("/finance/type_of_countersecured_asset",
    // "finance-type_of_countersecured_asset.json"),
    // FINANCE_GUARANTEE_TYPE("/finance/guarantee_type", "finance-guarantee_type.json"),
    // FINANCE_MAINTAIN_TYPE("/finance/maintain_type", "finance-maintain_type.json"),
    // TAX_VALUE_ADDED_TAX_RATE("/tax/value_added_tax_rate", "tax-value_added_tax_rate.json"),
    // TAX_TAX_CALCULATE_MEANS("/tax/tax_calculate_means", "tax-tax_calculate_means.json"),
    // TAX_TAX_OBJECT("/tax/tax_object", "tax-tax_object.json"), TAX_TAXPAYER_TYPE("/tax/taxpayer_type",
    // "tax-taxpayer_type.json"),
    // TAX_LOCAL_TAXES_FEES("/tax/local_taxes_fees", "tax-local_taxes_fees.json"),
    // TAX_INVOICE_TYPE("/tax/invoice_type", "tax-invoice_type.json"),
    // INVESTMENT_INVESTMEN_OPERATION_MODE("/investment/investmen_operation_mode",
    // "investment-investmen_operation_mode.json"),
    // INVESTMENT_INVESTMENT_PROJECT_STATUS("/investment/investment_project_status",
    // "investment-investment_project_status.json"),
    // PROJECT_TYPE_OF_ENGINEERING_BUSINESS("/project/type_of_engineering_business",
    // "project-type_of_engineering_business.json"),
    /**
     * 项目分类
     */
    PROJECT_PROJECT_CLASSIFICATION("/project/project_classification", "project-project_classification.json");
    // PROJECT_PROJECT_UNDERTAKE_SUBJECT_TYPE("/project/project_undertake_subject_type",
    // "project-project_undertake_subject_type.json"),
    // PROJECT_PROJECT_SITE_TYPE("/project/project_site_type", "project-project_site_type.json"),
    // PROJECT_CONTRACTING_SYSTEM("/project/contracting_system", "project-contracting_system.json"),
    // PROJECT_CONSTRUCTION_PROJECT_STATUS_ENG("/project/construction_project_status_eng",
    // "project-construction_project_status_eng.json"),
    // PROJECT_CONSTRUCTION_PROJECT_STATUS_FIN("/project/construction_project_status_fin",
    // "project-construction_project_status_fin.json"),
    // PROJECT_CONSTRUCTION_PROJECT_STATUS_BIZ("/project/construction_project_status_biz",
    // "project-construction_project_status_biz.json"),
    // COMMON_NATURE_OF_BUSINESS("/common/nature_of_business", "common-nature_of_business.json"),
    // COMMON_COMPANY_TYPE_BY_SECTOR("/common/company_type_by_sector", "common-company_type_by_sector.json"),
    // COMMON_COMPANY_TYPE_BY_BIZ("/common/company_type_by_biz", "common-company_type_by_biz.json"),
    // COMMON_COMPANY_TYPE_BY_KPI("/common/company_type_by_kpi", "common-company_type_by_kpi.json"),
    // COMMON_EPC_MANAGEMENT_MODE("/common/epc_management_mode", "common-epc_management_mode.json"),
    // COMMON_DATA_STATUS("/common/data_status", "common-data_status.json"),
    // COMMON_JUDGMENT_SIGN("/common/judgment_sign", "common-judgment_sign.json");
    
    /**
     * url
     */
    private final String url;

    private final String fileName;

    CommonDataTypeEnum(String url, String fileName) {
        this.url = url;
        this.fileName = fileName;
    }
    
    /**
     * 校验
     *
     * @param url url
     * @return boolean
     */
    public static boolean valid(String url) {
        return Arrays.stream(CommonDataTypeEnum.values()).anyMatch(typeEnum -> typeEnum.url.equals(url));
    }

    public String getUrl() {
        return url;
    }

    public String getFileName() {
        return fileName;
    }
}
