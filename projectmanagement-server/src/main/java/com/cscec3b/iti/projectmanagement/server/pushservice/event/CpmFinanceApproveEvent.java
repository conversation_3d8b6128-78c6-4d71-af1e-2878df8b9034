package com.cscec3b.iti.projectmanagement.server.pushservice.event;

import lombok.Getter;
import lombok.Setter;
import org.springframework.context.ApplicationEvent;

/**
 * 财商一体化立项事件
 *
 * <AUTHOR>
 * @date 2024/01/17
 */
@Getter
@Setter
public class CpmFinanceApproveEvent extends ApplicationEvent {

    /**
     * 项目中心项目id
     */
    private Long cpmProjectId;

    /**
     * 云枢组织id
     */
    private String yunshuOrgId;

    /**
     * 中标未立项独立立项文件id
     */
    private Long BelongId;

    /**
     * 财商一体化立项状态
     */
    private Integer financeApproveStatus;

    public CpmFinanceApproveEvent(Object source, Long cpmProjectId, String yunshuOrgId, Integer financeApproveStatus) {
        super(source);
        this.cpmProjectId = cpmProjectId;
        this.yunshuOrgId = yunshuOrgId;
        this.financeApproveStatus = financeApproveStatus;
    }
}
