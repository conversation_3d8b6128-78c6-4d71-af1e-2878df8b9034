package com.cscec3b.iti.projectmanagement.server.config;

import java.util.Arrays;

import lombok.Getter;

/**
 * 支持的项目数据标准类型枚举及本地文件存储路径
 *
 * <AUTHOR>
 * @date 2023/02/14 10:52
 **/
@Getter
public enum ProjectStatusDataEnum {

    PROJECT_CONSTRUCTION_PROJECT_STATUS_ENG("/project/construction_project_status_eng","project-construction_project_status_eng.json"),
    PROJECT_CONSTRUCTION_PROJECT_STATUS_FIN("/project/construction_project_status_fin",
            "project-construction_project_status_fin.json"),
    PROJECT_CONSTRUCTION_PROJECT_STATUS_BIZ("/project/construction_project_status_biz",
            "project-construction_project_status_biz.json");

    private final String url;

    private final String fileName;


    ProjectStatusDataEnum(String url, String fileName) {
        this.url = url;
        this.fileName = fileName;
    }

    public static boolean valid(String url) {
        return Arrays.stream(ProjectStatusDataEnum.values()).anyMatch(typeEnum -> typeEnum.url.equals(url));
    }
    public String getUrl() {
        return url;
    }
}
