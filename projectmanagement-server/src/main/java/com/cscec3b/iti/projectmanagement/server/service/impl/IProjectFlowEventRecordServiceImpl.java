package com.cscec3b.iti.projectmanagement.server.service.impl;

import javax.annotation.Resource;

import org.springframework.stereotype.Service;

import com.cscec3b.iti.projectmanagement.server.entity.ProjectFlowEventRecord;
import com.cscec3b.iti.projectmanagement.server.mapper.ProjectFlowEventRecordMapper;
import com.cscec3b.iti.projectmanagement.server.service.IProjectFlowEventRecordService;
@Service
public class IProjectFlowEventRecordServiceImpl implements IProjectFlowEventRecordService {

    @Resource
    private ProjectFlowEventRecordMapper projectFlowEventRecordMapper;

    @Override
    public int insert(ProjectFlowEventRecord record) {
        return projectFlowEventRecordMapper.insert(record);
    }

    @Override
    public ProjectFlowEventRecord selectByPrimaryKey(String id) {
        return projectFlowEventRecordMapper.selectByPrimaryKey(id);
    }

}
