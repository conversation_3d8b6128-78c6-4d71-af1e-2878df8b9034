package com.cscec3b.iti.projectmanagement.server.service.impl;

import com.cscec3b.iti.common.web.exception.BusinessException;
import com.cscec3b.iti.projectmanagement.server.entity.ProjectEventSubscribe;
import com.cscec3b.iti.projectmanagement.server.entity.YunshuOrgSync;
import com.cscec3b.iti.projectmanagement.server.mapper.ProjectEventSubscribeMapper;
import com.cscec3b.iti.projectmanagement.server.mapper.YunshuOrgSyncMapper;
import com.cscec3b.iti.projectmanagement.server.service.IOpenApiDataScopeService;
import lombok.RequiredArgsConstructor;
import org.springframework.stereotype.Service;

import java.util.Optional;

/**
 * 对外接口数据范围服务类
 *
 * <AUTHOR>
 * @date 2025/06/09
 */
@Service
@RequiredArgsConstructor
public class OpenApiDataScopeServiceImpl implements IOpenApiDataScopeService {

    private final ProjectEventSubscribeMapper projectEventSubscribeMapper;
    private final YunshuOrgSyncMapper yunshuOrgSyncMapper;

    @Override
    public String getAppDataIdPathByAppKey(String appKey, String queryOrgId) {
        // 业务系统 获取数据范围
        ProjectEventSubscribe projectEventSubscribe = projectEventSubscribeMapper.selectByOpenApiKey(appKey);
        // 业务系统的 最高级别的数据权限
        String appDataPath = Optional.ofNullable(projectEventSubscribe).map(ProjectEventSubscribe::getYunshuExecuteUnitIdPath)
                .orElseThrow(() -> new BusinessException(80107005));
        return Optional.ofNullable(queryOrgId)
                .map(executeUnitId -> {
                    YunshuOrgSync sync = yunshuOrgSyncMapper.getByDeptId(executeUnitId);
                    if (sync != null) {
                        return sync.getIdPath();
                    } else {
                        throw new BusinessException(80107000);
                    }
                })
                .orElse(appDataPath);
    }
}
