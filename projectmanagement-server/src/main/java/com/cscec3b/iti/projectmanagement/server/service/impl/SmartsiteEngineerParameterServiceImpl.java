package com.cscec3b.iti.projectmanagement.server.service.impl;

import static com.cscec3b.iti.projectmanagement.server.constant.Constants.SMARTSITE_ENGINEER_PARAMETER_KEY;

import java.util.List;

import javax.validation.constraints.NotNull;

import org.apache.commons.lang3.ObjectUtils;
import org.springframework.boot.CommandLineRunner;
import org.springframework.data.redis.core.BoundHashOperations;
import org.springframework.data.redis.core.RedisTemplate;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;

import com.cscec3b.iti.projectmanagement.server.entity.SmartsiteEngineerParameter;
import com.cscec3b.iti.projectmanagement.server.mapper.SmartsiteEngineerParameterMapper;
import com.cscec3b.iti.projectmanagement.server.service.ISmartsiteEngineerParameterService;

import lombok.extern.slf4j.Slf4j;

/**
 * 智慧工地工程参数
 *
 * <AUTHOR>
 * @date 2023/08/11 19:11
 **/
@Slf4j
@Service
@Transactional(rollbackFor = Exception.class)
public class SmartsiteEngineerParameterServiceImpl implements ISmartsiteEngineerParameterService, CommandLineRunner {

	private final SmartsiteEngineerParameterMapper engineerParameterMapper;

	private final BoundHashOperations<String, String, SmartsiteEngineerParameter> boundHashOperations;

	public SmartsiteEngineerParameterServiceImpl(SmartsiteEngineerParameterMapper engineerParameterMapper,
			RedisTemplate<String, SmartsiteEngineerParameter> redisTemplate) {
		this.engineerParameterMapper = engineerParameterMapper;
		this.boundHashOperations = redisTemplate.boundHashOps(SMARTSITE_ENGINEER_PARAMETER_KEY);
	}


	@Override
	public SmartsiteEngineerParameter getByIdAndDataKey(String id, String dataKey) {
		// 根据id和dataKey查询缓存
		final String cacheKey = id + ":" + dataKey;
		// 从缓存中获取缓存
		SmartsiteEngineerParameter smartsiteEngineerParameter = boundHashOperations.get(cacheKey);
		// 如果缓存中没有，则查询数据库
		if (ObjectUtils.isEmpty(smartsiteEngineerParameter)) {
			smartsiteEngineerParameter = engineerParameterMapper.getByIdAndDataKey(id, dataKey);
			// 如果查询结果不为空，则将查询结果放入缓存
			if (ObjectUtils.isNotEmpty(smartsiteEngineerParameter)) {
				boundHashOperations.put(cacheKey, smartsiteEngineerParameter);
				return smartsiteEngineerParameter;
			}
		}
		return smartsiteEngineerParameter;
	}

	@Override
	public Boolean updateEngineerParameter(SmartsiteEngineerParameter parameter) {
		return Boolean.FALSE;
	}

	@Override
	public Boolean delEngineerParameter(String id, String dataKey) {
		final String cacheKey = id + ":" + dataKey;
		boundHashOperations.delete(cacheKey);
		return engineerParameterMapper.delEngineerParameter(id, dataKey) >= 1;
	}

	@Override
	public Boolean addEngineerParameter(SmartsiteEngineerParameter parameter) {
		return engineerParameterMapper.addEngineerParameter(parameter) == 1;
	}

	@Override
	public void run(String... args) throws Exception {
		log.info("初始化智慧工地工程参数");
		final List<SmartsiteEngineerParameter> parameterList = engineerParameterMapper.getAll();
		parameterList.forEach(engineerParameter -> {
			final String typeId = engineerParameter.getId();
			final String dataKey = engineerParameter.getDataKey();
			final String cacheKey = typeId + ":" + dataKey;
			boundHashOperations.put(cacheKey, engineerParameter);
		});
	}

	@Override
	public void delParameterCache(@NotNull String typeId, @NotNull String dataKey) {
		final String cacheKey = typeId + ":" + dataKey;
		boundHashOperations.delete(cacheKey);
	}
}
