//package com.cscec3b.iti.projectmanagement.server.service.impl;
//
//import com.baomidou.mybatisplus.core.toolkit.Wrappers;
//import com.cscec3b.iti.common.base.json.JsonUtils;
//import com.cscec3b.iti.common.web.exception.BusinessException;
//import com.cscec3b.iti.common.web.exception.FrameworkException;
//import com.cscec3b.iti.projectmanagement.api.dto.request.approvalstep.ApprovalStepReq;
//import com.cscec3b.iti.projectmanagement.server.bidapprovalservice.BidApprovalHandlerFactory;
//import com.cscec3b.iti.projectmanagement.server.bidapprovalservice.BidFilePreHandler;
//import com.cscec3b.iti.projectmanagement.server.constant.Constants;
//import com.cscec3b.iti.projectmanagement.server.entity.BidApproval;
//import com.cscec3b.iti.projectmanagement.server.entity.Project;
//import com.cscec3b.iti.projectmanagement.server.enums.ApprovalStepEnum;
//import com.cscec3b.iti.projectmanagement.server.enums.IndContractsTypeEnum;
//import com.cscec3b.iti.projectmanagement.server.enums.IndependTypeEnum;
//import com.cscec3b.iti.projectmanagement.server.mapper.BidApprovalMapper;
//import com.cscec3b.iti.projectmanagement.server.mapper.ProjectMapper;
//import com.cscec3b.iti.projectmanagement.server.service.AbstractApprovalStepService;
//import com.cscec3b.iti.projectmanagement.server.service.ProjectService;
//import com.cscec3b.iti.projectmanagement.server.service.WfApprovalService;
//import com.fasterxml.jackson.core.type.TypeReference;
//import lombok.AllArgsConstructor;
//import lombok.extern.slf4j.Slf4j;
//import org.apache.commons.lang3.ObjectUtils;
//import org.springframework.stereotype.Service;
//import org.springframework.transaction.annotation.Transactional;
//
//import java.util.List;
//import java.util.Objects;
//import java.util.Optional;
//
/// **
// * 立项步骤-立项复核
// *
// * <AUTHOR>
// * @date 2024/01/03
// */
//@Slf4j
//@Service("reCheckService")
//@AllArgsConstructor
//@Transactional(rollbackFor = Exception.class)
//public class ApprovalStepOfReCheckServiceImpl extends AbstractApprovalStepService {
//
//    private final BidApprovalMapper bidApprovalMapper;
//
//    private final WfApprovalService wfApprovalService;
//
//    private final ProjectService projectService;
//
//    private final ProjectMapper projectMapper;
//
//    /**
//     * 合同文件处理工厂
//     */
//    private final BidApprovalHandlerFactory bidApprovalHandlerFactory;
//
//    @Override
//    public Integer currentStep() {
//        return ApprovalStepEnum.RE_CHECK.getNo();
//    }
//
//
//    /**
//     * 立项复核
//     *
//     * @param stepReq 请求参数
//     * @return boolean
//     */
//    @Override
//    public boolean saveCurrentStep(ApprovalStepReq stepReq) {
//
//        final BidApproval bidApproval = bidApprovalMapper.selectById(stepReq.getBidApprovalId());
//        final Integer currentStepNo = bidApproval.getCurrentStepNo();
//        if (!currentStepNo.equals(currentStep())) {
//            throw new FrameworkException(-1, "步骤信息异常");
//        }
//        Optional.of(bidApproval).map(BidApproval::getIndependentProject)
//                .filter(ind -> !Constants.NUMBER_ZERO.toString().equals(ind))
//                .orElseThrow(() -> new BusinessException(80108007));
//        // 获取下一步骤
//        // 如果独立性判断为非独立项目，移除当前步骤之后的步骤信息
//        final String stepList = bidApproval.getStepList();
//        if (Objects.equals(IndependTypeEnum.NON_INDEPENDENT.getCode(), bidApproval.getIndependentProject())) {
//            List<Integer> stepNos = JsonUtils.readValue(stepList, new TypeReference<List<Integer>>() {
//            });
//            if (stepNos != null) {
//                stepNos = stepNos.subList(0, stepNos.indexOf(currentStepNo) + 1);
//            }
//            bidApproval.setStepList(JsonUtils.toJsonStr(stepNos));
//        }
//        this.submitApprovalStep(bidApproval);
//        return true;
//    }
//
//    /**
//     * 项目创建
//     *
//     * @param bidApproval 中标未立项信息
//     * @return {@link Boolean}
//     */
//    // @Override
//    public Long createOrHookProject(BidApproval bidApproval) {
//        final String independentProject = bidApproval.getIndependentProject();
//        final IndependTypeEnum independTypeEnum = IndependTypeEnum.getEnumByCode(independentProject);
//        if (IndependTypeEnum.INDEPENDENT.equals(independTypeEnum)) {
//            // 独立立项
//            return createProject(bidApproval);
//        } else if (IndependTypeEnum.NON_INDEPENDENT.equals(independTypeEnum)) {
//
//            // 关联项目
//            return hookProject(bidApproval);
//        }
//        return null;
//    }
//
//    /**
//     * 关联项目:<br>
//     * 1. 更新文件挂接信息<br>
//     * 2. 更新项目金额<br>
//     *
//     * @param bidApproval 中标未立项
//     * @return {@link Long}
//     */
//    private Long hookProject(BidApproval bidApproval) {
//        final Project project = projectService.selectById(bidApproval.getCpmProjectId());
//        final Long independentContractId = project.getIndependentContractId();
//        final Integer independentContractType = project.getIndependentContractType();
//        final String type = bidApproval.getType();
//        final IndContractsTypeEnum indContractsTypeEnum = IndContractsTypeEnum.getEnumCode(type);
//        final Long belongId = bidApproval.getBelongId();
//        // 更新文件挂接信息
//        final BidFilePreHandler fileHandler = bidApprovalHandlerFactory.getFileHandler(indContractsTypeEnum);
//        fileHandler.hookProject(independentContractId, independentContractType, belongId,
//                IndependTypeEnum.NON_INDEPENDENT.getCode());
//        // 更新项目金额
//        // projectService.recalculationProjectAmounts(project);
//        projectService.recalculationProjectAmountsV3(project);
//        projectService.updateProject(project);
//        return project.getId();
//    }
//
//    /**
//     * 项目初始化
//     *
//     * @param bidApproval 中标未立项
//     * @return {@link Long}
//     */
//    private Long createProject(BidApproval bidApproval) {
//        // 创建项目：
//        final String type = bidApproval.getType();
//        final IndContractsTypeEnum indContractsTypeEnum = IndContractsTypeEnum.getEnumCode(type);
//        if (ObjectUtils.isEmpty(indContractsTypeEnum)) {
//            throw new BusinessException(8010320);
//        }
//        // 检查是否重复立项
//        final int count =
//                projectMapper.selectCount(Wrappers.<Project>lambdaQuery().eq(Project::getIndependentContractId,
//                                bidApproval.getId())
//                        .eq(Project::getIndependentContractType, indContractsTypeEnum.getDictCode()));
//        if (count > 0) {
//            throw new BusinessException(8010006);
//        }
//        final BidFilePreHandler<?> fileHandler = bidApprovalHandlerFactory.getFileHandler(indContractsTypeEnum);
//        return fileHandler.createProject(bidApproval);
//    }
//
//}
