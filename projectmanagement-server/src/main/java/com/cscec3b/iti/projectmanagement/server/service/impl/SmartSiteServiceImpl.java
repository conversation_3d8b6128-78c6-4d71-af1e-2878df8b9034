package com.cscec3b.iti.projectmanagement.server.service.impl;

import cn.hutool.core.bean.BeanUtil;
import cn.hutool.core.collection.CollUtil;
import cn.hutool.core.util.StrUtil;
import cn.hutool.http.HttpUtil;
import cn.hutool.json.JSONArray;
import cn.hutool.json.JSONObject;
import cn.hutool.json.JSONUtil;
import com.cscec3b.iti.common.base.json.JsonUtils;
import com.cscec3b.iti.common.web.exception.BusinessException;
import com.cscec3b.iti.common.web.exception.FrameworkException;
import com.cscec3b.iti.projectmanagement.api.IYunShuSmartConstructionOrgApi;
import com.cscec3b.iti.projectmanagement.api.dto.dto.smartsite.*;
import com.cscec3b.iti.projectmanagement.api.dto.request.project.SmartSiteReq;
import com.cscec3b.iti.projectmanagement.api.dto.response.YunShuSmartConstructionOrgResp;
import com.cscec3b.iti.projectmanagement.api.dto.response.project.SmartSiteData;
import com.cscec3b.iti.projectmanagement.api.dto.response.project.SmartSiteResp;
import com.cscec3b.iti.projectmanagement.server.config.SmartSiteProperties;
import com.cscec3b.iti.projectmanagement.server.constant.Constants;
import com.cscec3b.iti.projectmanagement.server.constant.ProjectConstant;
import com.cscec3b.iti.projectmanagement.server.entity.*;
import com.cscec3b.iti.projectmanagement.server.entity.dto.ComparableResult;
import com.cscec3b.iti.projectmanagement.server.enums.*;
import com.cscec3b.iti.projectmanagement.server.mapper.ProjectMapper;
import com.cscec3b.iti.projectmanagement.server.pushservice.enums.ProjectEventEnum;
import com.cscec3b.iti.projectmanagement.server.service.*;
import com.cscec3b.iti.projectmanagement.server.util.ComparableUtils;
import com.cscec3b.iti.projectmanagement.server.util.DateTimeUtil;
import com.cscec3b.iti.projectmanagement.server.util.OpenApiInvoker;
import com.fasterxml.jackson.core.JsonProcessingException;
import com.fasterxml.jackson.databind.ObjectMapper;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.collections4.CollectionUtils;
import org.apache.commons.lang3.ObjectUtils;
import org.apache.commons.lang3.StringUtils;
import org.springframework.retry.annotation.Retryable;
import org.springframework.scheduling.annotation.Async;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;

import javax.annotation.Resource;
import java.time.Instant;
import java.time.LocalDateTime;
import java.util.*;
import java.util.stream.Collectors;

@Slf4j
@Service
@Transactional(rollbackFor = Exception.class)
public class SmartSiteServiceImpl implements ISmartSiteService {

//    @Resource
//    private IAppStdOrgService stdOrgService;

    @Resource
    private ProjectMapper projectMapper;

    @Resource
    private SmartSiteProperties smartSiteProperties;

    @Resource
    private ProjectProgressService projectProgressService;

    @Resource
    private SmartsiteSyncProjectInfoService smartsiteSyncProjectInfoService;

    @Resource
    private ISmartsiteEngineerParameterService engineerParameterService;

    @Resource
    private IYunShuSmartConstructionOrgApi yunshuSmartOrgService;

    @Resource
    private YunShuSmartConstructionOrgService yunShuSmartConstructionOrgService;

    @Resource
    private BusinessSystemDataSmartsiteService businessSystemDataSmartsiteService;

    @Resource
    private BusinessSystemDataChangeApprovalConfigService dataChangeApprovalConfigService;

    @Override
    @Retryable
    public void dockingSmartSite(Project project) {
//        //特殊立项类型项目，a8编码为null或空字符串，此时智慧工地无需立项,项目部信息由项目中信息来创建
//        if (project.getSourceSystem().equals(Constants.NUMBER_TWO)&& StringUtils.isEmpty(project.getA8ProjectCode())) {
//            projectWithoutSmartSiteHandler(project);
//        }else{
//            //a8编码不为空时，需要与智慧工地对接，此时智慧工地状态为立项中，项目部信息由根据a8编码在智慧工地中获取的信息来创建
//            projectWithSmartSiteHandler(project);
//        }
    }

    /**
     *
     * @date 2023/04/02 18:48
     * @param a8ProjectCodes
     * @return  SmartSiteResp
     * <AUTHOR>
     */
    @Override
    public List<SmartSiteData> batchUpdateProjectStatus(List<String> a8ProjectCodes) {
        SmartSiteReq smartSiteReq = new SmartSiteReq();
        smartSiteReq.setProjectNos(CollUtil.newArrayList(a8ProjectCodes));
        smartSiteReq.setSchType(Constants.NUMBER_ONE);
        Map<String, Object> requestEntity = OpenApiInvoker.encryptData(smartSiteProperties, smartSiteReq);
        log.info("dockingSmartSite==>>requestEntity:{}", requestEntity);
        String responseStr = HttpUtil.post(smartSiteProperties.getProjectUrl(), requestEntity);
        log.info("dockingSmartSite==>>responseStr:{}", responseStr);
        if (Objects.isNull(responseStr)) {
            throw new RuntimeException();
        }
        ObjectMapper objectMapper = new ObjectMapper();
        SmartSiteResp smartSiteResp;
        try {
            smartSiteResp = objectMapper.readValue(responseStr, SmartSiteResp.class);
        } catch (JsonProcessingException e) {
            throw new RuntimeException(e);
        }
        if (!smartSiteResp.getSuccess() || CollUtil.isEmpty(smartSiteResp.getData())) {
            return new ArrayList<>();
        }
        //去除智慧工地返回数据List中，重复一致的项目信息
        List<SmartSiteData> disList = smartSiteResp.getData().stream().distinct().collect(Collectors.toList());
        return disList;
    }

    @Deprecated
    private void projectWithoutSmartSiteHandler(Project project) {
        //立项进度表中记录无需智慧工地立项
        ProjectProgress progress = projectProgressService.selectProjectProgress(project.getId());
        progress.setSmartApproveStatus(Constants.NUMBER_THREE);
        projectProgressService.updateProjectProgress(progress);
        //根据项目信息创建项目部
//        this.createProjectDeptByProject(project);
    }

    @Deprecated
    private void projectWithSmartSiteHandler(Project project) {
        SmartSiteReq smartSiteReq = new SmartSiteReq();
        smartSiteReq.setProjectNos(CollUtil.newArrayList(project.getA8ProjectCode()));
        smartSiteReq.setSchType(Constants.NUMBER_ONE);
        Map<String, Object> requestEntity = OpenApiInvoker.encryptData(smartSiteProperties, smartSiteReq);
        log.info("dockingSmartSite==>>requestEntity:{}", requestEntity);
        String responseStr = HttpUtil.post(smartSiteProperties.getProjectUrl(), requestEntity);
        log.info("dockingSmartSite==>>responseStr:{}", responseStr);

        //--------定时任务查询智慧工地获取项目部信息时，更新项目进度表信息--------
        this.smartSiteInfoToProjectProgress(project.getId(), responseStr);
        //------------------------------------------------------------------

        if (Objects.isNull(responseStr)) {
            throw new RuntimeException();
        }
        ObjectMapper objectMapper = new ObjectMapper();
        SmartSiteResp smartSiteResp;
        try {
            smartSiteResp = objectMapper.readValue(responseStr, SmartSiteResp.class);
        } catch (JsonProcessingException e) {
            throw new RuntimeException(e);
        }
        if (!smartSiteResp.getSuccess() || CollUtil.isEmpty(smartSiteResp.getData())) {
            return;
        }
        // 数据推送UC创建项目部
//        this.createProjectDept(project, smartSiteResp.getData().get(0));
    }

    private void smartSiteInfoToProjectProgress(Long projectId, String responseStr) {
        //--------定时任务查询智慧工地获取项目部信息时，更新项目进度表信息--------
        ProjectProgress progress = projectProgressService.selectProjectProgress(projectId);
        if (progress == null) {
            throw new BusinessException(8010205);
        }
        // 只记录第一次查询的时间（后续查询不覆盖）
        if (Constants.NUMBER_ZERO.equals(progress.getSmartQueryCount())) {
            progress.setSmartQueryTime(Instant.now().toEpochMilli());
        }
        progress.setSmartQueryCount(progress.getSmartQueryCount() + Constants.NUMBER_ONE);
        progress.setSmartApproveStatus(Constants.NUMBER_ONE);
        if (StringUtils.isEmpty(responseStr)) {
            progress.setSmartRemarks("调用智慧工地接口失败");
            // 超过三次，备注更新为：查询多次未查询到相应项目信息
            if (progress.getSmartQueryCount() > Constants.NUMBER_THREE) {
                progress.setSmartRemarks("查询多次未查询到相应项目信息");
            }
        } else {
            ObjectMapper objectMapper = new ObjectMapper();
            SmartSiteResp smartSiteResp;
            try {
                smartSiteResp = objectMapper.readValue(responseStr, SmartSiteResp.class);
            } catch (JsonProcessingException e) {
                throw new RuntimeException(e);
            }
            List<SmartSiteData> siteData = smartSiteResp.getData();
            if (Boolean.FALSE.equals(smartSiteResp.getSuccess())) {
                progress.setSmartRemarks("调用智慧工地接口失败");
            } else {
                if (CollectionUtils.isEmpty(siteData)) {
                    // 超过三次，备注更新为：查询多次未查询到相应项目信息
                    if (progress.getSmartQueryCount() > Constants.NUMBER_THREE) {
                        progress.setSmartRemarks("查询多次未查询到相应项目信息");
                    }
                } else {
                    // 查询到多条项目信息
                    if (siteData.size() > Constants.NUMBER_ONE) {
                        progress.setSmartRemarks("查询到多条项目信息");
                    }
                    // 取第一条信息
                    SmartSiteData data = siteData.get(Constants.NUMBER_ZERO);
                    progress.setSmartDeptName(data.getTitle());
                    String address = StrUtil.builder().append(data.getProvince()).append(data.getCity())
                            .append(data.getDistrict()).append(data.getAddress()).toString();
                    progress.setSmartDeptAddress(address);
                    progress.setSmartApproveStatus(Constants.NUMBER_TWO);
                }
            }
        }
        projectProgressService.updateProjectProgress(progress);
    }

    @Override
    public boolean syncSmartSiteProject(SmartSiteProjectSyncDto syncDto, ProjectEventReceiveRecord receiveRecord) {

        final Project project =
                Optional.ofNullable(syncDto).map(SmartSiteProjectSyncDto::getProjectId)
                        .map(projectMapper::selectById).orElseThrow(() -> new BusinessException(8010320));
        // 变更前数据
        final SmartSiteProjectSyncDto beforeChangeData =
            BeanUtil.copyProperties(project, SmartSiteProjectSyncDto.class);
        receiveRecord.setBeforeChangeData(JsonUtils.toJsonStr(beforeChangeData.setProjectId(project.getId())));
        // 需要更新数据
        receiveRecord.setChangeData(JsonUtils.toJsonStr(syncDto));
        // 转换填充工程/施工参数
        updateEngineerParam(syncDto);
        // 立项则插入，更新则更新
        final BusinessSystemDataSmartsite systemDataSmartsite = BeanUtil.copyProperties(syncDto,
                BusinessSystemDataSmartsite.class);
        if (receiveRecord.getEventCode().equals(ProjectEventEnum.INITIATION.getDictCode())) {
            // 填充业务板块数据
            businessSystemDataSmartsiteService.saveOrUpdate(systemDataSmartsite);
            // 立项时无须走业务板块审批配置
            updateProject(receiveRecord, syncDto);
        } else {
            // 更新业务板块信息
            businessSystemDataSmartsiteService.updateById(systemDataSmartsite);
            // 走前置事件（业务板块字段变更审批表）
            // 变更记录所有有变更的信息 匹配审批流程
            final List<ComparableResult> compareResult = ComparableUtils.compare(beforeChangeData, syncDto);
            // 忽略未发生变化的字段， 获取发生变化的字段
            final List<ComparableResult> changedFieldList =
                    compareResult.stream().filter(result -> !result.getHandlerType().equals(ComparableHandleTypeEnum.NO_CHANGE.getDictCode()))
                            .collect(Collectors.toList());
            // 走业务变更审批流配置前置判断方法
            final List<ComparableResult> noBpmResultList = dataChangeApprovalConfigService.matchApprovalProcess(project,
                    BusSysDataTypeEnum.SMART_SITE_SEGMENT, changedFieldList);
            if (CollectionUtils.isNotEmpty(noBpmResultList)) {
                final Map<String, Object> tempMap =
                        noBpmResultList.stream().filter(re -> !re.getFieldId().equals("projectType")).collect(Collectors.toMap(ComparableResult::getFieldId,
                                o -> Objects.isNull(o.getNewFieldContent()) ? "" : o.getNewFieldContent(),
                                (k1, k2) -> k2));
                if (ObjectUtils.isNotEmpty(tempMap)) {
                    final SmartSiteProjectSyncDto toUpdateSyncDto = BeanUtil.toBean(tempMap,
                            SmartSiteProjectSyncDto.class);
                    toUpdateSyncDto.setProjectId(project.getId());
                    updateProject(receiveRecord, toUpdateSyncDto);
                }
            }
        }

        return Boolean.TRUE;
    }

    private void updateEngineerParam(SmartSiteProjectSyncDto syncDto) {
        // 填充工程/施工参数
        List<SmartsiteEngineerParameter> paramList = new ArrayList<>();
        final String engineerParameter = syncDto.getEngineerParameter();
        if (JSONUtil.isJsonArray(engineerParameter)) {
            final JSONArray jsonArray = JSONUtil.parseArray(engineerParameter);
            final String projectType = syncDto.getProjectType();
            final String[] split = projectType.split("#");
            if (jsonArray.size() > Constants.NUMBER_ZERO && split.length >= Constants.NUMBER_TWO) {
                final String ids = split[1];
                final String[] idSplit = ids.split(",");
                final String id = idSplit[idSplit.length - 1];
                jsonArray.stream().filter(Objects::nonNull).forEach(arr -> {
                    final JSONObject parseObj = JSONUtil.parseObj(arr);
                    final Set<Map.Entry<String, Object>> entries = parseObj.entrySet();
                    for (Map.Entry<String, Object> entry : entries) {
                        String key = entry.getKey();
                        final Object value = entry.getValue();
                        if (key.startsWith("__CONTRACT_CONSTRUCTION_PARAMS__")) {
                            key = key.replace("__CONTRACT_CONSTRUCTION_PARAMS__", "");
                        }
                        final SmartsiteEngineerParameter cacheParameter = engineerParameterService.getByIdAndDataKey(id, key);
                        if (ObjectUtils.isNotEmpty(cacheParameter)) {
                            cacheParameter.setValue(String.valueOf(value));
                            paramList.add(cacheParameter);
                        }
                    }
                });
            }
            syncDto.setEngineerParameter(JsonUtils.toJsonStr(paramList));
        }
    }

    private void updateProject(ProjectEventReceiveRecord receiveRecord, SmartSiteProjectSyncDto syncDto) {
        try {
            projectMapper.syncSmartSiteProjectInfo(syncDto);
            receiveRecord.setFinalChageData(JsonUtils.toJsonStr(syncDto));
        } catch (Exception e) {
            log.error("更新项目信息失败: {}", e.getMessage());
            receiveRecord.setFinalChageData(null);
            throw new FrameworkException(-1, "更新项目信息失败");
        }
    }

    @Override
    public SmartSiteData genSmartSiteData(ProjectInfo projectInfo, ProjectBasicInfo basicInfo,
        ProjectContractInfo contractInfo) {
        return new SmartSiteData().setAddress(basicInfo.getProjectAddressCn())
            // 全称 简称
            .setTitle(projectInfo.getProjectName()).setProjectAbbreviation(basicInfo.getProjectAbbreviation())
            // 项目部合同备案经理
            .setProjectManagerName(contractInfo.getContractPm())
            .setProjectManagerPhone(contractInfo.getContractTelephone())
            // 项目执行经理
            .setProjectExecutiveManagerName(contractInfo.getOperatorPm())
            .setProjectExecutiveManagerPhone(contractInfo.getOperatorTelephone());
    }

    @Override
    public SmartSiteProjectSyncDto getSmartSiteProjectSyncDto(ProjectInfo projectInfo, ProjectBasicInfo basicInfo,
            ProjectContactInfo contactInfo, ProjectAgreementInfo agreementInfo, ProjectContractInfo contractInfo,
            ProjectRelyInfo projectRelyInfo) {
        SmartSiteProjectSyncDto syncDto = null;
        try {
            // 项目中心项目id
            final String standardProjectId = projectInfo.getStandardProjectId();
            // 业主开工令
            final Long ownerStartDate =
                Optional.ofNullable(agreementInfo).map(ProjectAgreementInfo::getOwnerStartDate).map(Date::getTime)
                    .filter(m -> m > 0).orElse(null);
            // 监理开工令
            final Long managerStartDate =
                Optional.ofNullable(agreementInfo).map(ProjectAgreementInfo::getManagerStartDate).map(Date::getTime)
                    .filter(m -> m > 0).orElse(null);
            // 项目进场日期
            final Long projectApprovalDate =
                Optional.ofNullable(agreementInfo).map(ProjectAgreementInfo::getProjectApprovalDate).map(Date::getTime)
                    .filter(m -> m > 0).orElse(null);
            // 实际开式日期 优先获取：业主开工令>监理开工令>项目进场
            final Long realityStartDate =
                Optional.ofNullable(ownerStartDate)
                    .orElse(Optional.ofNullable(managerStartDate).orElse(projectApprovalDate));
            //
            //
            // Long realityStartDate =
            //         null != ownerStartDate ? ownerStartDate : null != managerStartDate ? managerStartDate :
            //                 projectApprovalDate;
            // 转换工程状态
            String projectStatusEng = null;
            final String infoProjectStatus = agreementInfo.getProjectStatus();

            // 拆分施工项目状态字段第一级用于映射编码
            String infoFirstProjectStatus = infoProjectStatus.split(Constants.COMMA_ENG)[0];

            if (StringUtils.isNotBlank(infoFirstProjectStatus)) {
                if (Constants.PREPARE.equals(infoFirstProjectStatus) || Constants.PROJECT_APPROVAL.equals(infoFirstProjectStatus)
                        || Constants.CONSTRUCTION_PREPARATION.equals(infoFirstProjectStatus)) {
                    projectStatusEng = ProjectStatusEnum.OPERATING_RESERVE.getDictCode();
                } else if (1 == 2) {
                    // todo 将工地的 结算调整 状态转换为 竣工 状态
                    projectStatusEng = ProjectStatusEnum.BE_COMPLETED.getDictCode();
                } else {
                    final ProjectStatusEnum enumCode = ProjectStatusEnum.getEnumCode(infoFirstProjectStatus);
                    projectStatusEng = ObjectUtils.isNotEmpty(enumCode) ? enumCode.getDictCode() : null;
                }
            }

            syncDto = new SmartSiteProjectSyncDto()
                    .setProjectId(Optional.ofNullable(standardProjectId).map(Long::valueOf).orElse(null))
                    // .setSmartSiteApprovalAt(null == projectInfo.getCreatedAt() ? null : projectInfo.getCreatedAt())
                    .setYunshuOrgId(projectInfo.getYunshuOrgId())
                    // 工程结构 施工范围
                    // .setProjectStructure(basicInfo.getProjectStructure()).setProjectRange(basicInfo
                    // .getProjectRange())
                    // 项目概述（smart_site_project_description）
                    // .setProjectDescription(basicInfo.getProjectDescription())
                    // 施工参数 是否有装配项
                    .setEngineerParameter(basicInfo.getProjectParameter())
                    // .setHasAssembled(basicInfo.getHasAssembled())
                    // 装配内容 装配面积
                    // .setAssembledInfo(basicInfo.getAssembledInfo()).setAssembledArea(basicInfo.getAssembledArea())
                    // 是否边远散小项目 是滞生态敏感区
                    .setIsEdgeSmall(Optional.ofNullable(basicInfo.getIsEdgeArea()).map(Integer::valueOf).orElse(null))
                    .setIsEcologySensitive(Optional.ofNullable(basicInfo.getIsSensitiveArea()).map(Integer::valueOf).orElse(null))
                    // 是否创新项目
                    // .setIsInnovation(basicInfo.getIsInnovation()).setDeputy(contactInfo.getDeputy())
                    // 装配率 施工分包信息
                    // .setAssembledRatio(basicInfo.getAssembledRatio()).setSubpackageInfo(contactInfo
                    // .getSubpackageInfo())
                    // .setContractPm(contractInfo.getContractPm())
                    // .setContractTelephone(contractInfo.getContractTelephone()).setOperatorPm(contractInfo
                    // .getOperatorPm())
                    // .setOperatorTelephone(contractInfo.getOperatorTelephone()).setBidPm(contractInfo.getBidPm())
                    // .setBidTelephone(contractInfo.getBidTelephone()).setGovPm(contractInfo.getGovPm())
                    // .setGovTelephone(contractInfo.getGovTelephone())
                    // 项目进场日期
                    .setRealEnterTime(projectApprovalDate)
                    // 业主下发开工日期
                    // .setOwnerStartDate(ownerStartDate)
                    // 监理下发开工日期
                    // .setManagerStartDate(managerStartDate)
                    // 实际开式日期 优先获取：业主开工令>监理开工令>项目进场
                    .setRealWorkBeginTime(realityStartDate)
                    // 合同竣工日期
                .setWorkerEndTime(Optional.ofNullable(agreementInfo.getContractEndDate()).map(Date::getTime)
                    .filter(m -> m > 0).orElse(null))
                    // 竣工备案日期
                .setRecordDate(Optional.ofNullable(agreementInfo.getRecordDate()).map(Date::getTime).filter(m -> m > 0)
                    .orElse(null))
                    // 五方主休验收日期
                .setRealOpenTrafficTime(Optional.ofNullable(agreementInfo.getCheckDate()).map(Date::getTime)
                    .filter(m -> m > 0).orElse(null))
                // 实际竣工日期 优先取竣工备案日期，为空则取五方验收日期 再为空则取项目完工(通车)日期
                .setWorkEndTime(Optional.ofNullable(agreementInfo.getRecordDate()).map(Date::getTime).filter(m -> m > 0)
                    .orElse(Optional.ofNullable(agreementInfo.getCheckDate()).map(Date::getTime).filter(m -> m > 0)
                        .orElse(Optional.ofNullable(agreementInfo.getProjectFinishDate()).map(Date::getTime)
                            .filter(m -> m > 0).orElse(null))))
                // .setWorkEndTime(ObjectUtils.isNotEmpty(agreementInfo.getRecordDate().getTime())
                // ? agreementInfo.getRecordDate().getTime() : agreementInfo.getCheckDate().getTime())
                    // 项目施工状态(项目状态(工程))
                    .setProjectType(basicInfo.getProjectType())
                    .setProjectStatusEng(projectStatusEng)
                    .setProjectScale(basicInfo.getProjectScale())
                    .setSmartContractModel(basicInfo.getContractModel())
                    .setSmartProjectAddress(basicInfo.getProjectAddressCn())
                    .setContractStartDate(null == agreementInfo.getContractStartDate() ? null :
                            agreementInfo.getContractStartDate().getTime())
                    .setContractEndDate(null == agreementInfo.getContractStartDate() ? null :
                            agreementInfo.getContractStartDate().getTime())
                    .setProjectA8no(basicInfo.getProjectA8no())
                    .setLng(basicInfo.getLng())
                    .setLat(basicInfo.getLat())
                    .setQualityTask(contractInfo.getQualityTask())
                    .setSecurityTask(contractInfo.getSecurityTask())
                    .setYzwVideoId(Objects.nonNull(projectRelyInfo.getVideoProjectId()) ?
                            projectRelyInfo.getVideoProjectId() : "")
                    .setYzwServiceId(Objects.equals("云筑", projectRelyInfo.getServiceType())
                            ? projectRelyInfo.getServiceSystemId() : "")
                    .setSmartSiteProjectId(Optional.ofNullable(projectInfo.getId()).map(Long::valueOf).orElse(null))
                .setSmartSitePinmingId(
                    Optional.ofNullable(projectInfo.getPinmingProjectId()).map(Long::valueOf).orElse(null))
                .setSmartApprovalTime(Optional.ofNullable(agreementInfo.getApprovalTime()).map(Date::getTime)
                    .filter(m -> m > 0).orElse(null));
        } catch (Exception e) {
            log.warn("更新工程信息失败 -> 工地项目信息 ：projectInfo: {}", JSONUtil.toJsonStr(projectInfo));
        }
        //到智慧工地树查询父级组织信息
        try {
            getYunShuSmartConstructionOrgResp(syncDto, projectInfo.getYunshuOrgId());
        } catch (Exception e) {
            log.warn("获取智慧工地树组织及上组织信息失败", e);
        }
        return syncDto;
    }

    /**
     * 到智慧工地树查询父级组织信息
     *
     * @param orgId
     * @return {@link YunShuSmartConstructionOrgResp}
     */
    private void getYunShuSmartConstructionOrgResp(SmartSiteProjectSyncDto syncDto, String orgId) {
        final YunShuSmartConstructionOrgResp info = yunShuSmartConstructionOrgService.findDeptAndParentAndTreeInfo(orgId);
        syncDto.setYunshuParentOrgId(info.getOrgID()).setYunshuParentOrgName(info.getOrgFullName())
                .setYunshuParentTreeId(info.getTreeId()).setYunshuQueryCode(info.getChild().getQueryCode())
                .setYunshuTreeId(info.getChild().getTreeId());
//        try{
//            final YunShuSmartConstructionOrgResp smartOrgResp = yunshuSmartOrgService.findByDepartmentId(orgId).getData();
//            Optional<YunShuSmartConstructionOrgResp> optionalSmartOrgResp = Optional.ofNullable(smartOrgResp);
//            // 获取 treeParentId，如果 smartOrgResp 不为 null，则获取其值，否则返回一个默认值（例如，-1）
//            String parentTreeId = optionalSmartOrgResp.map(YunShuSmartConstructionOrgResp::getTreeParentId).orElse("");
//            String parentOrgId = optionalSmartOrgResp.map(YunShuSmartConstructionOrgResp::getParentId).orElse("");
//            syncDto.setYunshuParentOrgId(parentTreeId).setYunshuParentOrgId(parentOrgId);
//            if (StringUtils.isNotBlank(parentOrgId)) {
//                final YunShuSmartConstructionOrgResp smartParentOrgResp = yunshuSmartOrgService.findByDepartmentId(parentOrgId).getData();
//                Optional<YunShuSmartConstructionOrgResp> optionalSmartParentOrgResp = Optional.ofNullable(smartParentOrgResp);
//                String parentOrgName = optionalSmartParentOrgResp.map(YunShuSmartConstructionOrgResp::getOrgFullName).orElse("");
//                syncDto.setYunshuParentOrgName(parentOrgName);
//            }
//        }catch (Exception e){
//            log.info(e.getMessage());
//        }

    }

//    @Override
//    public void createProjectDept(Project project, SmartSiteData siteData) {
//        log.info("createProjectDept==>>siteData:{}", siteData);
//        CreateProjectDeptReq deptReq = new CreateProjectDeptReq();
//        deptReq.setOrgType(DeptTypeEnum.PROJECT_DEPT.getDictCode());
//        final String fullName = siteData.getTitle();
//        deptReq.setFullName(fullName);
//        final String abbreviation = siteData.getProjectAbbreviation();
//        deptReq.setAbbreviation(StringUtils.isBlank(abbreviation) ? fullName : abbreviation);
//        // 获取上级组织
//        deptReq.setParentOrgCode(getParentOrgCode(project.getExecuteUnitCode()));
//        deptReq.setEstablishmentDate(DateUtil.current());
//        //暂时不设置行政区域字段
//        // deptReq.setRegionCode(siteData.getProvince());
//        final String address =
//            StringUtils.join(siteData.getProvince(), siteData.getCity(), siteData.getDistrict(), siteData
//            .getAddress());
//
//        deptReq.setAddress(address);
//        deptReq.setProductionCode(project.getA8ProjectCode());
//        if (Objects.nonNull(siteData.getPointy())) {
//            deptReq.setLongitude(siteData.getPointy().toString());
//        }
//        if (Objects.nonNull(siteData.getPointx())) {
//            deptReq.setLatitude(siteData.getPointx().toString());
//        }
//        // 管理团队
//        ArrayList<ManagingMemberDTO> managementTeam = CollUtil.newArrayList();
//        if (StrUtil.isNotBlank(siteData.getProjectManagerName())) {
//            // 项目部合同备案经理
//            ManagingMemberDTO recordManager = new ManagingMemberDTO();
//            recordManager.setPositionCode(PositionNameEnum.CONTRACT_RECORD_MANAGER.getDictCode());
//            recordManager.setPositionName(PositionNameEnum.CONTRACT_RECORD_MANAGER.getZhCN());
//            recordManager.setMemberName(siteData.getProjectManagerName());
//            recordManager.setPhoneNumber(siteData.getProjectManagerPhone());
//            managementTeam.add(recordManager);
//        }
//        if (StrUtil.isNotBlank(siteData.getProjectExecutiveManagerName())) {
//            // 项目执行经理
//            ManagingMemberDTO executiveManager = new ManagingMemberDTO();
//            executiveManager.setPositionCode(PositionNameEnum.PROJECT_EXECUTIVE_MANAGER.getDictCode());
//            executiveManager.setPositionName(PositionNameEnum.PROJECT_EXECUTIVE_MANAGER.getZhCN());
//            executiveManager.setMemberName(siteData.getProjectExecutiveManagerName());
//            executiveManager.setPhoneNumber(siteData.getProjectExecutiveManagerPhone());
//            managementTeam.add(executiveManager);
//        }
//        if (StrUtil.isNotBlank(siteData.getSafetySupervisionName())) {
//            // 安监责任人
//            ManagingMemberDTO safetyDirector = new ManagingMemberDTO();
//            safetyDirector.setPositionCode(PositionNameEnum.SAFETY_DIRECTOR.getDictCode());
//            safetyDirector.setPositionName(PositionNameEnum.SAFETY_DIRECTOR.getZhCN());
//            safetyDirector.setMemberName(siteData.getSafetySupervisionName());
//            safetyDirector.setPhoneNumber(siteData.getSafetySupervisionPhone());
//            managementTeam.add(safetyDirector);
//        }
//        deptReq.setManagementTeam(managementTeam);
//
//
//        GenericityResponse<CreatedRes> createdResp;
//        try {
//            do {
//                project.setProjectDeptName(deptReq.getFullName());
//                project.setProjectDeptAbbreviation(deptReq.getAbbreviation());
//                createdResp = stdOrgService.createProjectDept(deptReq);
//                deptReq.setFullName(deptReq.getFullName() + Constants.NUMBER_ONE);
//            } while (ProjectEnum.PROJECT_DEPT_EXISTS.getCode().equals(createdResp.getStatus()));
//        } catch (Exception e) {
//            log.error("UC创建项目部失败:{}",e.getMessage());
//            throw new FrameworkException(-1, e.getMessage());
//        }
//        // ---------------------------------------------------------------
//        // 创建失败
//        CreatedRes respData = createdResp.getData();
//        if (Boolean.FALSE.equals(respData.getIsSuccess()) || !Constants.NUMBER_ZERO.equals(createdResp.getStatus())) {
//            throw new BusinessException(8010005, new String[]{DeptTypeEnum.PROJECT_DEPT.getZhCN()});
//        }
//        // 更新项目信息
//        try {
//            this.updateProjectInfo(project, siteData, respData.getCode(), respData);
//        } catch (Exception e) {
//            log.error("更新项目信息失败");
//            throw new FrameworkException(-1, "更新项目信息失败");
//        }
//    }

//    private void createProjectDeptByProject(Project project) {
//        log.info("createProjectDeptByProject==>>project:{}", project);
//        CreateProjectDeptReq deptReq = new CreateProjectDeptReq();
//        deptReq.setOrgType(DeptTypeEnum.PROJECT_DEPT.getDictCode());
//        deptReq.setFullName(project.getProjectFinanceName());
//        final String projectFinanceAbbreviation = project.getProjectFinanceAbbreviation();
//        deptReq.setAbbreviation(StringUtils.isBlank(
//            projectFinanceAbbreviation)? project.getProjectFinanceName() : projectFinanceAbbreviation);
//        // 获取上级组织
//        deptReq.setParentOrgCode(getParentOrgCode(project.getExecuteUnitCode()));
//        deptReq.setEstablishmentDate(DateUtil.current());
//        //暂时不设置行政区域字段
//        String regionIdPath = project.getRegionIdPath();
//        regionIdPath = regionIdPath.startsWith(Constants.ID_PATH_CONNECTOR) ?  regionIdPath.replaceFirst(Constants
//        .ID_PATH_CONNECTOR, "") : regionIdPath;
//        deptReq.setRegionCode(regionIdPath);
//        deptReq.setAddress(project.getProjectAddress());
//        // 调用UC创建项目部,如果提示名称重复,在名称后面拼接1
//
//        // ----------------UC创建项目部时，更新项目进度表信息--------------
//        ProjectProgress progress = projectProgressService.selectProjectProgress(project.getId());
//        if (progress == null) {
//            throw new BusinessException(8010205);
//        }
//        GenericityResponse<CreatedRes> createdResp;
//        try {
//            do {
//                project.setProjectDeptName(deptReq.getFullName());
//                // 传递到UC的时间
//                progress.setToUcTime(Instant.now().toEpochMilli());
//                progress.setUcDepartStatus(Constants.NUMBER_ONE);
//                createdResp = stdOrgService.createProjectDept(deptReq);
//                deptReq.setFullName(deptReq.getFullName() + Constants.NUMBER_ONE);
//            } while (ProjectEnum.PROJECT_DEPT_EXISTS.getCode().equals(createdResp.getStatus()));
//        } catch (Exception e) {
//            progress.setUcDepartStatus(Constants.NUMBER_ONE);
//            progress.setUcRemarks(e.getMessage());
//            // 更新项目信息
//            projectProgressService.updateProjectProgress(progress);
//            throw e;
//        }
//        // 如果创建项目部成功，记录项目部地址字段，否则抛出异常
//        if (Constants.NUMBER_ZERO.equals(createdResp.getStatus())) {
//            progress.setUcDepartStatus(Constants.NUMBER_TWO);
//            progress.setSmartDeptName(deptReq.getFullName()).setSmartDeptAddress(deptReq.getAddress());
//            projectProgressService.updateProjectProgress(progress);
//        } else {
//            // ---------------- 将UC返回的错误的具体信息写到项目进度表信息中--------------
//            progress.setUcDepartStatus(Constants.NUMBER_ONE);
//            progress.setUcRemarks(createdResp.getMsg());
//            projectProgressService.updateProjectProgress(progress);
//            // ---------------------------------------------------------------
//            throw new FrameworkException(createdResp.getStatus(), createdResp.getMsg());
//        }
//
//        CreatedRes respData = createdResp.getData();
//        if (Boolean.FALSE.equals(respData.getIsSuccess())) {
//            throw new BusinessException(8010005, new String[]{DeptTypeEnum.PROJECT_DEPT.getZhCN()});
//        }
//        // 更新项目信息,此时智慧工地回传信息为空
//        this.updateProjectInfo(project, new SmartSiteData(), respData.getCode(), respData);
//    }

//    private void ucInfoToProjectProgress(ProjectProgress progress, GenericityResponse<CreatedRes> createdResp) {
//        // 将UC返回的错误的具体信息写到项目进度表信息中
//        if (Constants.NUMBER_ZERO.equals(createdResp.getStatus())) {
//            progress.setUcDepartStatus(Constants.NUMBER_TWO);
//        } else {
//            progress.setUcDepartStatus(Constants.NUMBER_ONE);
//            progress.setUcRemarks(createdResp.getMsg());
//        }
//        projectProgressService.updateProjectProgress(progress);
//    }


    private void updateProjectInfo(Project project, SmartSiteData siteData) {
        // 项目部信息
//        project.setProjectDeptId(deptId);
//        project.setProjectDeptIdPath(StrUtil.builder().append(respData.getIdPath()).append(respData.getId())
//                .append(StrUtil.SLASH).toString());
        project.setProjectDeptType(DeptTypeEnum.PROJECT_DEPT.getDictCode());
        project.setUpdateAt(System.currentTimeMillis());
        // 若需要智慧工地覆盖以下业务字段时更新,特殊立项时开工日期等字段由用户输入
        if (SourceSystemEnum.MARKETING.getDictCode().equals(project.getSourceSystem())) {
            if (StrUtil.isNotBlank(siteData.getActualStart())) {
                LocalDateTime actualStart = DateTimeUtil.longToLocalDateTime(Long.parseLong(siteData.getActualStart()));
                Long startTime = DateTimeUtil.localDateTime2MilliSecond(actualStart);
                project.setRealWorkBeginTime(startTime);
                // 预计实际竣工日期=智慧工地的实际开工时间+市场营销的总工期
                LocalDateTime predictWork = actualStart.plusDays(project.getCountDays());
                Long predictWorkEndTime = DateTimeUtil.localDateTime2MilliSecond(predictWork);
                project.setPredictWorkEndTime(predictWorkEndTime);
            }
            if (StrUtil.isNotBlank(siteData.getCompletionRecordTime())) {
                project.setWorkEndTime(Long.parseLong(siteData.getCompletionRecordTime()));
            }
            if (StrUtil.isNotBlank(siteData.getOpenTime())) {
                project.setRealOpenTrafficTime(Long.parseLong(siteData.getOpenTime()));
            }
        }

        log.info("updateProjectInfo==>>project:{}", project);
        Integer result = projectMapper.updateProjectBySmartSite(project);
        if (Constants.NUMBER_ZERO.equals(result)) {
            throw new BusinessException(8010008, new String[]{ProjectConstant.PROJECT_DATA});
        }
    }


//    private String getParentOrgCode(String orgCode) {
//        log.info("getParentOrgCode==>>orgCode:{}", orgCode);
//        GenericityResponse<StandardOrganizationInfoRes> orgDetailResp = stdOrgService.geOrgInfoByCode(orgCode);
//        if (!Constants.NUMBER_ZERO.equals(orgDetailResp.getStatus())) {
//            throw new FrameworkException(orgDetailResp.getStatus(), orgDetailResp.getMsg());
//        }
//        // 能否下设项目部/指挥部
//        if (Objects.nonNull(orgDetailResp.getData())
//                && Constants.NUMBER_ONE.equals(orgDetailResp.getData().getIsSetupSub())) {
//            return orgCode;
//        }
//        // 有无单位直管项目
//        GenericityResponse<List<StdorganizationTreeNodeRes>> orgTreeResp = stdOrgService.orgTreeNode(orgCode);
//        if (!Constants.NUMBER_ZERO.equals(orgTreeResp.getStatus())) {
//            throw new FrameworkException(orgTreeResp.getStatus(), orgTreeResp.getMsg());
//        }
//        if (CollUtil.isEmpty(orgTreeResp.getData())) {
//            throw new BusinessException(8010053);
//        }
//        List<StdorganizationTreeNodeRes> projectGroupList = orgTreeResp.getData().stream().filter(e ->
//                ProjectEnum.DM_PROJECT_GROUP.getCode().equals(e.getOrgType())).collect(Collectors.toList());
//        if (CollUtil.isNotEmpty(projectGroupList)) {
//            return projectGroupList.get(Constants.NUMBER_ZERO).getCode();
//        }
//        throw new BusinessException(8010053);
//    }

    @Override
    @Async("cpmTaskExecutor")
    public void addSmartSiteOutResp(ProjectDetailOutResp detailOutResp, Long projectId) {
        try {
            final long epochMilli = Instant.now().toEpochMilli();
            final SmartsiteSyncProjectInfo syncProjectInfo = new SmartsiteSyncProjectInfo();
            syncProjectInfo.setProjectId(projectId)
                .setSmartsiteProjectBasicInfo(JsonUtils.toJsonStr(detailOutResp.getProjectBasicInfo()))
                .setSmartsiteProjectAgreementInfo(JsonUtils.toJsonStr(detailOutResp.getProjectAgreementInfo()))
                .setSmartsiteProjectInfo(JsonUtils.toJsonStr(detailOutResp.getProjectInfo()))
                .setSmartsiteProjectContactInfo(JsonUtils.toJsonStr(detailOutResp.getProjectContactInfo()))
                .setSmartsiteProjectContractInfo(JSONUtil.toJsonStr(detailOutResp.getProjectContractInfo()))
                .setSmartsiteProjectRelyInfo(JSONUtil.toJsonStr(detailOutResp.getProjectRelyInfo()))
                .setSmartsiteProjectHookInfo(JSONUtil.toJsonStr(detailOutResp.getProjectHookInfo()))
                .setHookNum(detailOutResp.getHookNum())
                .setHookDTOList(JSONUtil.toJsonStr(detailOutResp.getHookDTOList()))
                .setUpdateTime(epochMilli);

            smartsiteSyncProjectInfoService.insertOrUpdate(syncProjectInfo);
        } catch (Exception e) {
            log.error("保存、更新智慧工地项目信息异常：{}", e.getMessage());
        }
    }

//    @Deprecated
//    @Override
//    public void createProjectDeptScheduled(Project project) {
//        try {
//            // 构建请求参数 SmartSiteData
//            final SmartSiteData siteData = new SmartSiteData();
//            final String projectName = project.getProjectName();
//            final String financeAbbreviation = project.getProjectName();
//            final String projectAddress = project.getProjectAddress();
//            siteData.setTitle(projectName)
//                .setProjectAbbreviation(
//                    StringUtils.isNotBlank(financeAbbreviation) ? financeAbbreviation : projectName)
//                .setAddress(StringUtils.isNotBlank(projectAddress) ? projectAddress : "-")
//                .setYunshuOrgId(project.getYunshuOrgId());
//            // 构建请求参数 deptReq
////            CreateProjectDeptReq deptReq = new CreateProjectDeptReq();
////            deptReq.setOrgType(DeptTypeEnum.PROJECT_DEPT.getDictCode()).setFullName(projectName)
////                .setAbbreviation(StringUtils.isNotBlank(financeAbbreviation) ? financeAbbreviation : projectName)
////                .setParentOrgCode(getParentOrgCode(project.getExecuteUnitCode()))
////                .setEstablishmentDate(DateUtil.current())
////                .setAddress(StringUtils.isNotBlank(projectAddress) ? projectAddress : "-");
////            // 创建项目部
////            final GenericityResponse<CreatedRes> createdResp = stdOrgService.createProjectDept(deptReq);
////            if (0 == createdResp.getStatus() && Boolean.TRUE.equals(createdResp.getData().getIsSuccess())) {
////                final CreatedRes data = createdResp.getData();
////                project.setProjectDeptName(deptReq.getFullName()).setProjectDeptAbbreviation(deptReq
// .getAbbreviation());
//                this.updateProjectInfo(project, siteData);
////            } else {
////                throw new FrameworkException(createdResp.getStatus(), createdResp.getMsg());
////            }
//
//        } catch (Exception e) {
//            log.error("创建项目部失败：id:{}-{}", project.getId(), e.getMessage());
//            throw new FrameworkException(-1, e.getMessage());
//        }
//    }
}
