package com.cscec3b.iti.projectmanagement.server.entity;

import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;
import lombok.experimental.Accessors;

/**
 * 帮助中心
 */
@ApiModel(value = "帮助中心")
@Data
@Accessors(chain = true)
public class HelpCenter {
    /**
     * 主键id
     */
    @ApiModelProperty(value = "主键id")
    private Long id;

    /**
     * 标题
     */
    @ApiModelProperty(value = "标题")
    private String title;

    /**
     * 状态 0: 删除; 1: 已发布;  2: 草稿
     */
    @ApiModelProperty(value = "状态 0: 删除; 1: 已发布;  2: 草稿")
    private int status;

    /**
     * 发布时间
     */
    @ApiModelProperty(value = "发布时间")
    private Long publishTime;

    /**
     * 创建人
     */
    @ApiModelProperty(value = "创建人")
    private String createBy;

    /**
     * 创建时间
     */
    @ApiModelProperty(value = "创建时间")
    private Long createAt;

    @ApiModelProperty(value = "更新时间")
    private Long updateAt;
}