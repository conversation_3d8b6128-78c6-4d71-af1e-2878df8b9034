package com.cscec3b.iti.projectmanagement.server.mapper;

import java.util.List;

import org.apache.ibatis.annotations.Mapper;
import org.apache.ibatis.annotations.Param;

import com.baomidou.mybatisplus.core.mapper.BaseMapper;
import com.cscec3b.iti.projectmanagement.api.dto.dto.ExecuteUnitTreeDto;
import com.cscec3b.iti.projectmanagement.api.dto.request.event.QuerySubscribersReq;
import com.cscec3b.iti.projectmanagement.api.dto.response.SubscriberForApprovalTypeResp;
import com.cscec3b.iti.projectmanagement.server.entity.ProjectEventSubscribe;
import com.cscec3b.iti.projectmanagement.server.entity.dto.ClientRequestDataPermission;
import com.cscec3b.iti.projectmanagement.server.entity.dto.ProjectFlowEventSubscribeDto;
import com.cscec3b.iti.projectmanagement.server.pushservice.enums.FlowNodeDataTypeEnum;
import com.cscec3b.iti.projectmanagement.server.pushservice.enums.FlowNodeEnum;
import com.cscec3b.iti.projectmanagement.server.pushservice.enums.FlowNodeHandlerEnum;

@Mapper
public interface ProjectEventSubscribeMapper extends BaseMapper<ProjectEventSubscribe> {
    ProjectEventSubscribe selectByPrimaryKey(Long consumerId);

    int updateByPrimaryKey(ProjectEventSubscribe records);


    int deleteByPrimaryKey(Long consumerId);

    List<ProjectEventSubscribe> getSubscriberPages(@Param("vo") QuerySubscribersReq params);

    Integer insertSubscriber(@Param("vo") ProjectEventSubscribe subscribe);

    List<ProjectFlowEventSubscribeDto> getFlowEventSubscribers(@Param("nodeEnum") FlowNodeEnum flowNodeCode,
            @Param("handlerEnum") FlowNodeHandlerEnum flowHandlerCode,
            @Param("dataTypeEnum") FlowNodeDataTypeEnum dataTypeEnum, @Param("customerId") String customerId);

    ProjectEventSubscribe selectByAppCode(@Param("appCode") String appCode);

    /**
     * 通过appKey 获取业务系统信息
     *
     * @param openApiKey 业务系统appKey
     * @return {@link ProjectEventSubscribe}
     */
    ProjectEventSubscribe selectByOpenApiKey(@Param("openApiKey") String openApiKey);


    /**
     * 判断业务系统是否订阅对应板块的事件信息
     *
     * @param subscribeId  业务系统id
     * @param flowNodeCode 事件板块信息
     * @param dataTypeEnum 数据类型信息
     * @return int
     */
    ProjectFlowEventSubscribeDto existEventBySubscribeIdAndNodeAndType(@Param("subscribeId") Long subscribeId,
        @Param("nodeEnum") FlowNodeEnum flowNodeCode, @Param("dataTypeEnum") FlowNodeDataTypeEnum dataTypeEnum);

    /**
     * 查询业务系统列表
     *
     * @param subscriber           业务系统名称
     * @param appCode              业务系统标识
     * @param executeUnitQueryCode 云枢执行单位
     * @return {@link List}<{@link SubscriberForApprovalTypeResp}>
     */
    List<SubscriberForApprovalTypeResp> getSubscriberForApprovalType(@Param("subscriber") String subscriber,
            @Param("appCode") String appCode,
            @Param("executeUnitQueryCode") String executeUnitQueryCode);

    /**
     * 更新业务系统表中的云枢组织信息
     *
     * @param partition 云枢组织信息
     */
    void updateSubscriberByExecuteUnitId(List<ExecuteUnitTreeDto> partition);

    ClientRequestDataPermission getClientRequestDataPermission(@Param("openApiKey") String openApiKey,
        @Param("reqOrgId") String requestOrgId);
}
