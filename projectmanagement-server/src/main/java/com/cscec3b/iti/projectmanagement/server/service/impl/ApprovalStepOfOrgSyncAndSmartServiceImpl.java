//package com.cscec3b.iti.projectmanagement.server.service.impl;
//
//import com.cscec3b.iti.projectmanagement.api.dto.request.approvalstep.ApprovalStepReq;
//import com.cscec3b.iti.projectmanagement.server.entity.BidApproval;
//import com.cscec3b.iti.projectmanagement.server.entity.Project;
//import com.cscec3b.iti.projectmanagement.server.enums.ApprovalStepEnum;
//import com.cscec3b.iti.projectmanagement.server.enums.IndependTypeEnum;
//import com.cscec3b.iti.projectmanagement.server.mapper.BidApprovalMapper;
//import com.cscec3b.iti.projectmanagement.server.mapper.ProjectMapper;
//import com.cscec3b.iti.projectmanagement.server.pushservice.enums.FlowNodeDataTypeEnum;
//import com.cscec3b.iti.projectmanagement.server.pushservice.enums.FlowNodeEnum;
//import com.cscec3b.iti.projectmanagement.server.pushservice.enums.FlowNodeHandlerEnum;
//import com.cscec3b.iti.projectmanagement.server.pushservice.event.CpmProjectFlowEvent;
//import com.cscec3b.iti.projectmanagement.server.service.AbstractApprovalStepService;
//import com.cscec3b.iti.projectmanagement.server.service.YunShuSmartConstructionOrgService;
//import lombok.AllArgsConstructor;
//import lombok.extern.slf4j.Slf4j;
//import org.springframework.context.ApplicationEventPublisher;
//import org.springframework.stereotype.Service;
//import org.springframework.transaction.annotation.Transactional;
//
//import java.util.Objects;
//
/// **
// * 立项步骤-组织同步与财商一体化
// *
// * <AUTHOR>
// * @date 2024/01/03
// */
//@Slf4j
//@Service("orgSyncAndSmartService")
//@AllArgsConstructor
//@Transactional(rollbackFor = Exception.class)
//public class ApprovalStepOfOrgSyncAndSmartServiceImpl extends AbstractApprovalStepService {
//
//    /**
//     * 立项步骤-类型映射服务
//     */
//
//    /**
//     * 中标未立项服务
//     */
//    private final BidApprovalMapper bidApprovalMapper;
//
//    /**
//     * 事件发布器
//     */
//    private final ApplicationEventPublisher publisher;
//
//    /**
//     * 项目进度服务
//     */
//
//    private final ProjectMapper projectMapper;
//
//    /**
//     * 云枢组织服务类
//     */
//    private final YunShuSmartConstructionOrgService yunShuSmartConstructionOrgService;
//
//    @Override
//    public Integer currentStep() {
//        return ApprovalStepEnum.FINANCE_SMART.getNo();
//    }
//
//
//    /**
//     * 需要检查组织与财商立项是否完成
//     * @param bidApproval 中标未立项
//     * @return boolean
//     */
//    @Override
//    public boolean checkCurrentStep(BidApproval bidApproval) {
//        final Long cpmProjectId = bidApproval.getCpmProjectId();
//        final Project project = projectMapper.selectById(cpmProjectId);
//        return Objects.nonNull(project) && Objects.nonNull(project.getProjectFinanceCode())
//                && Objects.nonNull(project.getYunshuOrgId());
//    }
//
//    /**
//     * 提交中标未立项项目类型信息
//     *
//     * @param stepReq 请求参数
//     * @return boolean
//     */
//
//    @Override
//    public boolean saveCurrentStep(ApprovalStepReq stepReq) {
//        final BidApproval bidApproval = this.bidApprovalMapper.selectById(stepReq.getBidApprovalId());
//        submitApprovalStep(bidApproval);
//        return true;
//    }
//
//
//    /**
//     * 推送财商立项事件，后续待财商立项完成通过财商编码查询组织信息
//     *
//     * @param bidApproval 中标未立项
//     */
//    @Override
//    public void preEvent(BidApproval bidApproval) {
//        // 如果是独立立项则要 触发财商与工地立项
//        if (Objects.nonNull(bidApproval)) {
//            log.info("[组织同步与财商一体化] preEvent： ->  财商立项事件触发");
//            final String independentProject = bidApproval.getIndependentProject();
//            final IndependTypeEnum independTypeEnum = IndependTypeEnum.getEnumByCode(independentProject);
//            if (IndependTypeEnum.INDEPENDENT.equals(independTypeEnum) && Objects.nonNull(bidApproval
//            .getCpmProjectId())) {
//                // 触发项目流转事件(财商立项项目 后置事件)
//                publisher.publishEvent(new CpmProjectFlowEvent(this, bidApproval.getCpmProjectId(),
//                        FlowNodeEnum.FINANCE_PROJECT, FlowNodeHandlerEnum.POST, FlowNodeDataTypeEnum.CREATE));
//            }
//        }
//    }
//}
