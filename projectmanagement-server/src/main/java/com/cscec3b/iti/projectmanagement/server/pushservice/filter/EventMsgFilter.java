package com.cscec3b.iti.projectmanagement.server.pushservice.filter;

import com.cscec3b.iti.projectmanagement.server.entity.Project;
import com.cscec3b.iti.projectmanagement.server.entity.ProjectEventPushRecord;
import com.cscec3b.iti.projectmanagement.server.entity.dto.ProjectFlowEventSubscribeDto;
import org.springframework.core.Ordered;

import javax.validation.constraints.NotNull;

public interface EventMsgFilter extends Ordered {
    void setNextFilter(EventMsgFilter nextFilter);

    boolean process(@NotNull Project project, @NotNull ProjectFlowEventSubscribeDto subscriber,
            ProjectEventPushRecord pushRecord);
}
