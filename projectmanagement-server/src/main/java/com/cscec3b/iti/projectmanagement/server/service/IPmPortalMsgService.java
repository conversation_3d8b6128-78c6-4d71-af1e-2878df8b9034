package com.cscec3b.iti.projectmanagement.server.service;


import com.cscec3b.iti.projectmanagement.api.dto.request.task.TaskReq;

/**
 * @Description 三局通对接接口IPmPortalMsgService
 * <AUTHOR>
 * @Date 2022/12/2 10:57
 */
public interface IPmPortalMsgService {
    /**
     * 推动信息门户
     *
     * @param task 任务
     * @description 消息推送到经办人的三局通账号
     * <AUTHOR>
     * @date 2022/12/2
     */
    void pushMessageToPortal(TaskReq task);
}
