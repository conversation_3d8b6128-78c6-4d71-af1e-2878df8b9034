package com.cscec3b.iti.projectmanagement.server.controller;

import com.cscec3b.iti.common.base.api.GenericityResponse;
import com.cscec3b.iti.common.base.api.ResponseBuilder;
import com.cscec3b.iti.common.base.page.Page;
import com.cscec3b.iti.projectmanagement.api.IRetryController;
import com.cscec3b.iti.retry.server.ApiRetryCallService;
import com.github.pagehelper.PageHelper;
import io.swagger.annotations.Api;
import lombok.extern.slf4j.Slf4j;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RestController;

/**
 * 异常重试
 *
 * <AUTHOR>
 * @date 2023/04/12 10:27
 **/

@RestController
@RequestMapping(IRetryController.PATH)
@Api(tags = "异常重试")
@Slf4j
public class RetryController implements IRetryController {

    private final ApiRetryCallService apiRetryCallService;

    public RetryController(ApiRetryCallService apiRetryCallService) {
        this.apiRetryCallService = apiRetryCallService;
    }

    /**
     * 获取需要干预的方法列表
     *
     * @param current
     * @param size
     * @param className
     * @param methodName
     * @param param
     * @return
     */
    @Override
    public GenericityResponse<Page<Object>> needIntervene(int current, int size, String className, String methodName,
            String param, String paramType, Long beginTime, Long endTime) {
        
        final com.github.pagehelper.Page<Object> pageInfo = PageHelper.startPage(current, size)
                                                                    .doSelectPage(() -> apiRetryCallService.getNeedHumanIntervention(className, methodName, param, paramType, beginTime, endTime));
        return ResponseBuilder.fromData(new Page<>(pageInfo.getTotal(), current, size).setRecords(pageInfo.getResult()));
    }

    /**
     * 置为自动执行
     * @param id
     * @return
     */
    @Override
    public GenericityResponse<Integer> intervene(String id) {
        return ResponseBuilder.fromData(apiRetryCallService.updateNeedHumanInterventionToAuto(id));
    }
    
    
}
