package com.cscec3b.iti.projectmanagement.server.entity;

import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;
import lombok.experimental.Accessors;

/**
 * <AUTHOR>
 * @description ProjectEventPushRecord
 * @date 2023/09/25 18:30
 */

/**
 * 项目事件推送记录表
 */
@ApiModel(description = "项目事件推送记录表")
@Data
@Accessors(chain = true)
public class ProjectEventPushRecord {

    /**
     * 推送记录表的主键id
     */
    @ApiModelProperty(value = "推送记录表的主键id")
    private String id;

    /**
     * 订阅者系统id
     */
    @ApiModelProperty(value = "订阅者系统id")
    private Long pushSystemId;

    /**
     * 推送订阅系统的接口地址
     */
    @ApiModelProperty(value = "推送订阅系统的接口地址")
    private String pushUrl;

    /**
     * 项目事件消息id，对应project_flow_event_record主键id
     */
    @ApiModelProperty(value = "项目事件消息id，对应project_flow_event_record主键id")
    private String projectMsgId;

    /**
     * 项目实体主键id
     */
    @ApiModelProperty(value = "项目实体主键id")
    private Long projectId;

    /**
     * 是否推送成功，根据业务系统响应结果判断
     */
    @ApiModelProperty(value = "是否推送成功，根据业务系统响应结果判断")
    private Integer isPushSuccess;

    /**
     * 推送时间
     */
    @ApiModelProperty(value = "推送时间")
    private Long pushTime;

    /**
     * 推送错误报文
     */
    @ApiModelProperty(value = "推送错误报文")
    private String errMsg;

    /**
     * 推送重试次数
     */
    @ApiModelProperty(value = "推送重试次数")
    private Integer times;

    /**
     * 日志级别：  warning, error,
     */
    private String logLevel;

    private Long startTime;

    private Long endTime;

}
