package com.cscec3b.iti.projectmanagement.server.controller;

import com.cscec3b.iti.common.base.api.GenericityResponse;
import com.cscec3b.iti.common.base.api.ResponseBuilder;
import com.cscec3b.iti.common.base.page.Page;
import com.cscec3b.iti.projectmanagement.api.IEngineeringProjectApi;
import com.cscec3b.iti.projectmanagement.api.dto.request.engineeringproject.*;
import com.cscec3b.iti.projectmanagement.api.dto.response.engineeringproject.*;
import com.cscec3b.iti.projectmanagement.server.service.IEngineeringProjectService;
import io.swagger.annotations.Api;
import lombok.RequiredArgsConstructor;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RestController;

import java.util.List;

/**
 * 工程项目管理控制器
 * 
 * <AUTHOR>
 * @date 2025/03/13
 */
@RestController
@RequestMapping(IEngineeringProjectApi.PATH)
@RequiredArgsConstructor
@Api(tags = "工程项目管理")
public class EngineeringProjectController implements IEngineeringProjectApi {

    private final IEngineeringProjectService engineeringProjectService;

    @Override
    public GenericityResponse<Boolean> addEngineeringProject(AddEngineeringProjectReq req) {
        return ResponseBuilder.fromData(engineeringProjectService.addEngineeringProject(req));
    }

    @Override
    public GenericityResponse<Boolean> hookEngineering2EngineeringProject(HookEngineering2EngineeringProjectReq req) {
        return ResponseBuilder.fromData(engineeringProjectService.hookEngineering2EngineeringProject(req));
    }

    @Override
    public GenericityResponse<Boolean> mappingProject2EngineeringProject(MappingStandard2EngineeringProjectReq req) {
        return ResponseBuilder.fromData(engineeringProjectService.mappingProject2EngineeringProject(req));
    }


    @Override
    public GenericityResponse<Page<EngineerProjectPageResp>> pageList(EngineerProjectPageReq req) {
        return ResponseBuilder.fromData(engineeringProjectService.pageList(req, null));
    }

    @Override
    public GenericityResponse<Page<EngineerProjectPageResp>> hookPageList(HookEngineerProjectPageReq req) {
        return ResponseBuilder.fromData(engineeringProjectService.hookPageList(req));
    }

    @Override
    public GenericityResponse<Boolean> unmappingProject2EngineeringProject(Long projectId) {
        return ResponseBuilder.fromData(engineeringProjectService.unmappingProject2EngineeringProject(projectId));
    }

    @Override
    public GenericityResponse<Boolean> transferProjectMapping(TransferProjectMappingReq req) {
        return ResponseBuilder.fromData(engineeringProjectService.transferProjectMapping(req));
    }

    @Override
    public GenericityResponse<Boolean> unmappingEngineering2EngineeringProject(Long engineeringProjectId) {
        return ResponseBuilder
            .fromData(engineeringProjectService.unmappingEngineering2EngineeringProject(engineeringProjectId));
    }

    @Override
    public GenericityResponse<Boolean> setMainProject(Long projectId, Long engineeringProjectId) {
        return ResponseBuilder.fromData(engineeringProjectService.setMainProject(projectId, engineeringProjectId));
    }

    @Override
    public GenericityResponse<EngineeringProjectInfoResp> getEngineeringProjectInfoByProject(Long projectId) {
        return ResponseBuilder.fromData(engineeringProjectService.getEngineeringProjectInfoByProject(projectId));
    }

    @Override
    public GenericityResponse<MappingStandardProjectResp>
        getMappingStandardProjectByEngineeringProject(Long engineeringProjectId) {
        return ResponseBuilder
            .fromData(engineeringProjectService.getMappingStandardProjectByEngineeringProject(engineeringProjectId));
    }

    @Override
    public GenericityResponse<MappingStandardProjectResp>
        getMappingStandardProjectByEngineeringProject(Long engineeringProjectId, Boolean getSmartSite) {
        return ResponseBuilder.fromData(engineeringProjectService
            .getMappingStandardProjectByEngineeringAndSmartSite(engineeringProjectId, getSmartSite));
    }

    @Override
    public GenericityResponse<Boolean> sendTodoTask(List<Long> engineerIds) {
        return ResponseBuilder.fromData(engineeringProjectService.sendTodoTask(engineerIds));
    }

    @Override
    public GenericityResponse<Page<CompanyViewEngineeringProjectResp>> companyViewPageList(CompanyViewEngineerProjectReq req) {
        return ResponseBuilder.fromData(engineeringProjectService.companyViewPageList(req));
    }

    @Override
    public GenericityResponse<List<CompanyViewEngineeringProjectTreeResp>> getCompanyViewEngineeringProjectTree(Long engineerId) {
        return ResponseBuilder.fromData(engineeringProjectService.getCompanyViewEngineeringProjectTree(engineerId));
    }
}
