package com.cscec3b.iti.projectmanagement.server.service;

import com.cscec3b.iti.common.base.page.Page;
import com.cscec3b.iti.projectmanagement.api.dto.request.special.CreateSpecialProjectReq;
import com.cscec3b.iti.projectmanagement.api.dto.request.special.QuerySpecialProjectParams;
import com.cscec3b.iti.projectmanagement.api.dto.request.special.UpdateSpecialProjectReq;
import com.cscec3b.iti.projectmanagement.api.dto.response.special.SpecialProjectDetailResp;
import com.cscec3b.iti.projectmanagement.api.dto.response.special.SpecialProjectResp;

/**
 * <AUTHOR>
 * @description SpecialProjectService
 * @date 2023/2/14 14:57
 */
public interface SpecialProjectService {

    /**
     * @description 新增特殊立项
     *
     * @date 2023/02/14 15:25
     * @param createSpecialProjectReq createSpecialProjectReq
     * @return Boolean boolean
     * <AUTHOR>
     */
    Boolean create(CreateSpecialProjectReq createSpecialProjectReq);
    
    /**
     * @description 编辑更新特殊立项
     *
     * @date 2023/02/14 15:29
     * @param updateSpecialProjectReq updateSpecialProjectReq
     * @return Boolean boolean
     * <AUTHOR>
     */
    Boolean update(UpdateSpecialProjectReq updateSpecialProjectReq);
    
    /**
     * @description 查询特殊立项详情
     *
     * @date 2023/02/14 15:36
     * @param id  项目id
     * @return SpecialProjectDetailResp
     * <AUTHOR>
     */
    SpecialProjectDetailResp getDetail(Long id);

    /**
     *
     * @date 2023/02/14 15:51
     * @param params 查询参数
     * @return Page<SpecialProjectResp>
     * <AUTHOR>
     */
    Page<SpecialProjectResp> specialProjectList(QuerySpecialProjectParams params);


    /**
     * 特殊立项列表-切换云枢执行单位
     *
     * @param params 查询参数
     * @return 特殊立项列表
     * <AUTHOR>
     * @Date 2023/8/23
     */
    Page<SpecialProjectResp> specialProjectListCloudPivot(QuerySpecialProjectParams params);
}
