package com.cscec3b.iti.projectmanagement.server.mapper;

import com.baomidou.mybatisplus.core.mapper.BaseMapper;
import com.cscec3b.iti.projectmanagement.api.dto.request.ContractFilePageReq;
import com.cscec3b.iti.projectmanagement.api.dto.response.ContractFilePageResp;
import com.cscec3b.iti.projectmanagement.api.dto.response.project.SupplementaryAgreementResp;
import com.cscec3b.iti.projectmanagement.server.entity.SupplementaryAgreement;
import com.cscec3b.iti.projectmanagement.server.enums.IndContractsTypeEnum;
import org.apache.ibatis.annotations.Mapper;
import org.apache.ibatis.annotations.Param;

import java.util.List;

/**
 * @Description SupplementaryAgreementMapper
 *
 * <AUTHOR>
 * @Date 2022/10/19 10:11
 */
@Mapper
public interface SupplementaryAgreementMapper extends BaseMapper<SupplementaryAgreement> {
    /**
     * 创建
     *
     * @param supplementaryAgreement supplementaryAgreement
     * @return int
     */
    int createSupplementaryAgreement(@Param("vo") SupplementaryAgreement supplementaryAgreement);
    
    /**
     * 通过belongid查询详情
     * @param belongId  belongId
     * @return supplementaryAgreement
     */
    SupplementaryAgreement qrySupplementaryAgreementByBelongId(Long belongId);
    
    /**
     * 获取补充协议记录表记录
     * @param id id
     * @return supplementaryAgreementResp
     */
    SupplementaryAgreementResp getSupplementaryAgreementDetail(Long id);
    
    /**
     * 按照originFileId删除补充协议
     * @param originFileId 原始id
     * @return int
     */
    int deleteByOriginFileId(@Param("originFileId") Long originFileId);


    /**
     * 分页查询文件列表
     *
     * @param pageReq       分页查询参数
     * @param scopeTypeEnum 合同文件类型
     * @return {@link List}<{@link ContractFilePageResp}>
     */
    List<ContractFilePageResp> pageList(@Param("req") ContractFilePageReq pageReq,
            @Param("scopeTypeEnum") IndContractsTypeEnum scopeTypeEnum);

}
