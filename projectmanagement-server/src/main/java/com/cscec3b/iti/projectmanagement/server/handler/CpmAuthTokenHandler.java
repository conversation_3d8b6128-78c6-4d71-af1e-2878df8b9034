package com.cscec3b.iti.projectmanagement.server.handler;

import cn.hutool.core.exceptions.ValidateException;
import cn.hutool.core.util.CharsetUtil;
import cn.hutool.jwt.JWT;
import cn.hutool.jwt.JWTException;
import cn.hutool.jwt.RegisteredPayload;
import com.cscec3b.iti.common.base.api.ResponseBuilder;
import com.cscec3b.iti.common.base.json.JsonUtils;
import com.cscec3b.iti.common.web.exception.FrameworkException;
import com.cscec3b.iti.projectmanagement.api.dto.dto.xindun.UserInfo;
import com.cscec3b.iti.projectmanagement.server.config.CommonDataCache;
import com.cscec3b.iti.projectmanagement.server.config.ExcludePath;
import com.cscec3b.iti.projectmanagement.server.config.PmExcludePathProperties;
import com.cscec3b.iti.projectmanagement.server.config.UserContextHolder;
import com.cscec3b.iti.projectmanagement.server.constant.Constants;
import com.cscec3b.iti.projectmanagement.server.service.IAuthService;
import com.g3.security.G3LoginUser;
import com.g3.security.G3UserContext;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.collections4.CollectionUtils;
import org.apache.commons.lang3.StringUtils;
import org.apache.http.entity.ContentType;
import org.apache.shenyu.client.springmvc.annotation.ShenyuSpringMvcClient;
import org.springframework.data.redis.core.RedisTemplate;
import org.springframework.http.HttpStatus;
import org.springframework.util.AntPathMatcher;
import org.springframework.web.method.HandlerMethod;
import org.springframework.web.servlet.HandlerInterceptor;

import javax.servlet.http.HttpServletRequest;
import javax.servlet.http.HttpServletResponse;
import java.io.IOException;
import java.lang.reflect.Method;
import java.util.List;
import java.util.Objects;
import java.util.concurrent.TimeUnit;

/**
 * 认证拦截器
 *
 * <AUTHOR>
 * @date 2023/10/23 14:01
 **/

@Slf4j
public class CpmAuthTokenHandler implements HandlerInterceptor {

    private static final String REQUEST = "REQUEST";

    private static final String ADMINISTRATIVE_REGION_URI = "/**/common-data/administrative-region";

    private final AntPathMatcher antPathMatcher = new AntPathMatcher();


    private final PmExcludePathProperties excludePathProperties;
    private final IAuthService authService;
    private final RedisTemplate<String, UserInfo> redisTemplate;
    private final CommonDataCache commonDataCache;
    private final G3UserContext g3UserContext;


    public CpmAuthTokenHandler(PmExcludePathProperties excludePathProperties, IAuthService authService,
            RedisTemplate<String, UserInfo> redisTemplate, CommonDataCache commonDataCache,
            G3UserContext g3UserContext) {
        this.excludePathProperties = excludePathProperties;
        this.authService = authService;
        this.redisTemplate = redisTemplate;
        this.commonDataCache = commonDataCache;
        this.g3UserContext = g3UserContext;
    }


    @Override
    public boolean preHandle(HttpServletRequest request, HttpServletResponse response, Object handler) throws Exception {

        response.setCharacterEncoding(CharsetUtil.UTF_8);
        String requestMethod = request.getMethod();
        final String requestUri = request.getRequestURI();
        log.debug("enter the token interceptor : {} {}", requestMethod, requestUri);

        // 匹配获取地区缓存接口，直接从内存中返回
        if (antPathMatcher.match(ADMINISTRATIVE_REGION_URI, requestUri)) {
            final Object data = commonDataCache.getCommonDataByUrl("/common/administrative_region");
            // 如果内存中未获取到，则走之前逻辑从redis中获取
            if (Objects.nonNull(data)) {
                response.setStatus(HttpStatus.OK.value());
                writeResponse(response, data);
                return false;
            }
        }

        // 放行自定义白名单(application.yml 文件中配置)
        if (isExcludeRequest(requestUri, requestMethod)) {
            log.debug("this api is custom exclude path");
            return true;
        }

        // 不是request类型目前不处理; 不是handlerMethod类型直接跳过
        if (!isRequestAndHandlerMethod(request, handler)) {
            log.debug("this api is not request and handlerMethod, excluded");
            return true;
        }

        // 放行有 @ShenyuSpringMvcClient 注解的接口, 已由神禹前置处理
        if (isShenYuRequest(handler)) {
            log.debug("this api from shenYu, excluded");
            log.info("shenYu app key: {}", request.getHeader("appKey"));
            return true;
        }

        // 获取token
        final String authorization = request.getHeader(Constants.AUTHORIZATION);
        // token 为空
        if (StringUtils.isBlank(authorization)) {
            response.setStatus(HttpStatus.UNAUTHORIZED.value());
            log.error("no token, return {}", HttpStatus.UNAUTHORIZED.value());
            return false;
        }
        // 从前置过滤器取出用户信息
        final G3LoginUser loginUser = g3UserContext.getLoginUser();
        // 使用原有逻辑获取用户信息
        UserInfo userInfo = null;
        JWT jwt = null;

        // 校验token 允许误差10s
        try {
            String jwtToken = authorization.replace(Constants.BEARER, "").replace(Constants.BEARER_CAME_CASE, "");
            // 解析jwt token
            jwt = JWT.of(jwtToken);
            // JWTValidator.of(jwt).validateDate(DateUtil.date(), 10L);
        } catch (JWTException | ValidateException e) {
            response.setStatus(HttpStatus.UNAUTHORIZED.value());
            log.error("The token verification fail, return {}", HttpStatus.UNAUTHORIZED.value());
            return false;

        }

        try {
            String jti = String.valueOf(jwt.getPayload(RegisteredPayload.JWT_ID));
            userInfo = getUserInfo(jti, loginUser.getExpired().intValue());
            // 不存在或者组织变动，都要重新从远程拉取
            if (userInfo == null || !userInfo.getOrgId().equals(loginUser.getOrgId())) {
                log.info("===local login user: {}", loginUser);
                userInfo = getRemoteUser(jti, loginUser.getExpired().intValue());
                log.info("===remote login user: {}", loginUser);
                if (Objects.isNull(userInfo)) {
                    response.setStatus(HttpStatus.UNAUTHORIZED.value());
                    log.error(" loginUser is null  {}", HttpStatus.UNAUTHORIZED.value());
                    return false;
                }
            }
        } catch (FrameworkException e) {
            response.setStatus(HttpStatus.UNAUTHORIZED.value());
            log.error("get userinfo error : {}-{}", e.getStatus(), e.getMessage());
            log.error("获取用户信息异常: ", e);
            return false;
        } catch (Exception e) {
            response.setStatus(HttpStatus.UNAUTHORIZED.value());
            log.error("get userinfo error : ", e);
            log.error("获取用户信息异常: ", e);
            return false;
        }

        // 刷新上下文
        saveUserContext(userInfo);


        return true;
    }

    /**
     * 匹配获取用户信息的接口 “/**\\/v2/auth/userinfo”，直接返回用户信息
     *
     * @param response response
     * @param obj Object
     * <AUTHOR>
     * @date 2023/10/26
     */
    private void writeResponse(HttpServletResponse response, Object obj) throws IOException {
        response.setContentType(ContentType.APPLICATION_JSON.toString());
        response.getWriter().write(JsonUtils.toJsonStr(ResponseBuilder.fromData(obj)));
    }

    /**
     * 从dop-auth获取用户信息
     *
     * @param jti jwt id
     * @param expired 过期时间
     * @return {@link UserInfo}
     * <AUTHOR>
     * @date 2023/10/23
     */
    private UserInfo getUserInfo(String jti, int expired) {
        UserInfo userInfo = redisTemplate.opsForValue().get(jti);
        if (Objects.isNull(userInfo)) {
            userInfo = getRemoteUser(jti, expired);
        }
        return userInfo;
    }


    /**
     * 从远程服务器dop-auth获取用户信息
     *
     * @param jti     jwt id
     * @param expired 过期时间
     * @return {@link UserInfo }
     */
    private UserInfo getRemoteUser(String jti, int expired) {
        UserInfo userInfo = authService.getUserInfo();
        redisTemplate.opsForValue().set(jti, userInfo, expired, TimeUnit.SECONDS);
        return userInfo;
    }


    private void saveUserContext(UserInfo userInfo) {
        // 放入本地线程
        UserContextHolder.setUser(userInfo);
    }


    @Override
    public void afterCompletion(HttpServletRequest request, HttpServletResponse response, Object handler,
                                Exception ex) throws Exception {
        UserContextHolder.remove();
        // afterCompletion(request, response, handler, ex);
    }

    /**
     * 白名单
     *
     * @param requestUri
     * @param requestMethod
     * @return
     */
    private boolean isExcludeRequest(String requestUri, String requestMethod) {
        List<ExcludePath> excludePaths = excludePathProperties.getExcludePaths();
        if (CollectionUtils.isNotEmpty(excludePaths)) {
            for (ExcludePath excludePath : excludePaths) {
                if (antPathMatcher.match(excludePath.getPath(), requestUri)
                        && (StringUtils.isBlank(excludePath.getHttpMethod())
                        || requestMethod.equalsIgnoreCase(excludePath.getHttpMethod()))) {
                    return true;
                }
            }
        }
        return false;
    }

    /**
     * 是否request类型且handlerMethod类型
     *
     * @param request httpServletRequest
     * @param handler handler
     * @return boolean
     * <AUTHOR>
     * @date 2023/10/23
     */
    private boolean isRequestAndHandlerMethod(HttpServletRequest request, Object handler) {
        return REQUEST.equalsIgnoreCase(request.getDispatcherType().name()) && handler instanceof HandlerMethod;
    }

    /**
     * 判断是否神禹请求
     *
     * @param handler
     * @return
     */
    private boolean isShenYuRequest(Object handler) {
        Method method = ((HandlerMethod) handler).getMethod();
        return method.isAnnotationPresent(ShenyuSpringMvcClient.class);
    }
}
