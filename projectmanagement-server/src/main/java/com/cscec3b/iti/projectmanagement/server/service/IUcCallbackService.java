package com.cscec3b.iti.projectmanagement.server.service;


import com.cscec3b.iti.projectmanagement.api.dto.request.uc.G3OrgEventCallbackReq;

import javax.servlet.http.HttpServletRequest;

/**
 * UC回调服务接口
 *
 * <AUTHOR>
 * @date 2024/07/30
 */
public interface IUcCallbackService {

    /**
     * 组织机构事件回调
     *
     * @param request     request
     * @param callbackReq 回调请求
     */
    void ucOrgCallback(HttpServletRequest request, G3OrgEventCallbackReq callbackReq) throws Exception;

    /**
     * 组织机构删除事件回调
     *
     * @param request  request
     * @param id       组织机构treeid
     * @param mainData 删除是否是主数据
     */
    void ucOrgDeleteCallback(HttpServletRequest request, String id, Boolean mainData);
}
