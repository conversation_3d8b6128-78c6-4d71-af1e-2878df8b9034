package com.cscec3b.iti.projectmanagement.server.converter.mapstruct;

import com.cscec3b.iti.projectmanagement.api.dto.request.changehistory.ProjectChangeHistoryDto;
import com.cscec3b.iti.projectmanagement.api.dto.response.changehistory.ProjectChangeHistoryResp;
import com.cscec3b.iti.projectmanagement.server.entity.ProjectChangeHistory;
import org.mapstruct.Mapper;
import org.mapstruct.MappingConstants.ComponentModel;

@Mapper(componentModel = ComponentModel.SPRING)
public interface ProjectChangeHistoryConverter extends IConverter<ProjectChangeHistoryDto, ProjectChangeHistoryResp,
        ProjectChangeHistory> {
}
