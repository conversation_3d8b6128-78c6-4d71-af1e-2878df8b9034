<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.cscec3b.iti.projectmanagement.server.mapper.IdTableMapper">
    <resultMap id="BaseResultMap" type="com.cscec3b.iti.projectmanagement.server.entity.IdTable">
        <!--@mbg.generated-->
        <!--@Table id_table-->
        <id column="prefix" jdbcType="VARCHAR" property="prefix"/>
        <result column="cur_date" jdbcType="VARCHAR" property="curDate"/>
        <result column="seq" jdbcType="BIGINT" property="seq"/>
    </resultMap>
    <sql id="Base_Column_List">
        <!--@mbg.generated-->
        `prefix`, cur_date, seq
    </sql>

    <select id="getIncrementId" resultType="java.lang.Long">
        select
        seq
        from id_table
        <where>
            `prefix` = #{prefix,jdbcType=VARCHAR}
            <choose>
                <when test="curDate != null and curDate != ''">
                    and cur_date = #{curDate,jdbcType=VARCHAR}
                </when>
                <otherwise>
                    and cur_date = date_format(now(), '%Y%m%d')
                </otherwise>
            </choose>
        </where>
        for update
    </select>

    <update id="updateIncrementId">
        update id_table
        set seq = seq + 1
        where `prefix` = #{prefix,jdbcType=VARCHAR}
        <choose>
            <when test="curDate != null and curDate != ''">
                and cur_date = #{curDate,jdbcType=VARCHAR}
            </when>
            <otherwise>
                and cur_date = date_format(now(), '%Y%m%d')
            </otherwise>
        </choose>
    </update>

    <insert id="insertOrUpdateIncrementId">
        insert into id_table (`prefix`, cur_date)
        values (#{prefix,jdbcType=VARCHAR}, date_format(now(), '%Y%m%d'))
        ON DUPLICATE KEY UPDATE `prefix`= #{prefix,jdbcType=VARCHAR},
                                cur_date = date_format(now(), '%Y%m%d'),
                                seq = seq + 1;
    </insert>
</mapper>