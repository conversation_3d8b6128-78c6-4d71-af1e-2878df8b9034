<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.cscec3b.iti.projectmanagement.server.mapper.EventBusinessColumnMapper">
    <resultMap id="DataBaseColumnMap"
               type="com.cscec3b.iti.projectmanagement.api.dto.dto.EventBusinessColumn">
        <result column="TABLE_NAME" property="table"/>
        <collection property="columns" ofType="com.cscec3b.iti.projectmanagement.api.dto.dto.ColumnDetail">
            <result column="COLUMN_NAME" property="column"/>
            <result column="DATA_TYPE" property="type"/>
            <result column="COLUMN_COMMENT" property="desc"/>
        </collection>
    </resultMap>

    <select id="databaseColumns" resultMap="DataBaseColumnMap" parameterType="java.util.Set">
        SELECT TABLE_NAME,
        COLUMN_NAME,
        DATA_TYPE,
        COLUMN_COMMENT
        FROM information_schema.columns
        where TABLE_SCHEMA = (select database())
        and table_name in
        <foreach collection="tables" item="table" open="(" separator="," close=")">
            #{table}
        </foreach>
    </select>

    <select id="getProjectInfo" resultType="java.util.Map">
        select
        <foreach collection="columns" item="column" separator=",">
            ${column}
        </foreach>
        from project
        where id = #{id}
    </select>

    <select id="getBureauContractInfo" resultType="java.util.Map">
        select
        <foreach collection="columns" item="column" separator=",">
            bc.${column}
        </foreach>
        from project p
        left join bureau_contract bc on bc.independent_contract_id = p.independent_contract_id
        where p.id = #{id}
    </select>

    <select id="getBureauSupplementaryAgreementInfo" resultType="java.util.Map">
        select
        <foreach collection="columns" item="column" separator=",">
            bsa.${column}
        </foreach>
        from project p
        left join bureau_supplementary_agreement bsa on bsa.independent_contract_id = p.independent_contract_id
        where p.id = #{id}
    </select>

    <select id="getBidSummaryInfo" resultType="java.util.Map">
        select
        <foreach collection="columns" item="column" separator=",">
            bs.${column}
        </foreach>
        from project p
        left join bid_summary bs on bs.independent_contract_id = p.independent_contract_id
        where p.id = #{id}
    </select>

    <select id="getSupplementaryAgreementInfo" resultType="java.util.Map">
        select
        <foreach collection="columns" item="column" separator=",">
            sa.${column}
        </foreach>
        from project p
        left join supplementary_agreement sa on sa.independent_contract_id = p.independent_contract_id
        where p.id = #{id}
    </select>

    <select id="getContractInfo" resultType="java.util.Map">
        select
        <foreach collection="columns" item="column" separator=",">
            c.${column}
        </foreach>
        from project p
        left join contract c on c.independent_contract_id = p.independent_contract_id
        where p.id = #{id}
    </select>

    <select id="getBidOpeningRecordsInfo" resultType="java.util.Map">
        select
        <foreach collection="columns" item="column" separator=",">
            bor.${column}
        </foreach>
        from project p
        left join bid_summary bs on bs.independent_contract_id = p.independent_contract_id
        left join bid_opening_records bor on bs.id = bor.relation_id
        where p.id = #{id}
    </select>
</mapper>
