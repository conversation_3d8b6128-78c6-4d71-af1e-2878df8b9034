<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.cscec3b.iti.projectmanagement.server.mapper.SecrecyProjectInfoMapper">
  <resultMap id="BaseResultMap" type="com.cscec3b.iti.projectmanagement.server.entity.SecrecyProjectInfo">
    <!--@mbg.generated-->
    <!--@Table secrecy_project_info-->
    <id column="id" jdbcType="BIGINT" property="id" />
    <result column="project_level" jdbcType="VARCHAR" property="projectLevel" />
    <result column="contact_person" jdbcType="VARCHAR" property="contactPerson" />
    <result column="contact_person_mobile" jdbcType="VARCHAR" property="contactPersonMobile" />
    <result column="region" jdbcType="VARCHAR" property="region" />
    <result column="project_address" jdbcType="VARCHAR" property="projectAddress" />
    <result column="investors" jdbcType="VARCHAR" property="investors" />
    <result column="contract_amount" jdbcType="DECIMAL" property="contractAmount" />
    <result column="designer" jdbcType="VARCHAR" property="designer" />
    <result column="supervisor" jdbcType="VARCHAR" property="supervisor" />
    <result column="project_manager" jdbcType="VARCHAR" property="projectManager" />
    <result column="count_days" jdbcType="INTEGER" property="countDays" />
    <result column="customer_name" jdbcType="VARCHAR" property="customerName" />
    <result column="superior_company_name" jdbcType="VARCHAR" property="superiorCompanyName" />
    <result column="signed_subject_value" jdbcType="VARCHAR" property="signedSubjectValue" />
    <result column="signed_subject_code" jdbcType="VARCHAR" property="signedSubjectCode"/>
    <result column="do_unit" jdbcType="VARCHAR" property="doUnit" />
    <result column="total_amount" jdbcType="DECIMAL" property="totalAmount" />
    <result column="no_tax_included_money" jdbcType="DECIMAL" property="noTaxIncludedMoney" />
    <result column="mid_amount_self" jdbcType="DECIMAL" property="midAmountSelf" />
    <result column="self_civil_amount" jdbcType="DECIMAL" property="selfCivilAmount" />
    <result column="self_install_amount" jdbcType="DECIMAL" property="selfInstallAmount" />
    <result column="self_steel_structure_amount" jdbcType="DECIMAL" property="selfSteelStructureAmount" />
    <result column="self_total_service_amount" jdbcType="DECIMAL" property="selfTotalServiceAmount" />
    <result column="self_other_amount" jdbcType="DECIMAL" property="selfOtherAmount" />
    <result column="project_tax_amount" jdbcType="DECIMAL" property="projectTaxAmount" />
    <result column="subcontract_amount" jdbcType="DECIMAL" property="subcontractAmount" />
    <result column="worker_reward_punish_appoint" jdbcType="VARCHAR" property="workerRewardPunishAppoint" />
    <result column="contract_scope" jdbcType="LONGVARCHAR" property="contractScope" />
    <result column="issuer_project" jdbcType="LONGVARCHAR" property="issuerProject" />
    <result column="project_desc" jdbcType="VARCHAR" property="projectDesc" />
    <result column="yunshu_execute_unit" jdbcType="VARCHAR" property="yunshuExecuteUnit" />
    <result column="yunshu_execute_unit_code" jdbcType="VARCHAR" property="yunshuExecuteUnitCode" />
    <result column="yunshu_execute_unit_id" jdbcType="VARCHAR" property="yunshuExecuteUnitId" />
    <result column="yunshu_execute_unit_id_path" jdbcType="LONGVARCHAR" property="yunshuExecuteUnitIdPath" />
    <result column="yunshu_execute_unit_abbreviation" jdbcType="VARCHAR" property="yunshuExecuteUnitAbbreviation" />
    <result column="effect_pic" jdbcType="VARCHAR" property="effectPic" />
    <result column="belong_file_type" jdbcType="TINYINT" property="belongFileType" />
    <result column="create_at" jdbcType="BIGINT" property="createAt" />
    <result column="update_at" jdbcType="BIGINT" property="updateAt" />
    <result column="standard_type_code_path" jdbcType="VARCHAR" property="standardTypeCodePath" />
    <result column="business_segment_code_path" jdbcType="VARCHAR" property="businessSegmentCodePath" />
    <result column="project_name" jdbcType="VARCHAR" property="projectName" />
    <result column="belong_id" jdbcType="BIGINT" property="belongId" />
    <result column="is_independent" jdbcType="VARCHAR" property="independent" />
    <result column="independent_contract_id" jdbcType="BIGINT" property="independentContractId" />
    <result column="independent_contract_type" jdbcType="INTEGER" property="independentContractType" />
    <result column="file_code" jdbcType="VARCHAR" property="fileCode" />
    <result column="successful_time" jdbcType="BIGINT" property="successfulTime" />
    <result column="actual_signed_time" jdbcType="BIGINT" property="actualSignedTime" />
    <result column="real_enter_time" jdbcType="BIGINT" property="realEnterTime" />
    <result column="work_end_time" jdbcType="BIGINT" property="workEndTime" />
    <result column="worker_begin_time" jdbcType="BIGINT" property="workerBeginTime" />
    <result column="worker_end_time" jdbcType="BIGINT" property="workerEndTime" />
    <result column="real_work_begin_time" jdbcType="BIGINT" property="realWorkBeginTime" />
    <result column="predict_work_end_time" jdbcType="BIGINT" property="predictWorkEndTime" />
    <result column="region_id_path" jdbcType="BIGINT" property="regionIdPath" />
    <result column="approval_status" jdbcType="INTEGER" property="approvalStatus" />
    <result column="approval_begin_time" jdbcType="BIGINT" property="approvalBeginTime" />
    <result column="standard_type" jdbcType="VARCHAR" property="standardType" />
    <result column="financial_business_segment" jdbcType="BIGINT" property="financialBusinessSegment" />
    <result column="update_by" jdbcType="VARCHAR" property="updateBy" />
    <result column="create_by" jdbcType="VARCHAR" property="createBy" />
  </resultMap>
  <sql id="Base_Column_List">
    <!--@mbg.generated-->
    id, project_level, contact_person, contact_person_mobile, region, project_address,
    investors, contract_amount, designer, supervisor, project_manager, count_days, customer_name,
    superior_company_name, signed_subject_value, signed_subject_code, do_unit, total_amount, no_tax_included_money,
    mid_amount_self, self_civil_amount, self_install_amount, self_steel_structure_amount,
    self_total_service_amount, self_other_amount, project_tax_amount, subcontract_amount,
    worker_reward_punish_appoint, contract_scope, issuer_project, project_desc, yunshu_execute_unit,
    yunshu_execute_unit_code, yunshu_execute_unit_id, yunshu_execute_unit_id_path, yunshu_execute_unit_abbreviation,
    effect_pic, belong_file_type, create_at, update_at, standard_type_code_path, business_segment_code_path,
    project_name, belong_id, is_independent, independent_contract_id, independent_contract_type,
    file_code, successful_time, actual_signed_time, real_enter_time, work_end_time, worker_begin_time,
    worker_end_time, real_work_begin_time, predict_work_end_time,region_id_path, approval_status, approval_begin_time,
    standard_type, financial_business_segment, create_by, update_by
  </sql>

  <select id="pageList" resultType="com.cscec3b.iti.projectmanagement.api.dto.response.ContractFilePageResp">
    select t.id, t.belong_id ,
    #{scopeTypeEnum.dictCode} as scopeType, #{scopeTypeEnum.zhCN} as scopeTypeName,
    t.project_name as projectName,
    file_code as contractFileCode,
    null as pre_file_id,
    null as pre_file_type,
    null as preProjectName,
    null as preFileKeyId,
    null as  preFileBelongId,
    t1.id as cpmProjectId,
    t1.cpm_project_key,
    t1.cpm_project_name,
    t1.cpm_project_abbreviation,
    t1.source_system,
    t.create_at,
    t.update_at,
    t.approval_status,
    t.approval_begin_time
    from secrecy_project_info t
    left join project t1 on t.independent_contract_id = t1.independent_contract_id
    <where>
      <if test="req.projectName != null and req.projectName != ''">
        and t.project_name like concat('%', #{req.projectName}, '%')
      </if>
      <if test="req.yunshueExecuteUnitQueryCode != null and req.yunshueExecuteUnitQueryCode != ''">
        and t.yunshu_execute_unit_id_path like concat('%', #{req.yunshueExecuteUnitQueryCode}, '%')
      </if>
      <if test="req.relativeProject != null and req.relativeProject == 1">
        and t1.id is not null
        <if test="req.cpmProjectKey != null and req.cpmProjectKey != ''">
          and t1.cpm_project_key like concat('%', #{req.cpmProjectKey}, '%')
        </if>
        <if test="req.cpmProjectName != null and req.cpmProjectName != ''">
          and (t1. project_name like concat('%', #{req.cpmProjectName}, '%')
          or t1.project_finance_name like concat('%', #{req.cpmProjectName}, '%'))
        </if>
      </if>
      <if test="req.relativeProject != null and req.relativeProject == 0">
        and t1.id is null
      </if>
      <if test="req.contractFileCode != null and req.contractFileCode != ''">
        and t.file_code like concat('%', #{req.contractFileCode},'%')
      </if>
      <if test="req.approvalStatus != null">
        and t.approval_status = #{req.approvalStatus}
      </if>
    </where>
    order by t.create_at desc
  </select>

</mapper>