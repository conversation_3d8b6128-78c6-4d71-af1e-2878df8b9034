<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.cscec3b.iti.projectmanagement.server.mapper.EngineeringProjectMapper">
    <resultMap id="BaseResultMap" type="com.cscec3b.iti.projectmanagement.server.entity.EngineeringProject">
        <!--@mbg.generated-->
        <!--@Table engineering_project-->
        <id column="id" jdbcType="BIGINT" property="id"/>
        <result column="engineering_key" jdbcType="VARCHAR" property="engineeringKey"/>
        <result column="engineering_name" jdbcType="VARCHAR" property="engineeringName"/>
        <result column="parent_id" jdbcType="BIGINT" property="parentId"/>
        <result column="main_project_id" jdbcType="BIGINT" property="mainProjectId"/>
        <result column="mapping_execute_unit_id" jdbcType="VARCHAR" property="mappingExecuteUnitId"/>
        <result column="execute_unit_id" jdbcType="VARCHAR" property="executeUnitId"/>
        <result column="project_dept_id" jdbcType="VARCHAR" property="projectDeptId"/>
        <result column="is_history" jdbcType="BOOLEAN" property="history"/>
        <result column="contact_user" jdbcType="VARCHAR" property="contactUser"/>
        <result column="contact_user_id" jdbcType="VARCHAR" property="contactUserId"/>
        <result column="create_by" jdbcType="VARCHAR" property="createBy"/>
        <result column="create_at" jdbcType="BIGINT" property="createAt"/>
        <result column="update_at" jdbcType="BIGINT" property="updateAt"/>
        <result column="update_by" jdbcType="VARCHAR" property="updateBy"/>
        <result column="deleted" jdbcType="BIGINT" property="deleted"/>
    </resultMap>
    <sql id="Base_Column_List">
        <!--@mbg.generated-->
        id, engineering_key, engineering_name, parent_id, main_project_id, mapping_execute_unit_id, project_dept_id,
        is_history, execute_unit_id, contact_user, contact_user_id, create_by, create_at, update_at, update_by, deleted
    </sql>

    <select id="getList"
            resultType="com.cscec3b.iti.projectmanagement.api.dto.response.engineeringproject.EngineerProjectPageResp">
        select e.id, e.engineering_key, e.engineering_name, e.engineering_code, e.main_project_id, e.project_dept_id,
        e.mapping_execute_unit_id as execute_unit_id, yos.name execute_unit_name, yos.id_path_name
        execute_unit_id_path_name,
        p.id mainProjectId,
        p.cpm_project_key,
        p.cpm_project_name ,
        p.project_finance_code, p.yunshu_org_id yunshuOrgId
        from engineering_project e
        left join yunshu_org_sync yos on e.mapping_execute_unit_id = yos.dept_id
        left join engineering_standard_project_mapping epm on e.id = epm.engineering_project_id and epm.is_main =
        true and epm.deleted = 0
        left join project p on p.id = epm.standard_project_id
        where e.deleted = 0
        and yos.id_path like concat(#{req.executeUnitIdPath}, '%')
        <if test="req.projectDeptIdIsNull">
            and (e.project_dept_id is null or e.project_dept_id = '')
        </if>
        <if test="req.history">
            and e.is_history = 1
        </if>
        <if test="req.mainProjectId != null">
            and e.main_project_id = #{req.mainProjectId}
        </if>
        <if test="req.engineeringName != null and req.engineeringName != ''">
            and e.engineering_name like concat('%', #{req.engineeringName}, '%')
        </if>
        <if test="req.engineeringKey != null and req.engineeringKey != ''">
            and e.engineering_key = #{req.engineeringKey}
        </if>
        <if test="req.engineeringCode != null and req.engineeringCode != ''">
            and e.engineering_code = #{req.engineeringCode}
        </if>
        <if test="req.mainCpmProjectKey != null and req.mainCpmProjectKey != ''">
            and p.cpm_project_key = #{req.mainCpmProjectKey}
        </if>
        <if test="req.mainCpmProjectName != null and req.mainCpmProjectName != ''">
            and p.cpm_project_name like concat('%', #{req.mainCpmProjectName}, '%')
        </if>
        <if test="req.mainProjectFinanceCode != null and req.mainProjectFinanceCode != ''">
            and p.project_finance_code = #{req.mainProjectFinanceCode}
        </if>
        <if test="req.mainProjectYunshuOrgId != null and req.mainProjectYunshuOrgId != ''">
            and p.yunshu_org_id = #{req.mainProjectYunshuOrgId}
        </if>
        order by e.update_at desc
    </select>

    <resultMap id="MappingStandardProjectRespResultMap"
               type="com.cscec3b.iti.projectmanagement.api.dto.response.engineeringproject.MappingStandardProjectResp">
        <id column="id" property="id"/>
        <result column="engineering_key" property="engineeringKey"/>
        <result column="engineering_name" property="engineeringName"/>
        <result column="engineering_code" property="engineeringCode"/>
        <result column="main_project_id" property="mainProjectId"/>
        <result column="mapping_execute_unit_id" property="executeUnitId"/>
        <result column="project_dept_id" property="projectDeptId"/>
        <result column="execute_unit_name" property="executeUnitName"/>
        <result column="execute_unit_id_path" property="executeUnitIdPath"/>
        <result column="execute_unit_abbreviation" property="executeUnitAbbreviation"/>
        <collection property="projectRespList"
                    ofType="com.cscec3b.iti.projectmanagement.api.dto.response.engineeringproject.StandardProjectResp">
            <id column="standard_project_id" property="id"/>
            <result column="cpm_project_key" property="cpmProjectKey"/>
            <result column="cpm_project_name" property="cpmProjectName"/>
            <result column="project_finance_code" property="projectFinanceCode"/>
            <result column="yunshu_org_id" property="yunshuOrgId"/>
            <result column="is_main" property="mainProject"/>
        </collection>
    </resultMap>

    <select id="getStandardProjectByEngineeringId"
            resultMap="MappingStandardProjectRespResultMap">
        select e.*,
               yos.name    execute_unit_name,
               yos.id_path execute_unit_id_path,
               yos.abbreviation
                           execute_unit_abbreviation,
               p.id as     standard_project_id,
               p.cpm_project_key,
               p.cpm_project_name,
               p.project_finance_code,
               p.yunshu_org_id,
               epm.is_main
        from engineering_project e
                 left join yunshu_org_sync yos on e.mapping_execute_unit_id = yos.dept_id
        left join engineering_standard_project_mapping epm on e.id = epm.engineering_project_id
        left join project p on p.id = epm.standard_project_id
        where e.deleted = 0
          and epm.deleted = 0
        and e.id = #{engineeringId}
        order by e.update_at desc
    </select>

    <!-- 新增递归查询方法 -->
    <select id="getAllDescendantIds" resultType="java.lang.String">
        SELECT _ids
        FROM (SELECT @ids                                 AS _ids,
                     (SELECT @ids := GROUP_CONCAT(id)
                      FROM engineering_project
                      WHERE FIND_IN_SET(parent_id, @ids)) and deleted = 0 AS cids
              FROM engineering_project,
                   (SELECT @ids := #{currentId}) vars
              WHERE @ids IS NOT NULL) temp1
    </select>

    <select id="getAllAncestorIds" resultType="java.lang.Long">
        SELECT _id as id
        FROM (SELECT @current_id              AS _id,
                     (SELECT @current_id := parent_id
                      FROM engineering_project
                      WHERE id = @current_id
                        and deleted = 0
                      LIMIT 1) AS pid
              FROM engineering_project,
                   (SELECT @current_id := #{currentId}) vars
              WHERE @current_id > 0) temp2
        WHERE _id != #{currentId}
    </select>

    <!-- 获取当前工程项目所在全量树的所有ID -->
    <select id="getFullTreeIds" resultType="java.lang.Long">
        -- 获取当前节点
        SELECT id
        FROM engineering_project
        WHERE engineering_key = #{engineerKey}
        AND deleted = 0

        UNION

        -- 获取所有祖先节点
        SELECT p.id
        FROM (SELECT @r AS _id,
        (SELECT @r := parent_id FROM engineering_project WHERE id = _id) AS parent_id
        FROM (SELECT @r := (SELECT id
        FROM engineering_project
        WHERE engineering_key = #{engineerKey}
        AND deleted = 0)) vars,
        engineering_project
        WHERE @r != 0) ancestors
        JOIN engineering_project p ON ancestors._id = p.id
        WHERE p.deleted = 0

        UNION

        -- 获取所有后代节点
        SELECT e.id
        FROM engineering_project e
        JOIN (SELECT id
        FROM engineering_project
        WHERE (parent_id = (SELECT id
        FROM engineering_project
        WHERE engineering_key = #{engineerKey}
        AND deleted = 0)
        OR id IN (SELECT e1.id
        FROM engineering_project e1
        WHERE e1.parent_id IN (SELECT e2.id
        FROM engineering_project e2
        WHERE e2.parent_id = (SELECT id
        FROM engineering_project
        WHERE engineering_key = #{engineerKey}
        AND deleted = 0))))
        AND deleted = 0) descendants ON e.id = descendants.id
        WHERE e.deleted = 0
    </select>

    <!-- 修改挂接列表查询 -->
    <select id="getHookList"
            resultType="com.cscec3b.iti.projectmanagement.api.dto.response.engineeringproject.EngineerProjectPageResp">
        select e.*, p.cpm_project_name, p.cpm_project_key, p.project_finance_code, p.yunshu_org_id
        from engineering_project e
        left join engineering_standard_project_mapping epm on e.id = epm.engineering_project_id and epm.is_main =
        true and epm.deleted = 0
        left join project p on p.id = epm.standard_project_id
        where e.deleted = 0
        <if test="excludeIds != null and excludeIds.size() > 0">
            AND e.id NOT IN
            <foreach collection="excludeIds" item="id" open="(" separator="," close=")">
                #{id}
            </foreach>
        </if>
        <if test="hookLevel ==1">
            and e.parent_id = 0
        </if>
        <if test="searchKeyWord != null and searchKeyWord != ''">
            and ( e.engineering_key = #{searchKeyWord}
            or e.engineering_name like concat('%', #{searchKeyWord}, '%')
            or e.engineering_code like concat('%', #{searchKeyWord}, '%')
            or p.cpm_project_name like concat('%' #{searchKeyWord}, '%')
            or p.project_finance_code = #{searchKeyWord}
            or p.cpm_project_key = #{searchKeyWord}
            or p.yunshu_org_id = #{searchKeyWord})
        </if>
        order by e.update_at desc
    </select>

    <select id="getOpenEngineeringProjectPage"
            resultType="com.cscec3b.iti.projectmanagement.api.dto.response.open.OpenEngineeringProjectPageResp">
        select e.id, e.engineering_key, e.engineering_name, e.engineering_code, e.mapping_execute_unit_id as
        execute_unit_id, yos.name
        executeUnitName, e.project_dept_id, yos2.name projectDeptName
        from engineering_project e
        inner join yunshu_org_sync yos on e.mapping_execute_unit_id = yos.dept_id
        left join yunshu_org_sync yos2 on e.project_dept_id = yos2.dept_id
        where e.deleted = 0 and yos.id_path like concat( #{effectiveIdPath}, '%')
        <if test="req.engineerName != null and req.engineerName != ''">
            and e.engineering_name like concat('%', #{req.engineerName}, '%')
        </if>
        <if test="req.engineerKey != null and req.engineerKey != ''">
            and e.engineering_key = #{req.engineerKey}
        </if>
        <if test="req.engineerCode != null and req.engineerCode != ''">
            and e.engineering_code like concat('%',#{req.engineerCode}, '%')
        </if>
        <if test="req.executeUnitName != null and req.executeUnitName != ''">
            and yos.name like concat('%', #{req.executeUnitName}, '%')
        </if>
        <if test="req.projectDeptName != null and req.projectDeptName != ''">
            and yos2.name like concat('%', #{req.projectDeptName}, '%')
        </if>
        <if test="req.projectDeptId!= null and req.projectDeptId!= ''">
            and e.project_dept_id = #{req.projectDeptId}
        </if>
        order by e.update_at desc
    </select>

    <resultMap id="OpenEngineerProjectTreeRespResultMap"
               type="com.cscec3b.iti.projectmanagement.api.dto.response.open.OpenEngineerProjectTreeResp">
        <id column="id" property="id"/>
        <result column="engineering_key" property="engineeringKey"/>
        <result column="engineering_name" property="engineeringName"/>
        <result column="engineering_code" property="engineeringCode"/>
        <result column="mapping_execute_unit_id" property="executeUnitId"/>
        <result column="project_dept_id" property="projectDeptId"/>
        <result column="executeUnitName" property="executeUnitName"/>
        <result column="projectDeptName" property="projectDeptName"/>
        <result column="parent_id" property="parentId"/>
        <collection property="standardProjectRespList"
                    ofType="com.cscec3b.iti.projectmanagement.api.dto.response.engineeringproject.StandardProjectResp">
            <id column="standard_project_id" property="id"/>
            <result column="cpm_project_key" property="cpmProjectKey"/>
            <result column="cpm_project_name" property="cpmProjectName"/>
            <result column="project_finance_code" property="projectFinanceCode"/>
            <result column="yunshu_org_id" property="yunshuOrgId"/>
        </collection>
    </resultMap>
    <select id="getTreeList"
            resultMap="OpenEngineerProjectTreeRespResultMap">
        select e.id, e.engineering_key, e.engineering_name, e.engineering_code, e.mapping_execute_unit_id, e.parent_id,
        yos.name executeUnitName, e.project_dept_id, yos2.name projectDeptName,
        epm.standard_project_id , p.cpm_project_name, p.cpm_project_key, p.project_finance_code, p.yunshu_org_id
        from engineering_project e
        inner join yunshu_org_sync yos on e.mapping_execute_unit_id = yos.dept_id
        left join yunshu_org_sync yos2 on e.project_dept_id = yos2.dept_id
        left join engineering_standard_project_mapping epm on e.id = epm.engineering_project_id and epm.deleted = 0
        left join project p on p.id = epm.standard_project_id
        where e.deleted = 0
        and e.id in
        <foreach collection="list" item="id" open="(" separator="," close=")">
            #{id}
        </foreach>
    </select>

    <select id="getCompanyViewList"
            resultType="com.cscec3b.iti.projectmanagement.api.dto.response.engineeringproject.CompanyViewEngineeringProjectResp">
        SELECT ep.id,
        ep.engineering_key,
        ep.engineering_name,
        ep.engineering_code,
        ep.execute_unit_id,
        ep.init_finance_code as financeCode,
        yos.name executeUnitName,
        yos.id_path_name executeUnitIdPathName,
        yos.id_path_abbreviation executeUnitIdPathAbbr
        FROM engineering_project ep
        JOIN yunshu_org_sync yos ON ep.mapping_execute_unit_id = yos.dept_id
        WHERE yos.id_path LIKE CONCAT(
        (SELECT id_path FROM yunshu_org_sync WHERE dept_id = #{req.executeUnitId}), '%'
        )
        AND (
        ep.parent_id = 0 -- 没有父级项目
        OR ep.parent_id = -1 -- 对外项目
        OR NOT EXISTS (
        -- 父级项目的执行单位是上级单位则排除
        SELECT 1
        FROM engineering_project ep_parent
        JOIN yunshu_org_sync yos_parent ON ep_parent.mapping_execute_unit_id = yos_parent.dept_id
        WHERE ep_parent.id = ep.parent_id
        AND
        (SELECT id_path FROM yunshu_org_sync WHERE dept_id = #{req.executeUnitId}) LIKE CONCAT(yos_parent.id_path, '%'))
        )
        <if test="req.engineeringName != null and req.engineeringName != ''">
            AND ep.engineering_name LIKE CONCAT('%', #{req.engineeringName}, '%')
        </if>
        <if test="req.engineeringCode != null and req.engineeringCode != ''">
            AND ep.engineering_code LIKE CONCAT('%', #{req.engineeringCode}, '%')
        </if>
        <if test="req.financeCode != null and req.financeCode != ''">
            AND ep.init_finance_code LIKE CONCAT('%', #{req.financeCode}, '%')
        </if>
        <if test="req.engineeringId != null">
            AND ep.id = #{req.engineeringId}
        </if>
        order by ep.update_at desc
    </select>

    <resultMap id="CompanyViewEngineeringProjectRespResultMap"
               type="com.cscec3b.iti.projectmanagement.api.dto.response.engineeringproject.CompanyViewEngineeringProjectTreeResp">
        <id column="id" property="id"/>
        <result column="engineering_key" property="engineeringKey"/>
        <result column="engineering_name" property="engineeringName"/>
        <result column="engineering_code" property="engineeringCode"/>
        <result column="finance_code" property="financeCode"/>
        <result column="parent_id" property="parentId"/>
        <result column="execute_unit_id" property="executeUnitId"/>
        <result column="execute_unit_name" property="executeUnitName"/>
        <result column="execute_unit_id_path_name" property="executeUnitIdPathName"/>
        <result column="execute_unit_id_path_abbr" property="executeUnitIdPathAbbr"/>
        <result column="level" property="level"/>
        <result column="e_type" property="type"/>
        <collection property="mappingStandardProjectResp"
                    ofType="com.cscec3b.iti.projectmanagement.api.dto.response.engineeringproject.CompanyViewStandardProjectResp">
            <id column="p_id" property="id"/>
            <result column="cpm_project_key" property="cpmProjectKey"/>
            <result column="cpm_project_name" property="cpmProjectName"/>
            <result column="init_finance_code" property="projectFinanceCode"/>
            <result column="yunshu_org_id" property="yunshuOrgId"/>
            <result column="yunshu_org_name" property="yunshuOrgName"/>
            <result column="yunshu_org_abbr" property="yunshuOrgAbbr"/>
            <result column="yunshu_org_id_path_name" property="yunshuOrgIdPathName"/>
            <result column="yunshu_org_id_path_abbr" property="yunshuOrgIdPathAbbr"/>
            <result column="parent_code" property="parentCode"/>
            <result column="level" property="level"/>
            <result column="s_type" property="type"/>
        </collection>
    </resultMap>
    <select id="getCompanyViewEngineeringProjectTree"
            resultMap="CompanyViewEngineeringProjectRespResultMap">
        call get_engineer_project_full_view(null, null, null, null,#{engineeringId})
    </select>

    <select id="getEngineeringArchivedPage"
            resultType="com.cscec3b.iti.projectmanagement.api.dto.response.open.OpenEngineeringArchiveResp">
        select ep.id as engineeringProjectId, ep.engineering_key, ep.engineering_name, ep.engineering_code
        from engineering_project ep
        left join yunshu_org_sync yos on ep.mapping_execute_unit_id = yos.dept_id
        where ep.engineering_key in
        <foreach collection="req.engineeringKeys" item="engineeringKey" open="(" separator="," close=")">
            #{engineeringKey}
        </foreach>
        and yos.id_path like concat( #{dataIdPath}, '%')
        order by ep.update_at desc
    </select>

    <select id="getEnginAndStandardProjectMappingInfo"
            resultType="com.cscec3b.iti.projectmanagement.api.dto.response.open.OpenEngineeringProjectMappingResp">
        select espm.is_main mainStandardProject,
        espm.engineering_project_id engineeringProjectId,
        ep.engineering_key engineeringKey,
        ep.engineering_name engineeringName,
        ep.engineering_code engineeringCode,
        p.cpm_project_key cpmProjectKey,
        p.cpm_project_name cpmProjectName,
        p.project_finance_code projectFinanceCode,
        p.id projectId
        from engineering_standard_project_mapping espm
        inner join project p on espm.standard_project_id = p.id
        left join engineering_project ep on espm.engineering_project_id = ep.id
        left join yunshu_org_sync yos on ep.mapping_execute_unit_id = yos.dept_id
        where p.cpm_project_key in
        <foreach collection="req.cpmProjectKeys" item="engineeringProjectId" open="(" separator="," close=")">
            #{engineeringProjectId}
        </foreach>
        and yos.id_path like concat( #{dataIdPath}, '%')
        order by espm.update_at desc
    </select>
</mapper>