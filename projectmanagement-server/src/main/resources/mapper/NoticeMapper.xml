<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.cscec3b.iti.projectmanagement.server.mapper.NoticeMapper">
    <!-- 通用查询映射结果 -->
    <resultMap id="BaseResultMap" type="com.cscec3b.iti.projectmanagement.server.entity.Notice">
        <result column="id" property="id"/>
        <result column="notice_title" property="noticeTitle"/>
        <result column="notice_type" property="noticeType"/>
        <result column="notice_content" property="noticeContent"/>
        <result column="publish_status" property="publishStatus"/>
        <result column="publish_organization" property="publishOrganization"/>
        <result column="publish_time" property="publishTime"/>
        <result column="read_status" property="readStatus"/>
        <result column="reading_quantity" property="readingQuantity"/>
        <result column="create_by" property="createBy"/>
        <result column="create_time" property="createTime"/>
        <result column="update_time" property="updateTime"/>
    </resultMap>

    <!-- 查询公告和附件明细映射结果 -->
    <resultMap id="NoticeAttachment" type="com.cscec3b.iti.projectmanagement.api.dto.response.notice.NoticeInfoResp">
        <result column="id" property="id"/>
        <result column="notice_title" property="noticeTitle"/>
        <result column="notice_type" property="noticeType"/>
        <result column="notice_content" property="noticeContent"/>
        <result column="publish_status" property="publishStatus"/>
        <result column="publish_organization" property="publishOrganization"/>
        <result column="publish_time" property="publishTime"/>
        <result column="reading_quantity" property="readingQuantity"/>
        <collection property="attachments" javaType="java.util.List"
                    ofType="com.cscec3b.iti.projectmanagement.api.dto.dto.AttachmentDto">
            <result property="inEditor" column="in_editor"/>
            <result property="originalName" column="original_name"/>
            <result property="fileSize" column="file_size"/>
            <result property="filePath" column="file_path"/>
            <result property="createAt" column="create_at"/>
            <result property="fileId" column="file_id"/>
            <result property="fileMd5" column="file_md5"/>
        </collection>
    </resultMap>

    <!-- 通用查询结果列 -->
    <sql id="BaseColumnList">
        id,
        notice_title,
        notice_type,
        notice_content,
        publish_status,
        publish_organization,
        publish_time,
        read_status,
        reading_quantity,
        create_by,
        create_time,
        update_time
    </sql>

    <!--通知公告查询条件-->
    <sql id="pageListWhere">
        where
        1 = 1
        <if test="vo.noticeTitle != null and vo.noticeTitle != ''">
            and notice_title LIKE CONCAT('%', #{vo.noticeTitle}, '%')
        </if>
        <if test="vo.publishOrganization != null and vo.publishOrganization != ''">
            and publish_organization LIKE CONCAT('%', #{vo.publishOrganization}, '%')
        </if>
        <if test="vo.noticeType != null">
            and notice_type = #{vo.noticeType}
        </if>
        <if test="vo.publishStartTime != null and vo.publishStartTime != 0">
            and publish_time &gt;= #{vo.publishStartTime}
        </if>
        <if test="vo.publishEndTime != null and vo.publishEndTime != 0">
            and publish_time &lt;= #{vo.publishEndTime}
        </if>
        <choose>
            <when test="vo.publishStatus == null">
                and (publish_status = 1 or create_by = #{userId})
            </when>
            <when test="vo.publishStatus == 1">
                and publish_status = 1
            </when>
            <when test="vo.publishStatus == 0">
                and publish_status = 0 and create_by = #{userId}
            </when>
        </choose>
        order by ifnull(publish_time,update_time) desc
    </sql>

    <!--获取通知公告数量-->
    <select id="getNoticeCount" resultType="long">
        select
        count(*)
        from
        notice_center t
        <include refid="pageListWhere"/>
    </select>
    <!--分页查询-->
    <select id="pageList" resultType="com.cscec3b.iti.projectmanagement.api.dto.response.notice.NoticePageResp">
        select
        t.id,
        t.notice_title,
        t.notice_type,
        t.notice_content,
        t.publish_status,
        t.publish_organization,
        t.publish_time,
        t.reading_quantity,
        t.create_by,
        t.create_time,
        t.update_time,
        ifnull(t1.id, 0) as read_status
        from notice_center t
        left join user_belong_notice t1 on t.id = t1.notice_id and t1.user_uuid = #{userId}
        <include refid="pageListWhere"/>
    </select>

    <!--分页查询-->
    <select id="qryNotices" resultMap="BaseResultMap">
        select
        <include refid="BaseColumnList"/>
        from
        notice_center t
        <include refid="pageListWhere"/>
    </select>

    <!--首页通知公告列表-->
    <select id="homePageNotice" resultType="com.cscec3b.iti.projectmanagement.api.dto.response.notice.NoticePageResp">
        select
            t.id,
            t.notice_title,
            t.notice_type,
            t.notice_content,
            t.publish_status,
            t.publish_organization,
            t.publish_time,
            t.reading_quantity,
            t.create_by,
            t.create_time,
            t.update_time,
            IF(t1.id is null, 0,1) AS read_status
        from notice_center t
                 left join user_belong_notice t1 on t.id = t1.notice_id and t1.user_uuid = #{userId}
        where publish_status = 1
        order by publish_time desc
        limit 5
    </select>

    <!--查询通知公告-->
    <select id="getNotice" resultType="com.cscec3b.iti.projectmanagement.server.entity.Notice">
        select
        <include refid="BaseColumnList"/>
        from
        notice_center t
        where t.id = #{id}
    </select>

    <!--获取通知公告及附件明细-->
    <select id="getNoticeById" resultMap="NoticeAttachment">
        select
        t.id, t.notice_title, t.notice_type, t.notice_content,
        t.publish_status, t.publish_organization, t.publish_time,
        t.read_status, t.reading_quantity,
        t.create_by, t.create_time,t.update_time,
        a.id,
        a.business_id,
        a.in_editor,
        a.original_name,
        a.file_size,
        a.file_path,
        a.file_id,
        a.file_md5,
        a.create_at,
        a.create_by
        from
        notice_center as t
        left join
        attachment as a
        on
        t.id = a.business_id and a.business_type = 1
        where t.id = #{id}
    </select>

    <!--新增通知公告-->
    <insert id="insertNotice" useGeneratedKeys="true" keyProperty="id">
        insert into notice_center(notice_title, notice_type, notice_content, publish_status, publish_organization,
        publish_time, read_status, reading_quantity,
        create_by, create_time, update_time)
        values (#{noticeTitle}, #{noticeType}, #{noticeContent}, #{publishStatus}, #{publishOrganization},
        #{publishTime}, #{readStatus}, #{readingQuantity},
        #{createBy}, #{createTime},#{updateTime})
    </insert>

    <!--更新通知公告-->
    <update id="updateNotice">
        update
        notice_center
        set
        <if test="noticeTitle != null and noticeTitle != ''">
            notice_title = #{noticeTitle},
        </if>
        <if test="noticeContent != null and noticeContent != ''">
            notice_content = #{noticeContent},
        </if>
        <if test="publishOrganization != null and publishOrganization != ''">
            publish_organization = #{publishOrganization},
        </if>
        <if test="noticeType != null">
            notice_type = #{noticeType},
        </if>
        <if test="publishStatus != null">
            publish_status = #{publishStatus},
        </if>
        <if test="publishTime != null">
            publish_time = #{publishTime},
        </if>
        <if test="readStatus != null">
            read_status = #{readStatus},
        </if>
        <if test="readingQuantity != null">
            reading_quantity = #{readingQuantity},
        </if>
        <if test="updateTime != null">
            update_time = #{updateTime}
        </if>
        where
        id = #{id}
    </update>

    <!--发布通知公告-->
    <update id="updatePublishStatus">
        update
        notice_center
        set
        <if test="publishStatus != null">
            publish_status = #{publishStatus},
        </if>
        <if test="publishTime != null">
            publish_time = #{publishTime},
        </if>
        <if test="updateTime != null">
            update_time = #{updateTime}
        </if>
        where
        id = #{id}
    </update>

    <!--删除通知公告-->
    <delete id="delNotice" parameterType="java.lang.Long">
        delete from notice_center where id = #{id}
    </delete>

    <!--删除通知公告的所有附件-->
    <delete id="delAttachments" parameterType="java.util.List">
        delete from attachment
        where id
        in
        <foreach collection="list" item="id" index="index" open="(" close=")" separator=",">
            #{id}
        </foreach>
    </delete>

    <!--批量新增通知公告的所有附件-->
    <insert id="batchSaveAttachment" parameterType="java.util.List">
        insert into attachment(
        id,business_id,business_type,original_name,file_size,
        file_path,create_at,create_by
        )
        values
        <foreach collection="list" item="item" index="index" separator=",">
            (#{item.id},#{item.businessId},#{item.businessType},
            #{item.originalName},#{item.fileSize},#{item.filePath},
            #{item.createAt},#{item.createBy})
        </foreach>
    </insert>

    <update id="increment">
        update
        notice_center
        set reading_quantity = reading_quantity + 1
        where id = #{id}
    </update>
</mapper>
