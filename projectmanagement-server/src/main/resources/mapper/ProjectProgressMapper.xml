<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.cscec3b.iti.projectmanagement.server.mapper.ProjectProgressMapper">

    <!--查询签约未立项预警-->
    <select id="agencyPageList"
            resultType="com.cscec3b.iti.projectmanagement.api.dto.response.warn.AgencyNoProjectResp">
        select p.id                        projectId,
               p.independent_contract_id   independentContractId,
               p.independent_contract_type independentContractType,
               p.project_finance_code      projectStatus,
               p.project_dept_name         projectDepartmentStatus,
               pp.sign_time                receiveTime,
        p.yunshu_execute_unit_abbreviation executeUnitAbbreviation,
               p.yunshu_execute_unit       yunshuExecuteUnit,
               p.yunshu_execute_unit_code  yunshuExecuteUnitCode,
               p.yunshu_execute_unit_id    yunshuExecuteUnitId,
               p.yunshu_execute_unit_id_path yunshuExecuteUnitIdPath
        from project_progress pp
                 left join project p on pp.project_id = p.id
        where p.source_system=1 and  pp.warn_status = 1
        and pp.sign_status = 2
          and (pp.approve_status = 0 or pp.approve_status = 1)
        and (p.yunshu_execute_unit_id_path like concat('%/', #{id}, '/%') or p.yunshu_execute_unit_id = #{id})
        order by pp.sign_time desc
    </select>

    <select id="getEarlyWarningById"
            resultType="com.cscec3b.iti.projectmanagement.api.dto.response.warn.AgencyNoProjectResp">
        select t.independentContract,group_concat(t.contractName separator '、') contractName,group_concat(t.contractCode
        separator '、') contractCode from (

        (select
        concat(independent_contract_id,',',independent_contract_type) independentContract,
        project_name contractName,
        contract_code contractCode
        from contract c
        where independent_contract_id in
        <foreach collection="idSet" item="id" open="(" separator="," close=")">
            #{id}
        </foreach>)

        union all

        (select
        concat(independent_contract_id,',',independent_contract_type) independentContract,
        project_name contractName,
        supplementary_agreement_code contractCode
        from supplementary_agreement
        where independent_contract_id in
        <foreach collection="idSet" item="id" open="(" separator="," close=")">
            #{id}
        </foreach>)

        union all

        (select
        concat(independent_contract_id,',',independent_contract_type) independentContract,
        project_name contractName,
        contract_code contractCode
        from bureau_contract
        where independent_contract_id in
        <foreach collection="idSet" item="id" open="(" separator="," close=")">
            #{id}
        </foreach>)

        union all

        (select
        concat(independent_contract_id,',',independent_contract_type) independentContract,
        project_name contractName,
        bureau_supplementary_agreement_code contractCode
        from bureau_supplementary_agreement
        where independent_contract_id in
        <foreach collection="idSet" item="id" open="(" separator="," close=")">
            #{id}
        </foreach>)
        ) t group by t.independentContract
    </select>
    <!--查询立项未签约预警-->
    <select id="projectPageList"
            resultType="com.cscec3b.iti.projectmanagement.api.dto.response.warn.ProjectNoAgencyResp">
        select p.id                   projectId,
               p.project_finance_code projectCode,
               p.project_finance_name projectName,
               p.project_name         engineeringName,
        p.yunshu_execute_unit executeUnit,
               pp.approve_finish_time projectCompletionTime,
        p.yunshu_execute_unit_abbreviation executeUnitAbbreviation
        from project_progress pp
                 left join project p on pp.project_id = p.id
        where p.source_system=1 and pp.warn_status = 1
          and pp.sign_status = 0
          and pp.approve_status = 2
        and (p.yunshu_execute_unit_id_path like concat('%/', #{id}, '/%') or p.yunshu_execute_unit_id = #{id})
        order by pp.approve_finish_time desc
    </select>


    <select id="qryWarnInfos" resultType="com.cscec3b.iti.projectmanagement.server.scheduled.dto.WarnInfoDto">
        select p.id                   projectId,
               p.business_type        businessType,
               pp.sign_status         signStatus,
               pp.sign_time           signTime,
               pp.approve_finish_time approveFinishTime,
               pp.approve_status      approveStatus,
               pp.warn_status         warnStatus
        from project_progress pp
                 left join project p on pp.project_id = p.id
        where pp.warn_status != 2
        and p.source_system=1
    </select>

    <update id="batchUpdateById" parameterType="com.cscec3b.iti.projectmanagement.server.entity.ProjectProgress">
        update
        project_progress
        set warn_status = #{status}
        where project_id in
        <foreach collection="list" item="id" open="(" separator="," close=")">
            #{id}
        </foreach>
    </update>

    <insert id="insert" parameterType="com.cscec3b.iti.projectmanagement.server.entity.ProjectProgress">
        INSERT INTO project_progress
        (project_id,
         sign_time,
         approve_finish_time,
         sign_status,
         approve_status,
         warn_status,
         to_finance_time,
         smart_dept_name,
        smart_dept_address,
        smart_query_time,
        smart_query_count,
        smart_approve_status,
        to_uc_time,
        uc_depart_status,
        finance_remarks,
        smart_remarks,
        uc_remarks,
        finance_return_count,
        a8_status,
        cpm_remarks,
        create_command_status,
        create_command_start_time,
        create_command_end_time
        )
        VALUES (#{vo.projectId}, #{vo.signTime}, #{vo.approveFinishTime}, #{vo.signStatus},
        ifnull(#{vo.approveStatus}, 0), ifnull(#{vo.warnStatus}, 0), #{vo.toFinanceTime}, #{vo.smartDeptName},
        #{vo.smartDeptAddress}, #{vo.smartQueryTime}, ifnull(#{vo.smartQueryCount}, 0),
        ifnull(#{vo.smartApproveStatus}, 0), #{vo.toUcTime}, ifnull(#{vo.ucDepartStatus}, 0),
        #{vo.financeRemarks}, #{vo.smartRemarks}, #{vo.ucRemarks}, ifnull(#{vo.financeReturnCount}, 0),
        ifnull(#{vo.a8Status}, 0), #{vo.cpmRemarks},#{vo.createCommandStatus},#{vo.createCommandStartTime},#{vo.createCommandEndTime})
    </insert>

    <select id="select" resultType="com.cscec3b.iti.projectmanagement.server.entity.ProjectProgress">
        SELECT project_id,
               sign_time,
               approve_finish_time,
               sign_status,
               approve_status,
               warn_status,
               to_finance_time,
               smart_dept_name,
               smart_dept_address,
               smart_query_time,
               smart_query_count,
               smart_approve_status,
               to_uc_time,
               uc_depart_status,
               finance_remarks,
               smart_remarks,
               uc_remarks,
               finance_return_count,
               a8_status
        FROM project_progress
        WHERE project_id = #{vo.projectId}
    </select>

    <select id="pageProjectMonitorList"
            parameterType="com.cscec3b.iti.projectmanagement.api.dto.request.warn.ProjectMonitorQueryParams"
            resultType="com.cscec3b.iti.projectmanagement.api.dto.response.warn.ProjectMonitorResp">
        SELECT
        p.project_code,
        p.project_name,
        p.project_finance_code,
        p.project_finance_name,
        p.project_finance_abbreviation,
        p.a8_project_code ,
        pp.project_id,
        pp.to_finance_time,
        pp.approve_finish_time AS financeReturnTime,
        pp.approve_status AS financeApproveStatus,
        p.project_dept_name AS smartDeptName,
        p.project_dept_abbreviation,
        pp.smart_dept_address,
        pp.smart_query_time,
        pp.smart_query_count,
        pp.smart_approve_status,
        pp.to_uc_time,
        pp.uc_depart_status,
        pp.finance_remarks,
        pp.smart_remarks,
        pp.uc_remarks,
        p.independent_contract_type,
        p.independent_contract_no,
        p.source_system,
        pp.cpm_remarks,
        pp.create_command_status,
        pp.create_command_start_time,
        pp.create_command_end_time,
        p.is_create_head,
        p.source_system,
        p.business_type,
        p.create_at as projectCreateTime,
        p.yunshu_execute_unit_id,
        p.yunshu_execute_unit,
        p.yunshu_execute_unit_id_path
        FROM
        project_progress pp
        inner join project p on pp.project_id = p.id
        WHERE pp.project_id = p.id
        <choose>
            <when test="pm.exceptionDataDisplay!=null and pm.exceptionDataDisplay==1">
<!--                查询条件展示异常数据，执行单位：分域匹配+执行单位异常数据，否则执行单位仅分域匹配-->
                AND (((p.yunshu_execute_unit_id='' OR p.yunshu_execute_unit_id is null) AND (p.yunshu_execute_unit='' OR
                p.yunshu_execute_unit is null))
                OR (p.yunshu_execute_unit_id_path like concat('%/', #{pm.executeUnitId}, '/%') OR
                p.yunshu_execute_unit_id = #{pm.executeUnitId}))
            </when>
            <otherwise>
                AND (p.yunshu_execute_unit_id_path like concat('%/', #{pm.executeUnitId}, '/%') OR
                p.yunshu_execute_unit_id = #{pm.executeUnitId})
            </otherwise>
        </choose>
<!--        默认监控全部-->
        <if test="pm.projectName!=null and pm.projectName!=''">
            AND p.project_name LIKE CONCAT('%',#{pm.projectName},'%')
        </if>
        <if test="pm.projectFinanceCode!=null and pm.projectFinanceCode!=''">
            AND p.project_finance_code LIKE CONCAT('%',#{pm.projectFinanceCode},'%')
        </if>
        <if test="pm.projectFinanceName!=null and pm.projectFinanceName!=''">
            AND p.project_finance_name LIKE CONCAT('%',#{pm.projectFinanceName},'%')
        </if>
        <if test="pm.projectFinanceAbbreviation!=null and pm.projectFinanceAbbreviation!=''">
            AND p.project_finance_abbreviation LIKE CONCAT('%',#{pm.projectFinanceAbbreviation},'%')
        </if>
        <if test="pm.a8ProjectCode != null and pm.a8ProjectCode != ''">
            AND p.a8_project_code LIKE CONCAT('%',#{pm.a8ProjectCode},'%')
        </if>
        <if test="pm.financeApproveStatus != null and pm.financeApproveStatus != ''">
            AND pp.approve_status = #{pm.financeApproveStatus}
        </if>
        <if test="pm.smartApproveStatus != null and pm.smartApproveStatus != ''">
            AND pp.smart_approve_status = #{pm.smartApproveStatus}
        </if>
        <if test="pm.ucDepartStatus != null and pm.ucDepartStatus != ''">
            and
            case
            when (p.business_type = '基础设施' AND p.is_create_head = 'Y' AND p.source_system = 1) then
            pp.create_command_status = #{pm.ucDepartStatus}
            when NOT (p.business_type = '基础设施' AND p.is_create_head = 'Y' AND p.source_system = 1) then
            pp.uc_depart_status = #{pm.ucDepartStatus}
            end
        </if>
        <if test="pm.bidCode != null and pm.bidCode != ''">
            AND p.independent_contract_type=1 AND p.independent_contract_no LIKE CONCAT('%',#{pm.bidCode},'%')
        </if>
        <if test="pm.agreementCode != null and pm.agreementCode != ''">
            AND p.independent_contract_type=3 AND p.independent_contract_no LIKE CONCAT('%',#{pm.agreementCode},'%')
        </if>
        <if test="pm.contractCode != null and pm.contractCode != ''">
            AND p.independent_contract_type=4 AND p.independent_contract_no LIKE CONCAT('%',#{pm.contractCode},'%')
        </if>
        <choose>
            <when test="pm.monitorProjectType!=null and pm.monitorProjectType!='' and pm.monitorProjectType== 1">
<!--                重大基础设施-->
                AND (p.business_type = '基础设施' AND p.is_create_head = 'Y' AND p.source_system = 1)
            </when>
            <when test="pm.monitorProjectType!=null and pm.monitorProjectType!='' and pm.monitorProjectType== 2">
                <!--                非重大基础设施-->
                AND NOT ((p.business_type = '基础设施' AND p.is_create_head = 'Y' AND p.source_system = 1) OR (p.source_system = 2))
            </when>
            <when test="pm.monitorProjectType!=null and pm.monitorProjectType!='' and pm.monitorProjectType== 3">
                <!--                特殊立项-->
                AND (p.source_system = 2)
            </when>
        </choose>
<!--        ORDER BY pp.project_id DESC-->
        ORDER BY p.create_at DESC
    </select>

    <update id="update" parameterType="com.cscec3b.iti.projectmanagement.server.entity.ProjectProgress">
        UPDATE project_progress
        SET
        <trim suffixOverrides=",">
            <if test="vo.signTime!=null and vo.signTime!=''">
                sign_time=#{vo.signTime},
            </if>
            <if test="vo.approveFinishTime!=null and vo.approveFinishTime!=''">
                approve_finish_time=#{vo.approveFinishTime},
            </if>
            <if test="vo.signStatus!=null">
                sign_status=#{vo.signStatus},
            </if>
            <if test="vo.approveStatus!=null">
                approve_status=#{vo.approveStatus},
            </if>
            <if test="vo.warnStatus!=null ">
                warn_status=#{vo.warnStatus},
            </if>
            <if test="vo.toFinanceTime!=null and vo.toFinanceTime!=''">
                to_finance_time=#{vo.toFinanceTime},
            </if>
            <if test="vo.smartDeptName!=null and vo.smartDeptName!=''">
                smart_dept_name=#{vo.smartDeptName},
            </if>
            <if test="vo.smartDeptAddress!=null and vo.smartDeptAddress!=''">
                smart_dept_address=#{vo.smartDeptAddress},
            </if>
            <if test="vo.smartQueryTime!=null">
                smart_query_time=#{vo.smartQueryTime},
            </if>
            <if test="vo.smartQueryCount!=null">
                smart_query_count=#{vo.smartQueryCount},
            </if>
            <if test="vo.smartApproveStatus!=null">
                smart_approve_status=#{vo.smartApproveStatus},
            </if>
            <if test="vo.toUcTime!=null and vo.toUcTime!=''">
                to_uc_time=#{vo.toUcTime},
            </if>
            <if test="vo.ucDepartStatus!=null">
                uc_depart_status=#{vo.ucDepartStatus},
            </if>
            <if test="vo.financeRemarks!=null and vo.financeRemarks!=''">
                finance_remarks=#{vo.financeRemarks},
            </if>
            <if test="vo.smartRemarks!=null and vo.smartRemarks!=''">
                smart_remarks=#{vo.smartRemarks},
            </if>
            <if test="vo.ucRemarks!=null and vo.ucRemarks!=''">
                uc_remarks=#{vo.ucRemarks},
            </if>
            <if test="vo.financeReturnCount!=null">
                finance_return_count=#{vo.financeReturnCount},
            </if>
            <if test="vo.a8Status!=null">
                a8_status=#{vo.a8Status},
            </if>
            <if test="vo.createCommandStatus!=null ">
                create_command_status=#{vo.createCommandStatus},
            </if>
            <if test="vo.createCommandStartTime!=null">
                create_command_start_time=#{vo.createCommandStartTime},
            </if>
            <if test="vo.createCommandEndTime!=null">
                create_command_end_time=#{vo.createCommandEndTime},
            </if>
            <if test="vo.cpmRemarks!=null and vo.cpmRemarks!=''">
                cpm_remarks=#{vo.cpmRemarks}
            </if>
        </trim>
        where project_id = #{vo.projectId}
    </update>

    <update id="financeProjectApprovalUpdate">
        update project_progress
        set
        <trim suffixOverrides=",">
            <if test="toFinanceTime != null">
                to_finance_time = #{toFinanceTime},
            </if>
            <if test="approveFinishTime != null">
                approve_finish_time = #{approveFinishTime},
            </if>
            warn_status =
            case
            when sign_status = #{progressEnum.COMPLETED.dictCode} and approve_status =
            #{progressEnum.COMPLETED.dictCode}
            then #{progressEnum.COMPLETED.dictCode}
            else warn_status
            end ,
            approve_status = #{progressEnum.dictCode},
            finance_remarks = #{financeRemarks}
        </trim>
        where project_id = #{id} and approve_status != #{progressEnum.COMPLETED.dictCode}
    </update>

    <update id="ucProjectApprovalUpdate">
        update project_progress
        set
        <trim suffixOverrides=",">
            <if test="toUcTime != null">
                to_uc_time = #{toUcTime},
            </if>
            uc_depart_status = #{progressEnum.dictCode},
            uc_remarks = #{ucRemarks}
        </trim>
        where project_id = #{id}
    </update>

    <update id="updateSignStatus">
        update project_progress
        set
        sign_time = #{signTime}, sign_status=#{progressEnum.dictCode}
        where project_id = #{id}
    </update>

    <update id="updateWarnStatus">
        update project_progress
        set warn_status = #{progressEnum.dictCode}
        where project_id = #{id}
    </update>

    <update id="updateCpmRemarks">
        update project_progress
        set cpm_remarks = #{cpmRemarks}
        where project_id = #{id}
    </update>

    <update id="createCommandInit">
        update project_progress
        set
        <trim suffixOverrides=",">
            <if test="createCommandStartTime != null">
                create_command_start_time = #{createCommandStartTime},
            </if>
            <if test="createCommandEndTime != null">
                create_command_end_time = #{createCommandEndTime},
            </if>
            create_command_status = #{progressEnum.dictCode},
        </trim>
        where project_id = #{projectId}
    </update>

    <update id="smartsiteProjectApprovalUpdate">
        update project_progress
        set
        <trim suffixOverrides=",">
            <if test="smartQueryTime != null">
                smart_query_time = #{smartQueryTime},
            </if>
            <if test="toUcTime != null">
                to_uc_time = #{toUcTime},
            </if>
            smart_approve_status = #{progressEnum.dictCode},
            smart_remarks = #{smartRemarks}
        </trim>
        where project_id = #{id}
    </update>

    <select id="exists" resultType="int">
        select count(*) from project_progress where project_id = #{projectId}
    </select>

    <!--查询签约未立项预警-切换云枢组织-->
    <select id="agencyPageCloudPivot"
            resultType="com.cscec3b.iti.projectmanagement.api.dto.response.warn.AgencyNoProjectResp">
        select p.id                        projectId,
               p.independent_contract_id   independentContractId,
               p.independent_contract_type independentContractType,
               p.project_finance_code      projectStatus,
               p.project_dept_name         projectDepartmentStatus,
               pp.sign_time                receiveTime,
               p.yunshu_execute_unit       yunshuExecuteUnit,
               p.yunshu_execute_unit_code  yunshuExecuteUnitCode,
               p.yunshu_execute_unit_id    yunshuExecuteUnitId,
               p.yunshu_execute_unit_id_path yunshuExecuteUnitIdPath,
               p.yunshu_execute_unit_abbreviation yunshuExecuteUnitAbbreviation
        from project_progress pp
                 left join project p on pp.project_id = p.id
        where p.source_system=1 and  pp.warn_status = 1
          and pp.sign_status = 2
          and (pp.approve_status = 0 or pp.approve_status = 1)
          and (p.yunshu_execute_unit_id_path like concat('%', #{treeId}, '%'))
        order by pp.sign_time desc
    </select>

    <!--查询立项未签约预警-切换云枢组织-->
    <select id="projectPageCloudPivot"
            resultType="com.cscec3b.iti.projectmanagement.api.dto.response.warn.ProjectNoAgencyResp">
        select p.id                   projectId,
               p.project_finance_code projectCode,
               p.project_finance_name projectName,
               p.project_name         engineeringName,
               pp.approve_finish_time projectCompletionTime,
               p.yunshu_execute_unit       yunshuExecuteUnit,
               p.yunshu_execute_unit_code  yunshuExecuteUnitCode,
               p.yunshu_execute_unit_id    yunshuExecuteUnitId,
               p.yunshu_execute_unit_id_path yunshuExecuteUnitIdPath,
                p.yunshu_execute_unit_abbreviation yunshuExecuteUnitAbbreviation
        from project_progress pp
                 left join project p on pp.project_id = p.id
        where p.source_system=1 and pp.warn_status = 1
          and pp.sign_status = 0
          and pp.approve_status = 2
          and (p.yunshu_execute_unit_id_path like concat('%', #{treeId}, '%'))
        order by pp.approve_finish_time desc
    </select>

<!--项目监控列表云枢组织切换-->
    <select id="pageProjectMonitorCloudPivot"
            parameterType="com.cscec3b.iti.projectmanagement.api.dto.request.warn.ProjectMonitorQueryParams"
            resultType="com.cscec3b.iti.projectmanagement.api.dto.response.warn.ProjectMonitorResp">
        SELECT
        p.project_code,
        p.project_name,
        p.project_finance_code,
        p.project_finance_name,
        p.project_finance_abbreviation,
        p.a8_project_code ,
        pp.project_id,
        pp.to_finance_time,
        pp.approve_finish_time AS financeReturnTime,
        pp.approve_status AS financeApproveStatus,
        p.project_dept_name AS smartDeptName,
        p.project_dept_abbreviation,
        pp.smart_dept_address,
        pp.smart_query_time,
        pp.smart_query_count,
        pp.smart_approve_status,
        pp.to_uc_time,
        pp.uc_depart_status,
        pp.finance_remarks,
        pp.smart_remarks,
        pp.uc_remarks,
        p.independent_contract_type,
        p.independent_contract_no,
        p.source_system,
        pp.cpm_remarks,
        pp.create_command_status,
        pp.create_command_start_time,
        pp.create_command_end_time,
        p.is_create_head,
        p.source_system,
        p.business_type,
        p.create_at as projectCreateTime,
        p.yunshu_execute_unit_id,
        p.yunshu_execute_unit,
        p.yunshu_execute_unit_id_path,
        p.yunshu_execute_unit_abbreviation yunshuExecuteUnitAbbreviation,
        p.cpm_project_key,
        p.cpm_project_name
        FROM
        project_progress pp
        inner join project p on pp.project_id = p.id
        WHERE pp.project_id = p.id
        <!--        默认监控全部-->
        <if test="pm.treeId!=null and pm.treeId!=''">
            AND p.yunshu_execute_unit_id_path LIKE CONCAT('%',#{pm.treeId},'%')
        </if>
        <if test="pm.projectName!=null and pm.projectName!=''">
            AND p.project_name LIKE CONCAT('%',#{pm.projectName},'%')
        </if>
        <if test="pm.projectFinanceCode!=null and pm.projectFinanceCode!=''">
            AND p.project_finance_code LIKE CONCAT('%',#{pm.projectFinanceCode},'%')
        </if>
        <if test="pm.yunshuOrgId != null and pm.yunshuOrgId != ''">
            AND p.yunshu_org_id = #{pm.yunshuOrgId}
        </if>
        <if test="pm.projectFinanceName!=null and pm.projectFinanceName!=''">
            AND p.project_finance_name LIKE CONCAT('%',#{pm.projectFinanceName},'%')
        </if>
        <if test="pm.projectFinanceAbbreviation!=null and pm.projectFinanceAbbreviation!=''">
            AND p.project_finance_abbreviation LIKE CONCAT('%',#{pm.projectFinanceAbbreviation},'%')
        </if>
        <if test="pm.a8ProjectCode != null and pm.a8ProjectCode != ''">
            AND p.a8_project_code LIKE CONCAT('%',#{pm.a8ProjectCode},'%')
        </if>
        <if test="pm.financeApproveStatus != null and pm.financeApproveStatus != ''">
            AND pp.approve_status = #{pm.financeApproveStatus}
        </if>
        <if test="pm.smartApproveStatus != null and pm.smartApproveStatus != ''">
            AND pp.smart_approve_status = #{pm.smartApproveStatus}
        </if>
        <if test="pm.ucDepartStatus != null and pm.ucDepartStatus != ''">
            and
            case
            when (p.business_type = '基础设施' AND p.is_create_head = 'Y' AND p.source_system = 1) then
            pp.create_command_status = #{pm.ucDepartStatus}
            when NOT (p.business_type = '基础设施' AND p.is_create_head = 'Y' AND p.source_system = 1) then
            pp.uc_depart_status = #{pm.ucDepartStatus}
            end
        </if>
        <if test="pm.bidCode != null and pm.bidCode != ''">
            AND p.independent_contract_type=1 AND p.independent_contract_no LIKE CONCAT('%',#{pm.bidCode},'%')
        </if>
        <if test="pm.agreementCode != null and pm.agreementCode != ''">
            AND p.independent_contract_no LIKE CONCAT('%',#{pm.agreementCode},'%')
            AND ( p.independent_contract_type=3 or p.independent_contract_type = 8 )
        </if>
        <if test="pm.contractCode != null and pm.contractCode != ''">
            AND p.independent_contract_type=4 AND p.independent_contract_no LIKE CONCAT('%',#{pm.contractCode},'%')
        </if>
        <if test="pm.investmentCode != null and pm.investmentCode != ''">
            and p.independent_contract_type = 9
            and p.independent_contract_no like concat('%', #{pm.investmentCode}, '%')
        </if>
        <if test="pm.cpmProjectKey != null and pm.cpmProjectKey !=''">
            AND p.cpm_project_key LIKE CONCAT('%', #{pm.cpmProjectKey}, '%')
        </if>
        <choose>
            <when test="pm.monitorProjectType!=null and pm.monitorProjectType!='' and pm.monitorProjectType== 1">
                <!--                重大基础设施-->
                AND (p.business_type = '基础设施' AND p.is_create_head = 'Y' AND p.source_system = 1)
            </when>
            <when test="pm.monitorProjectType!=null and pm.monitorProjectType!='' and pm.monitorProjectType== 2">
                <!--                非重大基础设施-->
                AND NOT ((p.business_type = '基础设施' AND p.is_create_head = 'Y' AND p.source_system = 1) OR (p.source_system = 2))
            </when>
            <when test="pm.monitorProjectType!=null and pm.monitorProjectType!='' and pm.monitorProjectType== 3">
                <!--                特殊立项-->
                AND (p.source_system = 2)
            </when>
        </choose>
        ORDER BY p.create_at DESC
    </select>

    <select id="mainDataInfo"
            resultType="com.cscec3b.iti.projectmanagement.api.dto.response.approvalstep.MainDataPushInfoResp">
        select
        p.id cpmProjectId, p.cpm_project_key,
        pp.to_finance_time, pp.approve_finish_time, pp.approve_status,
        pp.smart_query_time, pp.to_uc_time, pp.smart_approve_status,
        pp.finance_remarks, pp.smart_remarks,
        p.project_finance_code, p.project_finance_name, p.project_finance_abbreviation,
        p.yunshu_org_id

        from bid_approval ba
        left join project p on ba.cpm_project_id = p.id
        left join project_progress pp on p.id = pp.project_id
        where ba.is_independ_project = 'Y'
        and ba.cpm_project_id = #{id}
    </select>
</mapper>
