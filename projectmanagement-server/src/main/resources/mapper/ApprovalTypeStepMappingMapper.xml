<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.cscec3b.iti.projectmanagement.server.mapper.ApprovalTypeStepMappingMapper">
    <resultMap id="BaseResultMap" type="com.cscec3b.iti.projectmanagement.server.entity.ApprovalTypeStepMapping">
        <!--@mbg.generated-->
        <!--@Table approval_type_step_mapping-->
        <id column="id" jdbcType="BIGINT" property="id"/>
        <result column="type_id" jdbcType="BIGINT" property="typeId"/>
        <result column="step_no" jdbcType="INTEGER" property="stepNo"/>
        <result column="step_seq" jdbcType="TINYINT" property="stepSeq"/>
        <result column="version" jdbcType="INTEGER" property="version"/>
        <result column="create_at" jdbcType="BIGINT" property="createAt"/>
        <result column="create_by" jdbcType="VARCHAR" property="createBy"/>
        <result column="update_at" jdbcType="BIGINT" property="updateAt"/>
        <result column="update_by" jdbcType="VARCHAR" property="updateBy"/>
        <result column="is_send_todo_task" jdbcType="TINYINT" property="sendTodoTask"/>
        <result column="is_send_notice_msg" jdbcType="TINYINT" property="sendNoticeMsg"/>
        <result column="msg_config_id" jdbcType="VARCHAR" property="msgConfigId"/>
        <result column="send_user_codes" jdbcType="VARCHAR" property="sendUserCodes"/>
        <result column="deleted" jdbcType="BIGINT" property="deleted"/>
        <result column="org_id" jdbcType="VARCHAR" property="orgId"/>
        <result column="remind_expression" jdbcType="VARCHAR" property="remindExpression"/>
        <result column="user_chose_type" jdbcType="INTEGER" property="userChoseType"/>
    </resultMap>
    <sql id="Base_Column_List">
        <!--@mbg.generated-->
        id, type_id, step_no, step_seq, create_at, create_by, update_at, update_by, `version`, is_send_todo_task,
        is_send_notice_msg, msg_config_id, send_user_codes, deleted, org_id, remind_expression, user_chose_type
    </sql>

    <resultMap id="ApprovalTypeStepMappingRespResultMap"
               type="com.cscec3b.iti.projectmanagement.api.dto.response.approvalstep.ApprovalTypeStepMappingResp">
        <result column="stepMappingId" property="stepMappingId"/>
        <result column="type_id" property="typeId"/>
        <result column="step_no" property="stepNo"/>
        <result column="step_seq" property="stepSeq"/>
        <result column="version" property="version"/>
        <result column="sendTodoTask" property="sendTodoTask"/>
        <result column="sendNoticeMsg" property="sendNoticeMsg"/>
        <result column="msg_config_id" property="msgConfigId"/>
        <result column="send_user_codes" property="sendUserCodes"
                typeHandler="com.baomidou.mybatisplus.extension.handlers.JacksonTypeHandler"/>
        <result column="org_id" property="orgId"/>
        <result column="remind_expression" property="remindExpression"/>
        <result column="user_chose_type" property="userChoseType"/>
    </resultMap>
    <select id="lastListByTypeId"
            resultMap="ApprovalTypeStepMappingRespResultMap">
        SELECT
        atsm.id stepMappingId, atsm.type_id, atsm.step_no, atsm.step_seq, atsm.version, atsm.is_send_todo_task as
        sendTodoTask,
        atsm.is_send_notice_msg as sendNoticeMsg, atsm.msg_config_id, atsm.send_user_codes, org_id,
        atsm.remind_expression, atsm.user_chose_type
        FROM approval_type_step_mapping atsm
        INNER JOIN (
        SELECT type_id, MAX(version) AS max_version
        FROM approval_type_step_mapping
        WHERE update_at &lt; #{lastUpdateTime}
        GROUP BY type_id
        ) as max_versions
        ON atsm.type_id = max_versions.type_id AND atsm.version = max_versions.max_version
        WHERE atsm.deleted = 0 and atsm.type_id = #{typeId}
        order by atsm.step_seq
    </select>

    <select id="maxVersionByTypeId" resultType="java.lang.Integer">
        SELECT MAX(version) AS max_version
        FROM approval_type_step_mapping
        where deleted = 0 and type_id = #{typeId}
        GROUP BY type_id
    </select>
</mapper>