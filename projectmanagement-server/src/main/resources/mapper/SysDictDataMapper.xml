<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.cscec3b.iti.projectmanagement.server.mapper.SysDictDataMapper">
  <resultMap id="BaseResultMap" type="com.cscec3b.iti.projectmanagement.server.entity.SysDictData">
    <!--@mbg.generated-->
    <!--@Table sys_dict_data-->
    <id column="dict_code" jdbcType="BIGINT" property="dictCode" />
    <result column="dict_sort" jdbcType="INTEGER" property="dictSort"/>
    <result column="dict_label" jdbcType="VARCHAR" property="dictLabel" />
    <result column="dict_value" jdbcType="VARCHAR" property="dictValue" />
    <result column="parent_code" jdbcType="BIGINT" property="parentCode"/>
    <result column="dict_type" jdbcType="VARCHAR" property="dictType" />
    <result column="is_default" jdbcType="TINYINT" property="byDefault"/>
    <result column="deleted" jdbcType="BIGINT" property="deleted" />
    <result column="create_by" jdbcType="VARCHAR" property="createBy" />
    <result column="create_at" jdbcType="BIGINT" property="createAt" />
    <result column="update_by" jdbcType="VARCHAR" property="updateBy" />
    <result column="update_at" jdbcType="BIGINT" property="updateAt" />
    <result column="remark" jdbcType="VARCHAR" property="remark" />
    <result column="level" jdbcType="INTEGER" property="level"/>
  </resultMap>
  <sql id="Base_Column_List">
    <!--@mbg.generated-->
    dict_code, dict_sort, dict_label, dict_value, parent_code, dict_type, is_default,
    deleted, create_by, create_at, update_by, update_at, remark, `level`
  </sql>

  <delete id="physicalDeleteByType">
    delete from sys_dict_data
    where dict_type = #{dictType}
  </delete>
</mapper>