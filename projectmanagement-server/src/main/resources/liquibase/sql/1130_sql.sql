alter table project_flow_event_record
    add is_manual tinyint unsigned default 0 null comment '是否手动触发: 0: 系统触发; 1: 手动触发;';

alter table project_event_receive_record
    add api_result tinyint unsigned default 1 null comment '处理结果: 0: 失败; 1: 成功;';

alter table project_event_receive_record
    add err_msg varchar(2048) null comment '错误信息';

alter table project_event_receive_record
    modify pull_result json null comment '反查结果';


-- auto-generated definition
create table id_table
(
    prefix   varchar(32)      not null comment '前缀',
    cur_date varchar(16)      not null comment '日期',
    seq      bigint default 1 not null comment '序列',
    primary key (prefix, cur_date),
    constraint id_table_prefix_IDX
        unique (prefix, cur_date)
)
    comment 'id记录表';

-- 发版时先执行，seq 从 100 开始, 防止与已有数据冲突
insert into id_table (prefix, cur_date, seq)
values ('P', date_format(now(), '%Y%m%d'), 100);

-- project_management.smartsite_segment_data definition

CREATE TABLE `business_system_data_smartsite`
(
    `project_id`             bigint(20) unsigned NOT NULL COMMENT '项目中心项目id',
    `yunshu_org_id`          varchar(64)   DEFAULT NULL COMMENT '云枢组织id，与智慧工地项目部组织树同步',
    `engineer_parameter`     varchar(2000) DEFAULT NULL COMMENT '工程参数json',
    `is_edge_small`          tinyint(3)    DEFAULT NULL COMMENT '是否边小远散项目',
    `is_ecology_sensitive`   tinyint(3)    DEFAULT NULL COMMENT '是否生态敏感区项目',
    `real_enter_time`        bigint(20)    DEFAULT NULL COMMENT '实际进场日期',
    `real_work_begin_time`   bigint(20)    DEFAULT NULL COMMENT '实际开工日期',
    `worker_end_time`        bigint(20)    DEFAULT NULL COMMENT '合同竣工日期',
    `record_date`            bigint(20)    DEFAULT NULL COMMENT '竣工备案日期',
    `real_open_traffic_time` bigint(20)    DEFAULT NULL COMMENT '五方主体验收日期（实际通车时间）',
    `work_end_time`          bigint(20)    DEFAULT NULL COMMENT '实际竣工日期',
    `project_status_eng`     varchar(16)   DEFAULT NULL COMMENT '项目状态(工程): 00:开工准备; 01:在施; 02:完工; 03:竣工; 04:销项; 0199:停工; 0399:质保;',
    `project_type`           varchar(128)  DEFAULT NULL COMMENT '工程类型(总公司综合口径)',
    `smart_project_address`  varchar(360)  DEFAULT NULL COMMENT '项目地址',
    `project_scale`          varchar(255)  DEFAULT NULL COMMENT '项目规模',
    `smart_contract_model`   varchar(255)  DEFAULT NULL COMMENT '承包模式（智慧工地）',
    `lng`                    varchar(100)  DEFAULT NULL COMMENT '经度',
    `lat`                    varchar(100)  DEFAULT NULL COMMENT '纬度',
    `quality_task`           text COMMENT '质量目标',
    `security_task`          text COMMENT '安全任务',
    `project_a8no`           varchar(50)   DEFAULT NULL COMMENT 'A8项目编码',
    `contract_start_date`    bigint(20)    DEFAULT NULL COMMENT '合同开工日期',
    `contract_end_date`      bigint(20)    DEFAULT NULL COMMENT '合同竣工日期',
    `yunshu_tree_id`         varchar(255)  DEFAULT NULL COMMENT '项目部云枢treeid',
    `yunshu_query_code`      text COMMENT '智慧工地项目组织queryCode',
    `yunshu_parent_org_id`   varchar(255)  DEFAULT NULL COMMENT '智慧工地项目直接上级云枢组织ID',
    `yunshu_parent_org_name` varchar(255)  DEFAULT NULL COMMENT '智慧工地项目直接上级全称',
    `yunshu_parent_tree_id`  varchar(255)  DEFAULT NULL COMMENT '智慧工地项目直接上级treeID',
    PRIMARY KEY (`project_id`),
    UNIQUE KEY `smartsite_segment_data_un` (`yunshu_org_id`)
) ENGINE = InnoDB
  DEFAULT CHARSET = utf8mb4 COMMENT ='智慧工地板块业务系统数据';

CREATE TABLE `business_system_data_change_approval_config`
(
    `id`                      bigint(20) unsigned NOT NULL AUTO_INCREMENT COMMENT '主键ID',
    `field_id`                varchar(128)                 DEFAULT NULL COMMENT '字段ID',
    `scope_type`              varchar(100)                 DEFAULT NULL COMMENT '板块信息: smart_site_segment: 智慧工地',
    `field_name`              varchar(32)         NOT NULL COMMENT '字段名',
    `raw_field_content`       varchar(1024)                DEFAULT '*' COMMENT '字段原始值',
    `target_field_content`    varchar(1024)       NOT NULL DEFAULT '*' COMMENT '字段目标值',
    `process_type`            tinyint(3) unsigned NOT NULL DEFAULT '0' COMMENT '流程类型：0：无需审批；1：简易审批；2：云枢审批流；',
    `proc_def_id`             varchar(64)                  DEFAULT NULL COMMENT '流程定义id',
    `execute_unit_org_id`     varchar(64)         NOT NULL COMMENT '执行单位组织id',
    `execute_unit_query_code` varchar(1024)       NOT NULL COMMENT '执行单位queryCode',
    `deleted`                 bigint(20) unsigned          DEFAULT '0' COMMENT '是否删除： 0：未删除； 时间戳表示删除时间',
    `create_at`               bigint(20) unsigned NOT NULL COMMENT '创建时间',
    `create_by`               varchar(64)         NOT NULL COMMENT '创建人',
    `update_at`               bigint(20) unsigned NOT NULL COMMENT '更新时间',
    `update_by`               varchar(64)         NOT NULL COMMENT '更新人',
    `scope_type_name` varchar(32) DEFAULT NULL COMMENT '业务业务板块名称',
    PRIMARY KEY (`id`),
    UNIQUE KEY `config_scope_type_field_id_execute_unit_org_id_uindex` (`scope_type`, `field_id`, `execute_unit_org_id`)
) ENGINE = InnoDB
  AUTO_INCREMENT = 0
  DEFAULT CHARSET = utf8mb4 COMMENT ='业务系统字段数据变更审批配置表';


CREATE TABLE `business_system_data_approval_form`
(
    `id`                    varchar(64)         NOT NULL COMMENT '主键id',
    `raw_field_content`     varchar(1024)                DEFAULT '*' COMMENT '字段原始值',
    `target_field_content`  varchar(1024)       NOT NULL DEFAULT '*' COMMENT '字段目标值',
    `create_at`             bigint(20) unsigned NOT NULL COMMENT '创建时间',
    `deleted`               bigint(20) unsigned NOT NULL DEFAULT '0' COMMENT '是否删除：0：已删除；时间戳为删除时间',
    `project_id`            bigint(20) unsigned NOT NULL COMMENT '项目id',
    `field_id`              varchar(127)        NOT NULL COMMENT '变更字段名',
    `current_field_content` varchar(1024)                DEFAULT NULL COMMENT '主数据表中当前字段的值',
    `business_data_type`    varchar(100)        NOT NULL COMMENT '板块信息: smart_site_segment: 智慧工地',
    PRIMARY KEY (`id`)
) ENGINE = InnoDB
  DEFAULT CHARSET = utf8mb4 COMMENT ='业务板块数据变更审批表单';

CREATE TABLE `wf_approval`
(
    `proc_inst_id`  varchar(64)         NOT NULL COMMENT 'id',
    `belong_id`     varchar(64)         NOT NULL COMMENT '所属id(表单id)',
    `scope_type`    varchar(31)         NOT NULL COMMENT '作用域,表单类型，业务板块枚举',
    `process_type`  tinyint(1) unsigned NOT NULL DEFAULT '0' COMMENT '流程类型：0：无需审批；1：简易审批；2：云枢审批流；',
    `proc_def_id`   varchar(127)                 DEFAULT NULL COMMENT '流程模板id',
    `status`        tinyint(2)                   DEFAULT NULL COMMENT '流程状态',
    `pc_url`        varchar(511)                 DEFAULT NULL COMMENT '电脑代办url',
    `mobile_url`    varchar(511)                 DEFAULT NULL COMMENT '手机代办url',
    `project_id`    bigint(20) unsigned NOT NULL COMMENT '工程编码',
    `real_name`     varchar(128)                 DEFAULT NULL COMMENT '发起人名称',
    `remark`        varchar(1024)                DEFAULT NULL COMMENT '描述',
    `org_name`      varchar(255)                 DEFAULT NULL COMMENT '组织名称',
    `department_id` varchar(64)                  DEFAULT NULL COMMENT '部门Id',
    `instance_id`   varchar(64)                  DEFAULT NULL COMMENT '应用实例Id',
    `create_by`     varchar(32)                  DEFAULT NULL COMMENT '创建人',
    `create_at`     bigint(20) unsigned          DEFAULT NULL COMMENT '添加时间',
    `update_by`     varchar(32)                  DEFAULT NULL COMMENT '更新人',
    `update_at`     bigint(20) unsigned          DEFAULT NULL COMMENT '修改时间',
    `deleted`       bigint(20) unsigned NOT NULL DEFAULT '0' COMMENT '是否删除 0未删除；其他为删除时间戳',
    PRIMARY KEY (`proc_inst_id`) USING BTREE,
    UNIQUE KEY `udx_belong_id` (`belong_id`, `scope_type`) USING BTREE,
    KEY `idx_create_person` (`create_by`) USING BTREE
) ENGINE = InnoDB
  DEFAULT CHARSET = utf8mb4
  ROW_FORMAT = COMPACT COMMENT ='审批映射关系';

CREATE TABLE `wf_approval_done`
(
    `id`              bigint(20)  NOT NULL AUTO_INCREMENT COMMENT 'id',
    `type`            varchar(31)          DEFAULT NULL COMMENT 'init发起 submit审批reject驳回',
    `belong_id`       bigint(20)           DEFAULT NULL COMMENT '所属Id',
    `scope_type`      varchar(31) NOT NULL COMMENT '作用域',
    `proc_inst_id`    varchar(64)          DEFAULT NULL COMMENT '流程实例Id',
    `username`        varchar(63)          DEFAULT NULL COMMENT '用户账号',
    `real_name`       varchar(127)         DEFAULT NULL COMMENT '用户名称',
    `project_id`      bigint(20) unsigned  DEFAULT NULL COMMENT '项目id',
    `remark`          varchar(1024)        DEFAULT NULL COMMENT '备注',
    `approval_type`   tinyint(4)  NOT NULL DEFAULT '0' COMMENT ' 0不通过，1通过，2结束',
    `department_id`   varchar(64)          DEFAULT NULL COMMENT '部门Id',
    `department_name` varchar(255)         DEFAULT NULL COMMENT '部门名称',
    `instance_id`     varchar(63)          DEFAULT NULL COMMENT '应用实例Id',
    `create_by`       varchar(32)          DEFAULT NULL COMMENT '创建人',
    `create_at`       bigint(20) unsigned  DEFAULT NULL COMMENT '添加时间',
    `update_by`       varchar(32)          DEFAULT NULL COMMENT '更新人',
    `update_at`       bigint(20) unsigned  DEFAULT NULL COMMENT '修改时间',
    `deleted`         bigint(20)  NOT NULL DEFAULT '0' COMMENT '是否删除 0未删除；时间戳为删除时间',
    PRIMARY KEY (`id`) USING BTREE,
    KEY `idx_flow_id` (`proc_inst_id`) USING BTREE,
    KEY `idx_username` (`username`) USING BTREE,
    KEY `idx_belong_scope` (`belong_id`, `scope_type`) USING BTREE
) ENGINE = InnoDB
  DEFAULT CHARSET = utf8mb4
  ROW_FORMAT = COMPACT COMMENT ='已办记录';

CREATE TABLE `wf_approval_log`
(
    `id`          bigint(20)  NOT NULL AUTO_INCREMENT COMMENT 'id',
    `type`        varchar(31)          DEFAULT NULL COMMENT 'init发起 submit审批reject驳回',
    `belong_id`   bigint(20)           DEFAULT NULL COMMENT '所属Id',
    `scope_type`  varchar(31) NOT NULL COMMENT '作用域',
    `username`    varchar(63)          DEFAULT NULL COMMENT '用户账号',
    `response`    varchar(4096)        DEFAULT NULL COMMENT '响应',
    `param`       varchar(4096)        DEFAULT NULL COMMENT '入参',
    `tenant_id`   varchar(255)         DEFAULT NULL COMMENT '租户id',
    `instance_id` bigint(20)           DEFAULT NULL COMMENT '应用实例Id',
    `create_by`   varchar(32)          DEFAULT NULL COMMENT '创建人',
    `create_at`   bigint(20) unsigned  DEFAULT NULL COMMENT '添加时间',
    `update_by`   varchar(32)          DEFAULT NULL COMMENT '更新人',
    `update_at`   bigint(20) unsigned  DEFAULT NULL COMMENT '修改时间',
    `deleted`     bigint(20)  NOT NULL DEFAULT '0' COMMENT '是否删除 0未删除；时间戳为删除时间',
    PRIMARY KEY (`id`) USING BTREE
) ENGINE = InnoDB
  DEFAULT CHARSET = utf8mb4
  ROW_FORMAT = COMPACT COMMENT ='审批日志';

CREATE TABLE `wf_approvaler`
(
    `id`                 bigint(20) NOT NULL COMMENT '主键id',
    `proc_inst_id`       varchar(127)        DEFAULT NULL COMMENT '流程实例id',
    `approvaler_name`    varchar(64)         DEFAULT NULL COMMENT '审批人名称',
    `approvaler_account` varchar(64)         DEFAULT NULL COMMENT '审批人账号',
    `approval_time`      bigint(20)          DEFAULT NULL COMMENT '审批时间',
    `approvaler_mobile`  varchar(64)         DEFAULT NULL COMMENT '审批人手机',
    `approvaler_dept_id` varchar(64)         DEFAULT NULL COMMENT '审批人部门',
    `remark`             varchar(256)        DEFAULT NULL COMMENT '备注',
    `create_at`          bigint(20) unsigned DEFAULT NULL COMMENT '创建时间',
    `update_at`          bigint(20) unsigned DEFAULT NULL COMMENT '更新时间',
    `create_by`          varchar(32)         DEFAULT NULL COMMENT '创建人',
    `update_by`          varchar(32)         DEFAULT NULL COMMENT '更新人',
    `deleted`            bigint(20) unsigned DEFAULT NULL COMMENT '是否逻辑删除 0：未删除；时间戳为删除时间',
    PRIMARY KEY (`id`)
) ENGINE = InnoDB
  DEFAULT CHARSET = utf8mb4 COMMENT ='审批人记录表';

alter table project
    add effect_pic varchar(512) null comment '项目效果图';

