package com.cscec3b.iti.cpm.service;

import com.cscec3b.iti.model.req.ProjectFlowEventNoticeReq;
import com.cscec3b.iti.model.resp.open.ProjectAssociationStatusResp;

import java.util.List;

/**
 * 项目档案接口
 *
 * <AUTHOR>
 * @date 2023/09/08 12:08
 **/

public interface IProjectArchiveService {


	/**
	 * 项目档案通知
	 *
	 * @param noticeReq 通知参数
	 * @return boolean
	 */
	boolean notice(ProjectFlowEventNoticeReq noticeReq);

	/**
	 * api鉴权开关
	 *
	 * @return boolean
	 * <AUTHOR>
	 * @date 2023/10/19
	 */
	default boolean enableApiAuth() {
		return true;
	}

    /**
     * 获取项目关联状态
     *
     * @param cpmProjectKey CPM 项目密钥
     * @return {@link List }<{@link ProjectAssociationStatusResp }>
     */
    List<ProjectAssociationStatusResp> associationStatus(String cpmProjectKey);
}
