package com.cscec3b.iti.cpm.enums;

import java.util.Arrays;

import com.cscec3b.iti.cpm.exception.FrameworkException;

import lombok.Getter;

/**
 * 项目事件节点数据操作枚举
 *
 * <AUTHOR>
 * @date 2023/09/14 19:31
 **/

@Getter
public enum FlowNodeDataTypeEnum {

    CREATE("立项", "create", "项目立项"),

    UPDATE("更新", "update", "项目更新");

    /**
     * 节点名称
     */
    final String name;

    /**
     * 编码
     */
    final String code;

    /**
     * 描述
     */
    final String description;

    FlowNodeDataTypeEnum(String name, String code, String description) {
        this.name = name;
        this.code = code;
        this.description = description;
    }

    public static FlowNodeDataTypeEnum getByCode(String code) {
        return Arrays.stream(values()).filter(dataType -> dataType.getCode().equals(code)).findFirst()
            .orElseThrow(() -> new FrameworkException(-1, "项目更新类型不正确"));
    }

}
