
package com.cscec3b.iti.projectmanagement.api.dto.request.project;

import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;
import lombok.experimental.Accessors;

import javax.validation.constraints.NotNull;
import javax.validation.constraints.Size;
import java.io.Serializable;

/**
 * @description 项目请求数据
 * <AUTHOR>
 * @date 2022/11/01
 */
@Data
@Accessors(chain = true)
@ApiModel(value = "ProjectReq", description = "项目请求数据")
public class ProjectReq implements Serializable {

    private static final long serialVersionUID = 1L;

    @NotNull(message = "项目id不能为空")
    @ApiModelProperty(value = "项目id", required = true)
    private Long projectId;

    @NotNull(message = "任务id不能为空")
    @ApiModelProperty(value = "任务id", required = true)
    private Long taskId;

    @ApiModelProperty(value = "项目级别(局重点、公司重点、其他)")
    private String projectLevel;

    @ApiModelProperty(value = "项目级别code")
    private String projectLevelCode;

    @ApiModelProperty(value = "是否生态敏感区项目(是/否)")
    private Integer isEcologySensitive;

    @ApiModelProperty(value = "是否边小远散项目(是/否)")
    private Integer isEdgeSmall;

    @ApiModelProperty(value = "实际开工日期")
    private Long realWorkBeginTime;

    @ApiModelProperty(value = "预计实际竣工日期")
    private Long predictWorkEndTime;

    @ApiModelProperty(value = "实际进场日期")
    private Long realEnterTime;

    @ApiModelProperty(value = "实际竣工日期")
    private Long workEndTime;

    @ApiModelProperty(value = "五方主体验收日期（实际通车时间）")
    private Long realOpenTrafficTime;

    @ApiModelProperty(value = "建设单位（甲方）联系人")
    @Size(max = 255, message = "建设单位（甲方）联系人长度不能超过{max}个字符")
    private String contactPerson;

    @ApiModelProperty(value = "建设单位（甲方）联系人电话")
    @Size(max = 64, message = "建设单位（甲方）联系人电话长度不能超过{max}个字符")
    private String contactPersonMobile;

    @ApiModelProperty(value = "现场业主代表姓名")
    @Size(max = 50, message = "现场业主代表姓名长度不能超过50个字符")
    private String sceneOwnerRepresentName;

    @ApiModelProperty(value = "现场业主代表职务")
    @Size(max = 50, message = "现场业主代表职务长度不能超过50个字符")
    private String sceneOwnerRepresentDuty;

    @ApiModelProperty(value = "现场业主代表联系电话")
    @Size(max = 50, message = "现场业主代表联系电话长度不能超过50个字符")
    private String sceneOwnerRepresentPhone;

    @ApiModelProperty(value = "工程参数json")
    private String engineerParameter;

    @ApiModelProperty(value = "项目状态")
    private Integer projectStatus;

    @ApiModelProperty(value = "更新时间")
    private Long updateAt;
}
