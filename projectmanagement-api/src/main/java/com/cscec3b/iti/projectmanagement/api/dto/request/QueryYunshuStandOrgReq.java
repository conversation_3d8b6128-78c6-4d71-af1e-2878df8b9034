package com.cscec3b.iti.projectmanagement.api.dto.request;

import java.io.Serializable;

import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;
import lombok.experimental.Accessors;

/**
 * 云枢标准组织映射关系表
 */
@ApiModel(description = "云枢标准组织映射关系表")
@Data
@Accessors(chain = true)
public class QueryYunshuStandOrgReq extends BasePage implements Serializable {

    /**
     * 云枢组织名称
     */
    @ApiModelProperty(value = "云枢组织名称")
    private String yunshuExecuteUnit;

    /**
     * 云枢组织id
     */
    @ApiModelProperty(value = "云枢组织id")
    private String yunshuExecuteUnitId;

    /**
     * 云枢组织编码
     */
    @ApiModelProperty(value = "云枢组织编码")
    private String yunshuExecuteUnitCode;

    /**
     * 云枢组织idPath
     */
    @ApiModelProperty(value = "云枢组织idPath")
    private String yunshuExecuteUnitIdPath;

    /**
     * 标准组织
     */
    @ApiModelProperty(value = "标准组织")
    private String standUnit;

    /**
     * 标准组织id
     */
    @ApiModelProperty(value = "标准组织id")
    private String standUnitId;

    /**
     * 标准组织编码
     */
    @ApiModelProperty(value = "标准组织编码")
    private String standUnitCode;

    /**
     * 标准组织idPath
     */
    @ApiModelProperty(value = "标准组织idPath")
    private String standUnitIdPath;

    /**
     * 标准组织简称
     */
    @ApiModelProperty(value = "标准组织简称")
    private String standUnitAbbreviation;
}
