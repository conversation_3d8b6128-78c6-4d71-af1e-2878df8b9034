package com.cscec3b.iti.projectmanagement.api.dto.response;

import com.cscec3b.iti.projectmanagement.api.dto.response.project.BidSummaryResp;
import com.cscec3b.iti.projectmanagement.api.dto.response.project.BureauContractResp;
import com.cscec3b.iti.projectmanagement.api.dto.response.project.BureauSupplementaryAgreementResp;
import com.cscec3b.iti.projectmanagement.api.dto.response.project.SupplementaryAgreementResp;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

import java.io.Serializable;

/**
 * 文件详情
 *
 * <AUTHOR>
 * @date 2024/01/12
 */
@Data
@ApiModel(value = "合同文件详情")
public class ContractFileDetailResp<T> implements Serializable {

    /**
     * 文件类型
     */
    @ApiModelProperty(value = "文件类型")
    private String scopeType;

    private T data;

    /**
     * 投标总结
     */
    @ApiModelProperty(value = "投标总结详情")
    private BidSummaryResp bidSummaryResp;


    /**
     * 补充协议/无合同续签补充协议
     */
    @ApiModelProperty(value = "补充协议/无合同续签补充协议详情")
    private SupplementaryAgreementResp supplementaryAgreementResp;


    /**
     * 局内分包合同详情
     */
    @ApiModelProperty(value = "局内分包合同详情")
    private BureauContractResp bureauContractResp;

    /**
     * 局内补充协议/局内无合同续签补充协议详情
     */
    @ApiModelProperty(value = "局内补充协议/局内无合同续签补充协议详情")
    private BureauSupplementaryAgreementResp bureauSupplementaryAgreementResp;

    public ContractFileDetailResp() {
    }

    public ContractFileDetailResp(T data) {
        this.data = data;
    }
}
