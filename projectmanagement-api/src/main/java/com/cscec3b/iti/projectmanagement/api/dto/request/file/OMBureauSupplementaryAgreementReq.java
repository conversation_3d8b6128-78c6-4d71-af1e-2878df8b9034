package com.cscec3b.iti.projectmanagement.api.dto.request.file;

import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;
import lombok.experimental.Accessors;

import javax.validation.constraints.NotNull;
import java.math.BigDecimal;

/**
 * 市场营销-局内部补充协议合同表
 */
@ApiModel(description = "市场营销-局内部补充协议合同表")
@Data
@Accessors(chain = true)
public class OMBureauSupplementaryAgreementReq {
    /**
     * 主键id
     */
    @ApiModelProperty(value = "主键id")
    @NotNull(message = "id不能为空")
    private Long id;

    /**
     * 发起人单位
     */

    @ApiModelProperty(value = "发起人单位")
    private String submitPerson;

    /**
     * 补充协议编号
     */

    @ApiModelProperty(value = "补充协议编号")
    private String agreementCode;

    /**
     * 工程名称
     */

    @ApiModelProperty(value = "工程名称")
    private String projectName;

    /**
     * 工程属地
     */

    @ApiModelProperty(value = "工程属地")
    private String projectBelong;

    /**
     * 是否局重点项目
     */

    @ApiModelProperty(value = "是否局重点项目")
    private String bureauProject;

    /**
     * 是否授权外
     */

    @ApiModelProperty(value = "是否授权外")
    private String mandateForeign;

    /**
     * 客户名称
     */

    @ApiModelProperty(value = "客户名称")
    private String customerName;

    /**
     * 上级相关方/客户母公司
     */

    @ApiModelProperty(value = "上级相关方/客户母公司")
    private String superiorCompanyName;

    /**
     * 客户企业性质
     */

    @ApiModelProperty(value = "客户企业性质")
    private String enterpriseType;

    /**
     * 建设单位（甲方）联系人
     */

    @ApiModelProperty(value = "建设单位（甲方）联系人")
    private String contactPerson;

    /**
     * 合同经办人
     */

    @ApiModelProperty(value = "合同经办人")
    private String contractResponsiblePerson;

    /**
     * 是否纳入公司考核指标
     */

    @ApiModelProperty(value = "是否纳入公司考核指标")
    private String companyAssessmentIndicators;

    /**
     * 业务类型
     */

    @ApiModelProperty(value = "业务类型")
    private String businessType;

    /**
     * 补充协议金额
     */

    @ApiModelProperty(value = "补充协议金额 ")
    private BigDecimal supplementAmount;

    /**
     * 计价方式
     */

    @ApiModelProperty(value = "计价方式")
    private String pricingMethod;

    /**
     * 合同形式
     */

    @ApiModelProperty(value = "合同形式")
    private String contractForm;

    /**
     * 人工费是否可调
     */

    @ApiModelProperty(value = "人工费是否可调")
    private String costOfLaborChange;

    /**
     * 主材费是否可调
     */

    @ApiModelProperty(value = "主材费是否可调")
    private String costOfLaborChange2;

    /**
     * 是否有预付款
     */

    @ApiModelProperty(value = "是否有预付款")
    private String advancesFlag;

    /**
     * 进度款付款方式
     */

    @ApiModelProperty(value = "进度款付款方式")
    private String advancesWay;

    /**
     * 月进度付款比例
     */

    @ApiModelProperty(value = "月进度付款比例")
    private String advancesMonthRate;

    /**
     * 竣工验收支付比例
     */

    @ApiModelProperty(value = "竣工验收支付比例")
    private String completedRate;

    /**
     * 竣工验收收款周期（月）
     */

    @ApiModelProperty(value = "竣工验收收款周期（月）")
    private String completedCycle;

    /**
     * 结算支付比例
     */

    @ApiModelProperty(value = "结算支付比例")
    private String settlementRate;

    /**
     * 结算周期（月）
     */

    @ApiModelProperty(value = "结算周期（月）")
    private String settlementCycle;

    /**
     * 保修金
     */

    @ApiModelProperty(value = "保修金")
    private String warrantyPremium;

    /**
     * 保修金比例
     */

    @ApiModelProperty(value = "保修金比例")
    private String warrantyPremiumRate;

    /**
     * 保修金支付方式
     */

    @ApiModelProperty(value = "保修金支付方式")
    private String warrantyPremiumWay;

    /**
     * 支付方式
     */

    @ApiModelProperty(value = "支付方式")
    private String payTypeNew;

    /**
     * 现金支付方式
     */

    @ApiModelProperty(value = "现金支付方式")
    private String specificPayWay;

    /**
     * 是否垫资
     */

    @ApiModelProperty(value = "是否垫资")
    private String advancesFundFlag;

    /**
     * 履约担保方式
     */

    @ApiModelProperty(value = "履约担保方式")
    private String guaranteeWay;

    /**
     * 补充协议文本
     */

    @ApiModelProperty(value = "补充协议文本")
    private String agreementUrl;

    /**
     * 合同主要条款对比表
     */

    @ApiModelProperty(value = "合同主要条款对比表")
    private String contractTermUrl;

    /**
     * 法律意见书
     */

    @ApiModelProperty(value = "法律意见书")
    private String lawUrl;

    /**
     * 局内补充协议编号
     */

    @ApiModelProperty(value = "局内补充协议编号")
    private String bureauSupplementaryAgreementCode;

    /**
     * 独立合同ID
     */

    @ApiModelProperty(value = "独立合同ID")
    private Long independentContractId;

    /**
     * 独立合同类型：1投标总结；2合同定案；3补充协议；4局内部合同定案；5局内部补充协议；
     */

    @ApiModelProperty(value = "独立合同类型：1投标总结；2合同定案；3补充协议；4局内部合同定案；5局内部补充协议；")
    private Integer independentContractType;

    /**
     * 所属源文件id
     */

    @ApiModelProperty(value = "所属源文件id")
    private Long belongId;

    /**
     * 客户级别
     */

    @ApiModelProperty(value = "客户级别")
    private String customerLevel;

    /**
     * 建设单位（甲方）联系人电话
     */

    @ApiModelProperty(value = "建设单位（甲方）联系人电话")
    private String contactPersonMobile;

    /**
     * 突破底线条款
     */

    @ApiModelProperty(value = "突破底线条款")
    private String breakBottom;

    /**
     * 文件类型：5：局内部补充协议； 50：局内部无合同续签补充协议
     */

    @ApiModelProperty(value = "文件类型：5：局内部补充协议； 50：局内部无合同续签补充协议")
    private Integer belongFileType;

    /**
     * 云枢执行单位名称
     */

    @ApiModelProperty(value = "云枢执行单位名称")
    private String yunshuExecuteUnit;

    /**
     * 云枢执行单位code
     */

    @ApiModelProperty(value = "云枢执行单位code")
    private String yunshuExecuteUnitCode;

    /**
     * 云枢执行单位Id
     */

    @ApiModelProperty(value = "云枢执行单位Id")
    private String yunshuExecuteUnitId;

    /**
     * 云枢执行单位id_path
     */

    @ApiModelProperty(value = "云枢执行单位id_path")
    private String yunshuExecuteUnitIdPath;

    /**
     * 前置文件类型
     */

    @ApiModelProperty(value = "前置文件类型")
    private Integer preFileType;

    /**
     * 前置文件id
     */

    @ApiModelProperty(value = "前置文件id")
    private Long preFileId;

    /**
     * 创建时间
     */

    @ApiModelProperty(value = "创建时间")
    private Long createAt;

    /**
     * 更新时间
     */

    @ApiModelProperty(value = "更新时间")
    private Long updateAt;

    /**
     * 来源：0：中标未项(市场营销 ); 1: 市场营销(合同管理)
     */

    @ApiModelProperty(value = "来源：0：中标未项(市场营销 ); 1: 市场营销(合同管理)")
    private Integer source;

    /**
     * 独立性判断,Y:是独立,N:否
     */

    @ApiModelProperty(value = "独立性判断,Y:是独立,N:否")
    private String independent;

}