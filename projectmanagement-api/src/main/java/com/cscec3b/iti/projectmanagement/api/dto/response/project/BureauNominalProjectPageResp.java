package com.cscec3b.iti.projectmanagement.api.dto.response.project;

import java.io.Serializable;

import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

/**
 * <AUTHOR>
 * @description BureauNominalProjectPageResp
 * @date 2023/08/01 14:45
 */
@Data
@ApiModel(value = "局名义项目管理列表")
public class BureauNominalProjectPageResp implements Serializable {
    /**
     * 项目中心项目id
     */
    @ApiModelProperty("项目中心项目id")
    private Long projectId;

    /**
     * 财商编码
     */
    @ApiModelProperty("项目的财商编码")
    private String projectFinanceCode;
    /**
     * 财商名称
     */
    @ApiModelProperty("项目的财商名称")
    private String projectFinanceName;
    /**
     * 财商简称
     */
    @ApiModelProperty("项目的财商简称")
    private String projectFinanceAbbreviation;
    /**
     * 项目部云枢id
     */
    @ApiModelProperty("项目部云枢id")
    private String yunshuOrgId;
    /**
     * 执行单位
     */
    @ApiModelProperty("云枢执行单位")
    private String yunshuExecuteUnit;
    /**
     * 执行单位id
     */
    @ApiModelProperty("云枢执行单位id")
    private String yunshuExecuteUnitId;
    /**
     * 执行单位querycode
     */
    @ApiModelProperty("云枢执行单位path")
    private String yunshuExecuteUnitIdPath;

    /**
     * 项目局名义类型（1:总包; 2:分包）
     */
    @ApiModelProperty("项目局名义类型（1:总包; 2:分包）")
    private int bureauNominalProjectType;

    /**
     * 局名义总包项目id
     */
    @ApiModelProperty("局名义总包项目id")
    private Long generalContractProjectId;

    /**
     * 局名义总包项目名称
     */
    @ApiModelProperty("局名义总包项目名称")
    private String generalContractProjectName;

    /**
     * 来源系统
     */
    @ApiModelProperty("来源系统")
    private String sourceSystem;

    /**
     * 局名义总包来源系统
     */
    @ApiModelProperty("局名义总包来源系统")
    private String generalContractSourceSystem;

    /**
     * 云枢执行单位简称
     */
    @ApiModelProperty(value = "云枢执行单位简称")
    private String yunshuExecuteUnitAbbreviation;
    /**
     * 项目唯一标识
     */
    @ApiModelProperty(value = "项目唯一标识 含义：接收市场营销立项通知或特殊立项发起后生成.P+年月日+四位流水号")
    private String cpmProjectKey;
}
