package com.cscec3b.iti.projectmanagement.api.dto.response.open.v2;

import java.io.Serializable;

import com.fasterxml.jackson.annotation.JsonProperty;

import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;
import lombok.experimental.Accessors;

@Data
@Accessors(chain = true)
@ApiModel(value = "ProjectOpenHookQueryResp_v2.0", description = "项目挂接查询对外返回信息")
public class ProjectOpenHookQueryResp implements Serializable {

    private static final long serialVersionUID = 1L;

    @ApiModelProperty(value = "项目id")
    private Long projectId;

    @ApiModelProperty(value = "项目部所属组织编码")
    private String projectDeptId;

    @ApiModelProperty(value = "所属指挥部/项目部")
    private String projectDeptName;

    @ApiModelProperty(value = "项目编码")
    @JsonProperty(value = "projectCode")
    private String projectFinanceCode;

    @ApiModelProperty(value = "项目名称")
    @JsonProperty(value = "projectName")
    private String projectFinanceName;

    @ApiModelProperty(value = "项目简称（中文）")
    @JsonProperty(value = "projectNameAbbreviation")
    private String projectFinanceAbbreviation;

    @ApiModelProperty(value = "行政区域（地理位置）")
    private String region;

    @ApiModelProperty(value = "项目地址")
    private String projectAddress;

    @ApiModelProperty(value = "客户名称")
    private String customerName;

    @ApiModelProperty(value = "实际签约日期")
    private Long actualSignedTime;

    @ApiModelProperty(value = "云枢组织id")
    @JsonProperty(value = "projectOrgId")
    private String yunshuOrgId;

    @ApiModelProperty(value = "执行单位id")
    @JsonProperty(value = "implementationUnitId")
    private String executeUnitId;

    @ApiModelProperty(value = "执行单位")
    @JsonProperty(value = "implementationUnit")
    private String executeUnit;

    @ApiModelProperty(value = "云枢执行单位id")
    @JsonProperty(value = "projectOrgId")
    private String yunshuExecuteUnitId;

    @ApiModelProperty(value = "云枢执行单位")
    @JsonProperty(value = "projectOrg")
    private String yunshuExecuteUnit;

}
