package com.cscec3b.iti.projectmanagement.api.dto.response.open;

import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

import java.io.Serializable;

@Data
@ApiModel(value = "施工项目与工程项目映射信息")
public class OpenEngineeringProjectMappingResp implements Serializable {

    private static final long serialVersionUID = 1L;

    /**
     * 施工项目ID
     */
    @ApiModelProperty(value = "施工项目ID")
    private Long projectId;

    /**
     * 施工项目标识
     */
    @ApiModelProperty(value = "施工项目标识")
    private String cpmProjectKey;

    /**
     * 施工项目名称
     */
    @ApiModelProperty(value = "施工项目名称")
    private String cpmProjectName;

    /**
     * 财商项目编码
     */
    @ApiModelProperty(value = "财商项目编码")
    private String projectFinanceCode;

    /**
     * 是否主施工项目
     */
    @ApiModelProperty(value = "是否主施工项目")
    private Integer mainStandardProject;

    /**
     * 工程项目ID
     */
    @ApiModelProperty(value = "工程项目ID")
    private Long engineeringProjectId;

    /**
     * 工程项目标识
     */
    @ApiModelProperty(value = "工程项目标识")
    private String engineeringKey;

    /**
     * 工程项目名称
     */
    @ApiModelProperty(value = "工程项目名称")
    private String engineeringName;

    /**
     * 工程项目编码
     */
    @ApiModelProperty(value = "工程项目编码")
    private String engineeringCode;

}
