package com.cscec3b.iti.projectmanagement.api.dto.response.open;

import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

import java.io.Serializable;

@Data
@ApiModel("工程项目档案分页查询响应")
public class OpenEngineeringArchiveResp implements Serializable {

    /**
     * 工程项目ID
     */
    @ApiModelProperty(value = "工程项目ID")
    private Long engineeringProjectId;

    /**
     * 工程项目标识
     */
    @ApiModelProperty(value = "工程项目标识")
    private String engineeringKey;

    /**
     * 工程项目名称
     */
    @ApiModelProperty(value = "工程项目名称")
    private String engineeringName;

    /**
     * 工程项目编码
     */
    @ApiModelProperty(value = "工程项目编码")
    private String engineeringCode;

}
