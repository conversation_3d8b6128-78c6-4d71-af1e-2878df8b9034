package com.cscec3b.iti.projectmanagement.api.dto.dto;

import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;
import lombok.experimental.Accessors;

import java.io.Serializable;

@Data
@Accessors(chain = true)
@ApiModel(value = "通用枚举字典")
public class CommEnumDict implements Serializable {

    /**
     * 字典编码
     */
    @ApiModelProperty(value = "字典编码")
    private String dictCode;

    /**
     * 中文名称
     */
    @ApiModelProperty(value = "中文名称")
    private String zhCN;

    /**
     * 英文名称
     */
    @ApiModelProperty(value = "英文名称")
    private String enUS;

    /**
     * 编号
     */
    @ApiModelProperty(value = "编号")
    private String no;

    /**
     * 编码
     */
    @ApiModelProperty(value = "编码")
    private String code;

    /**
     * 名称
     */
    @ApiModelProperty(value = "名称")
    private String name;


    public CommEnumDict() {
    }

    public CommEnumDict(final String dictCode, final String zhCN, final String enUS) {
        this.dictCode = dictCode;
        this.zhCN = zhCN;
        this.enUS = enUS;
    }
}
