
package com.cscec3b.iti.projectmanagement.api.dto.request.project;

import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;
import lombok.experimental.Accessors;

import java.io.Serializable;
import java.util.List;

/**
* @description 智慧工地请求数据
* <AUTHOR>
* @date 2022/11/16
*/
@Data
@Accessors(chain = true)
@ApiModel(value = "SmartSiteReq", description = "智慧工地请求数据")
public class SmartSiteReq implements Serializable {

    private static final long serialVersionUID = 1L;

    @ApiModelProperty(value = "A8编码列表")
    private List<String> projectNos;

    @ApiModelProperty(value = "查询条件类型(1-默认A8编码)")
    private Integer schType;

}