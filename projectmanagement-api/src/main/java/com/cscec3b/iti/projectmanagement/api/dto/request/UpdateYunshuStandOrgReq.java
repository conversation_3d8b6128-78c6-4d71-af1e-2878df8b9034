package com.cscec3b.iti.projectmanagement.api.dto.request;

import java.io.Serializable;

import javax.validation.constraints.NotBlank;

import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;
import lombok.experimental.Accessors;

/**
 * 云枢标准组织映射关系表
 */
@ApiModel(description = "云枢标准组织映射关系表")
@Data
@Accessors(chain = true)
public class UpdateYunshuStandOrgReq extends YunshuStandOrgReq implements Serializable {

    /**
     * 云枢组织名称
     */
    @ApiModelProperty(value = "id")
    @NotBlank(message = "id")
    private long id;

}
