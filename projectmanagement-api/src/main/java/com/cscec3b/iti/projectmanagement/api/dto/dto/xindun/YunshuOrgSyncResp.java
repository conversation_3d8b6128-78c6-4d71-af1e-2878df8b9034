package com.cscec3b.iti.projectmanagement.api.dto.dto.xindun;

import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

/**
 * yunshu org同步
 * 云枢组织同步信息
 *
 * <AUTHOR>
 * @date 2023/08/23
 */

@Data
@ApiModel(description = "云枢实体组织信息")
public class YunshuOrgSyncResp {
    @ApiModelProperty(value = "treeId")
    private String id;

    /**
     * 云枢树queryCode
     */
    @ApiModelProperty(value = "云枢树queryCode")
    private String idPath;

    /**
     * 部门id
     */
    @ApiModelProperty(value = "部门id")
    private String deptId;

    /**
     * 部门code
     */
    @ApiModelProperty(value = "部门code")
    private String code;

    /**
     * 组织全称
     */
    @ApiModelProperty(value = "组织全称")
    private String name;

    /**
     * 组织简称
     */
    @ApiModelProperty(value = "组织简称")
    private String abbreviation;

    /**
     * 上级组织treeId
     */
    @ApiModelProperty(value = "上级组织treeId")
    private String parentId;

    /**
     * 是否叶子节点
     */
    @ApiModelProperty(value = "是否叶子节点")
    private Boolean leaf;

    /**
     * 组织类型: 10:局; 15:托管单位; 20:分局; 30:公司; 40:分公司; 45:专业公司; 47:城市公司; 50:项目经理部; 60:指挥部; 70:项目部; 80:机关; 90:部门;
     */
    @ApiModelProperty(value = "组织类型: 10:局; 15:托管单位; 20:分局; 30:公司; 40:分公司; 45:专业公司; 47:城市公司; 50:项目经理部; 60:指挥部; 70:项目部;" +
            " 80:机关; 90:部门; ")
    private String orgType;

    /**
     * 能否直接下设项目部或指挥部: 0:不可添加; 1:可以添加;
     */
    @ApiModelProperty(value = "能否直接下设项目部或指挥部: 0:不可添加; 1:可以添加;")
    private Byte isSetupSub;

    /**
     * 全路径名称
     */
    @ApiModelProperty(value = "全路径名称")
    private String idPathName;

    /**
     * 全路径简称
     */
    @ApiModelProperty(value = "全路径简称")
    private String idPathAbbreviation;

}
