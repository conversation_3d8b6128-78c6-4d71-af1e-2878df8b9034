package com.cscec3b.iti.projectmanagement.api.dto.request.event;

import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;
import lombok.experimental.Accessors;

import javax.validation.constraints.NotBlank;
import javax.validation.constraints.NotNull;
import javax.validation.constraints.Size;
import java.io.Serializable;

/**
 * <AUTHOR>
 * @description CreateSubscriberReq
 * @date 2023/04/18 14:15
 */
@Data
@Accessors(chain = true)
@ApiModel(value = "UpdateSubscriberReq", description = "更新系统订阅配置接口")
public class UpdateSubscriberReq implements Serializable {
    private static final long serialVersionUID = 1L;

    /**
     * 订阅系统id
     */
    @ApiModelProperty(value = "主键id")
    @NotNull(message = "主键id不能为空")
    private Long id;

    /**
     * 订阅系统id
     */
    @ApiModelProperty(value = "订阅系统id")
    private Long consumerId;

    /**
     * 订阅系统
     */
    @ApiModelProperty(value = "订阅系统")
    @NotBlank(message = "系统名称不能为空")
    @Size(max = 50,  message = "订阅系统名称长度必须小于50")
    private String subscriber;

    /**
     * 协议类型: http; https
     */
    @ApiModelProperty(value = "协议类型: http; https")
    private String protocol;

    /**
     * 证书
     */
    @ApiModelProperty(value = "证书")
//    @NotEmpty(message = "证书不能为空")
    private byte[] certificate;

    /**
     * 推送url
     */
    @ApiModelProperty(value = "推送url")
    private String pushUrl;

    /**
     * 事件类型：事件id,逗号分隔
     */
    @ApiModelProperty(value = "事件类型：事件id,逗号分隔")
    private String eventIds;

    /**
     * 更新人
     */
    @ApiModelProperty(value = "更新人")
    private String updateBy;

    /**
     * 系统负责人
     */
    @ApiModelProperty(value = "系统负责人")
    @Size(max = 10,  message = "系统负责人长度必须小于10")
    private String responsible;

    /**
     * 状态
     */
    @ApiModelProperty(value = "状态 0:删除; 1:正常")
    private int status;

    @ApiModelProperty(value = "系统标识")
    private String appCode;

    @ApiModelProperty(value = "appKey")
    private String appKey;

    @ApiModelProperty(value = "appSecret")
    private String appSecret;

    @ApiModelProperty(value = "拓展字段")
    private String extension;

    @ApiModelProperty(value = "云枢执行单位id")
    private String yunshuExecuteUnitId;

    @ApiModelProperty(value = "云枢执行单位")
    private String yunshuExecuteUnit;

    @ApiModelProperty(value = "云枢执行单位简称")
    private String yunshuExecuteUnitAbbreviation;

    @ApiModelProperty(value = "云枢执行单位idPath")
    private String yunshuExecuteUnitIdPath;

    @ApiModelProperty(value = "spi扩展实现类名称")
    private String spiClassName;

    /**
     * openApiKey,项目中心鉴权用，默认取神禹网关appkey
     */
    @ApiModelProperty(value = "openApiKey,项目中心鉴权用，默认取神禹网关appkey")
    @Size(max = 64, message = "openApiKey长度必须小于64")
    private String openApiKey;

    /**
     * openApiSecret,项目中心鉴权用，默认取神禹网关appsecret
     */
    @ApiModelProperty(value = "openApiSecret,项目中心鉴权用，默认取神禹网关appsecret")
    @Size(max = 64, message = "openApiSecret长度必须小于64")
    private String openApiSecret;
}
