package com.cscec3b.iti.projectmanagement.api.dto.request.approvalstep;

import com.cscec3b.iti.projectmanagement.api.dto.dto.AttachmentDto;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;
import org.hibernate.validator.constraints.Length;

import javax.validation.constraints.NotNull;
import java.io.Serializable;
import java.util.List;

@Data
@ApiModel("立项步骤注意事项请求参数")
public class ApprovalStepNoteReq implements Serializable {
    /**
     * 立项步骤id
     */
    @NotNull(message = "立项步骤不能为空")
    @ApiModelProperty(value = "立项步骤")
    private Integer stepNo;

    @Length(max = 1024, message = "注意事项内容长度不能超过1000")
    @ApiModelProperty(value = "注意事项内容")
    private String content;


    /**
     * 附件
     */
    @ApiModelProperty(value = "附件")
    private List<AttachmentDto> attachmentDtos;

}
