package com.cscec3b.iti.projectmanagement.api.dto.request;

import java.io.Serializable;

import javax.validation.constraints.NotNull;
import javax.validation.constraints.Size;

import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

/**
 * <AUTHOR>
 * @date 2022/01/13 11:31
 **/
@Data
@ApiModel(value = "Request对象", description = "Request对象")
public class TemplateRequest implements Serializable {
    private static final long serialVersionUID = 1L;

    @ApiModelProperty(value = "名称")
    @NotNull(message = "名称不能为空")
    @Size(min = 1, max = 20, message = "名称字符长度要求 [{min},{max}]")
    private String name;
}
