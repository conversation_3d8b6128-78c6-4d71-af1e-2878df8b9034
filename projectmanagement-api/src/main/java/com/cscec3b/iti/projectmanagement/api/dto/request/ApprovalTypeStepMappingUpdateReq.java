package com.cscec3b.iti.projectmanagement.api.dto.request;

import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Getter;
import lombok.Setter;

import java.io.Serializable;

/**
 * 项目立项类型与立项步骤绑定响应
 *
 * <AUTHOR>
 * @date 2024/01/16
 */
@Getter
@Setter
@ApiModel(value = "项目立项类型与立项步骤绑定响应")
public class ApprovalTypeStepMappingUpdateReq extends ApprovalTypeStepMappingReq implements Serializable {

    /**
     * 项目立项类型与步骤关联id
     */
    @ApiModelProperty(value = "项目立项类型与步骤关联id")
    private Long stepMappingId;

    /**
     * 立项类型code
     */
    @ApiModelProperty(value = "立项类型code ", notes = "默认值： default")
    private String typeCode;

}
