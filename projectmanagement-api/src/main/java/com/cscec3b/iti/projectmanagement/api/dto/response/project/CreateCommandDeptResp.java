package com.cscec3b.iti.projectmanagement.api.dto.response.project;

import java.io.Serializable;

import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;
import lombok.experimental.Accessors;

/**
 * @Description 创建指挥部返回信息
 * <AUTHOR>
 * @Date 2022/11/3 9:22
 */
@Data
@Accessors(chain = true)
@ApiModel(value = "CreateCommandDeptResp", description = "创建指挥部返回信息")
public class CreateCommandDeptResp implements Serializable {
    /**
     * 指挥部组织编码
     */
    @ApiModelProperty(value = "指挥部组织编码")
    private String code;

    /**
     * 指挥部组织名称
     */
    @ApiModelProperty(value = "指挥部组织名称")
    private String cmdFullName;
}
