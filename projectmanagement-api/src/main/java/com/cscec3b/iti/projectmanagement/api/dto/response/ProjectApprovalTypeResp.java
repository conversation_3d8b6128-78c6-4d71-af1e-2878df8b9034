package com.cscec3b.iti.projectmanagement.api.dto.response;

import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Getter;
import lombok.Setter;
import lombok.experimental.Accessors;

import java.io.Serializable;
import java.util.List;
import java.util.Set;

/**
 * 项目立项类型响应
 *
 * <AUTHOR>
 * @date 2024/01/02
 */
@Getter
@Setter
@Accessors(chain = true)
@ApiModel(value = "项目立项类型响应")
public class ProjectApprovalTypeResp implements Serializable {

    /**
     * 项目类型id
     */
    @ApiModelProperty(value = "项目类型id")
    private Long typeId;

    /**
     * 项目类型名称
     */
    @ApiModelProperty(value = "项目类型名称")
    private String typeName;

    /**
     * 项目类型编码
     */
    @ApiModelProperty(value = "项目类型编码")
    private String typeCode;

    /**
     * 父节点
     */
    @ApiModelProperty(value = "父节点")
    private Long parentId;

    /**
     * 父节点名称
     */
    @ApiModelProperty(value = "父节点名称")
    private String parentName;

    /**
     * 父节点编码
     */
    @ApiModelProperty(value = "父节点编码")
    private String parentCode;

    /**
     * 状态：0：正常； 1：停用
     */
    @ApiModelProperty(value = "状态：0：正常； 1：停用")
    private String remark;

    /**
     * 是否末级节点： 0：否； 1： 是
     */
    @ApiModelProperty(value = "是否末级节点： 0：否； 1： 是")
    private int lastNode;

    /**
     * 根节点code
     */
    @ApiModelProperty(value = "根节点code")
    private String rootCode;

    /**
     * 子节点信息
     */
    @ApiModelProperty(value = "子节点信息")
    private List<ProjectApprovalTypeResp> child;

    /**
     * 组织id
     */
    @ApiModelProperty(value = "组织id")
    private String orgId;

    /**
     * 组织名称
     */
    @ApiModelProperty(value = "组织名称")
    private String orgName;

    /**
     * 组织简称
     */
    @ApiModelProperty(value = "组织简称")
    private String abbreviation;

    /**
     * queryCode
     */
    @ApiModelProperty(value = "queryCode")
    private String idPath;

    /**
     * 业务板块信息
     */
    @ApiModelProperty(value = "业务板块信息")
    private Set<String> businessSegmentCodePath;
}
