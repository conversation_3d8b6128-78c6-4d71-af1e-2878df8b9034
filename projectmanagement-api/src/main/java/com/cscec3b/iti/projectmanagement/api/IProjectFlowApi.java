package com.cscec3b.iti.projectmanagement.api;

import javax.validation.Valid;

import org.springframework.web.bind.annotation.DeleteMapping;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.bind.annotation.RequestParam;

import com.cscec3b.iti.common.base.api.GenericityResponse;
import com.cscec3b.iti.common.base.page.Page;
import com.cscec3b.iti.projectmanagement.api.dto.request.flow.ProjectFlowCreateReq;
import com.cscec3b.iti.projectmanagement.api.dto.request.flow.ProjectFlowQueryReq;
import com.cscec3b.iti.projectmanagement.api.dto.response.event.ProjectFlowQueryResp;

import io.swagger.annotations.ApiOperation;

/**
 * 项目事件配置管理
 * <AUTHOR>
 * @date 2023/9/21 15:20
 */

public interface IProjectFlowApi {
    /**
     * 路径
     */
    String PATH = "/flow";

    /**
     * 分页查询项目事件列表
     * @param req
     * @return
     * @date 2023/10/8
     */
    @PostMapping("/page")
    @ApiOperation(value = "分页查询项目事件列表", notes = "分页查询项目事件列表")
    GenericityResponse<Page<ProjectFlowQueryResp>> queryPage(@Valid @RequestBody ProjectFlowQueryReq req);

    /**
     * 新增事件配置
     * @param req
     * @return
     * @date 2023/10/8
     */
    @PostMapping("/save")
    @ApiOperation(value = "新增事件配置", notes = "新增事件配置")
    GenericityResponse<Boolean> saveFlow(@Valid @RequestBody ProjectFlowCreateReq req);

    /**
     * 修改事件配置
     * @param req
     * @return
     * @date 2023/10/8
     */
    @PostMapping("/update")
    @ApiOperation(value = "修改事件配置", notes = "修改事件配置")
    GenericityResponse<Boolean> updateFlow(@Valid @RequestBody ProjectFlowCreateReq req);

    /**
     * 删除事件
     * @param id
     * @return
     * @date 2023/10/8
     */
    @DeleteMapping("/delete")
    @ApiOperation(value = "删除事件", notes = "删除事件")
    GenericityResponse<Boolean> deleteFlow(@RequestParam(value = "id") Long id);
}
