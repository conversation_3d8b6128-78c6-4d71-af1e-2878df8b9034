package com.cscec3b.iti.projectmanagement.api.dto.request.secrecy;

import com.baomidou.mybatisplus.annotation.TableField;
import com.cscec3b.iti.projectmanagement.api.dto.request.BasePage;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;
import lombok.experimental.Accessors;


/**
 * 查询保密项目请求对象
 * <AUTHOR>
 * @date 2024/04/11
 */
@Data
@Accessors(chain = true)
@ApiModel(value = "QuerySecrecyProjectParams", description = "查询保密项目请求对象")
public class QuerySecrecyProjectParams extends BasePage {

    @ApiModelProperty("云枢执行单位IdPath")
    private String yunshuExecuteUnitIdPath;

    /**
     * 财商立项名称
     */
    @ApiModelProperty(value = "财商立项名称")
    private String projectFinanceName;

    /**
     * 财商立项编号
     */
    @ApiModelProperty(value = "财商立项编号")
    private String projectFinanceCode;

    /**
     * 项目地址
     */
    @ApiModelProperty(value = "项目地址")
    private String projectAddress;

    @ApiModelProperty(value = "工程名称")
    private String projectName;

    @ApiModelProperty("项目标识")
    private String cpmProjectKey;

    /**
     * 局标准分类编码路径
     */
    @ApiModelProperty(value = "局标准分类编码路径")
    private String standardTypeCodePath;




}

