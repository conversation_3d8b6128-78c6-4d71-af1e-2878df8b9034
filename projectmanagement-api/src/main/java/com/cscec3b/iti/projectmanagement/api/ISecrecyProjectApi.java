package com.cscec3b.iti.projectmanagement.api;

import com.cscec3b.iti.common.base.api.GenericityResponse;
import com.cscec3b.iti.common.base.page.Page;
import com.cscec3b.iti.projectmanagement.api.dto.request.secrecy.SecrecyProjectReq;
import com.cscec3b.iti.projectmanagement.api.dto.request.secrecy.QuerySecrecyProjectParams;
import com.cscec3b.iti.projectmanagement.api.dto.request.secrecy.UpdateSecrecyProjectReq;
import com.cscec3b.iti.projectmanagement.api.dto.response.secrecy.SecrecyProjectDetailResp;
import com.cscec3b.iti.projectmanagement.api.dto.response.secrecy.SecrecyProjectResp;
import com.cscec3b.iti.projectmanagement.api.dto.response.secreycivilmilitary.CivilMilitaryIntegrationDetailResp;
import io.swagger.annotations.ApiModelProperty;
import io.swagger.annotations.ApiOperation;
import io.swagger.annotations.ApiParam;
import org.springframework.validation.annotation.Validated;
import org.springframework.web.bind.annotation.DeleteMapping;
import org.springframework.web.bind.annotation.GetMapping;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.bind.annotation.RequestParam;

import javax.validation.constraints.NotNull;
import java.util.List;

/**
 * 保密项目api
 * <AUTHOR>
 * @date 2024/04/10
 */
public interface ISecrecyProjectApi {

    /**
     * 路径
     */
    String PATH = "/project/secrecy";

    /**
     * 编辑保密项目API
     *
     * @param request 请求
     * @return {@link GenericityResponse }<{@link Boolean }>
     * <AUTHOR>
     * @date 2023/08/21
     */
    @PostMapping("/update")
    @ApiOperation(value = "编辑保密项目API", notes = "编辑保密项目API接口")
    GenericityResponse<Boolean> updateSpecialProject(
            @Validated @ApiParam(value = "编辑更新保密项目参数", required = true) @RequestBody UpdateSecrecyProjectReq request);


    /**
     * 保密项目详情API
     *
     * @param id id
     * @return {@link GenericityResponse }<{@link CivilMilitaryIntegrationDetailResp }>
     * <AUTHOR>
     * @date 2023/08/21
     */
    @GetMapping("/detail")
    @ApiOperation(value = "详情API", notes = "详情API接口")
    GenericityResponse<SecrecyProjectDetailResp>
    getSpecialProjectDetail(@ApiParam(value = "详情查询参数", required = true) @RequestParam @NotNull Long id);

    /**
     * 列表分页查询
     *
     * @param params 参数个数
     * @return {@link GenericityResponse }<{@link Page }<{@link SecrecyProjectDetailResp }>>
     * <AUTHOR>
     * @date 2023/08/21
     */
    @PostMapping("/page")
    @ApiOperation(value = "列表分页查询", notes = "保密项目列表分页查询API接口")
    GenericityResponse<Page<SecrecyProjectResp>> specialProjectPage(
            @Validated @ApiParam(value = "保密项目列表分页查询参数", required = true) @RequestBody QuerySecrecyProjectParams params);

    /**
     * 新建保密项目API
     *
     * @param request 请求
     * @return {@link GenericityResponse }<{@link Boolean }>
     * <AUTHOR>
     * @date 2023/08/21
     */
    @PostMapping("/create")
    @ApiOperation(value = "新建保密项目信息API", notes = "新建保密项目信息API")
    GenericityResponse<Long>
    create(@Validated @ApiParam(value = "新增保密项目入参", required = true) @RequestBody SecrecyProjectReq request);


    @DeleteMapping("/delete")
    @ApiModelProperty(value = "删除保密项目信息", notes = "删除保密项目信息")
    GenericityResponse<Boolean> delete(@ApiParam(value = "删除保密项目信息", required = true) @RequestBody List<Long> ids);


}
