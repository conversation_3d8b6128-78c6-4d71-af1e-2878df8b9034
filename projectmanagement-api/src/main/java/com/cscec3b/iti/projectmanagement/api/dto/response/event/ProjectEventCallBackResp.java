package com.cscec3b.iti.projectmanagement.api.dto.response.event;

import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

import java.util.ArrayList;

/**
 * 事件回调接口返回值
 *
 * <AUTHOR>
 * @date 2023/04/20 11:00
 **/

@Data
@ApiModel("事件回调接口返回值")
public class ProjectEventCallBackResp {

    @ApiModelProperty(value = "项目信息")
    private Object projectInfo;

    @ApiModelProperty(value = "投标总结")
    private Object bidSummaryInfo = new ArrayList<>();

    @ApiModelProperty(value = "局内分包合同信息")
    private Object bureauContractInfo = new ArrayList<>();

    @ApiModelProperty(value = "局内补充协议信息")
    private Object bureauSupplementaryAgreementInfo = new ArrayList<>();

    @ApiModelProperty(value = "合同详情")
    private Object contractInfo = new ArrayList<>();

    @ApiModelProperty(value = "补充协议信息")
    private Object supplementaryAgreementInfo = new ArrayList<>();

    @ApiModelProperty(value = "开标记录信息")
    private Object bidOpeningRecordsInfo = new ArrayList<>();

    @ApiModelProperty(value = "工程项目相关")
    private Object standardMappingInfo = new ArrayList<>();

}
