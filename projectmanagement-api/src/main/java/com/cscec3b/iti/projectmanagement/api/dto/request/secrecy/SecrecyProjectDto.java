package com.cscec3b.iti.projectmanagement.api.dto.request.secrecy;

import com.cscec3b.iti.projectmanagement.api.validation.annotation.EnumVal;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;
import lombok.experimental.Accessors;

import javax.validation.constraints.DecimalMin;
import javax.validation.constraints.NotBlank;
import javax.validation.constraints.Size;
import java.io.Serializable;
import java.math.BigDecimal;


/**
 * <AUTHOR>
 * @date 2024/04/11
 */
@Data
@Accessors(chain = true)
@ApiModel(value = "SecrecyProjectDto", description = "转换对象,建造类与非建造类通用字段")
public class SecrecyProjectDto implements Serializable {
    private static final long serialVersionUID = 1L;

    /**
     * 立项描述
     */
    @ApiModelProperty(value = "立项描述")
    @NotBlank(message = "立项描述不能为空")
    @Size(min = 1, max = 250, message = "立项描述的字段长度要求[{min},{max}]")
    private String projectDesc;

    /**
     * 项目分类id
     */
    @ApiModelProperty(value = "项目分类id")
//    @NotBlank(message = "项目分类id不能为空")
    @Size(min = 1, max = 50, message = "项目分类id字段长度要求[{min},{max}]")
    private String projectClassId;

    /**
     * 行政区域的idpath
     */
    @ApiModelProperty(value = "行政区域的idpath")
//    @NotBlank(message = "行政区域的idpath不能为空")
    @Size(min = 1, max = 256, message = "行政区域的idpath字段长度要求[{min},{max}]")
    private String regionIdPath;

    /**
     * 项目分类名称
     */
    @ApiModelProperty(value = "项目分类名称")
//    @NotBlank(message = "项目分类名称不能为空")
    @Size(min = 1, max = 50, message = "项目分类名称字段长度要求[{min},{max}]")
    private String projectClassName;

    /**
     * 项目分类idpath
     */
    @ApiModelProperty(value = "项目分类idpath")
//    @NotBlank(message = "项目分类idpath不能为空")
    @Size(min = 1, max = 256, message = "项目分类idpath字段长度要求[{min},{max}]")
    private String projectClassIdPath;

    /**
     * 投资主体
     */
    @ApiModelProperty(value = "投资主体", position = 36)
    @Size(max = 20, message = "投资主体的字段长度必须小于[{max}]")
    private String investors;

    /**
     * 客户名称
     */
    @ApiModelProperty(value = "客户名称", position = 37)
    @Size(max = 255, message = "客户名称的字段长度必须小于[{max}]")
    private String customerName;

    /**
     * 合同总金额（元）
     */
    @ApiModelProperty(value = "合同总金额（元）", position = 34)
    @DecimalMin(value = "0.00", message = "合同总金额（元）格式不正确")
    private BigDecimal contractAmount;

    /**
     * 项目地址
     */
    @ApiModelProperty(value = "项目地址", position = 15)
    @NotBlank(message = "项目地址不能为空")
    @Size(min = 1, max = 128, message = "项目地址的字段长度必须小于[{max}]")
    private String projectAddress;

    @ApiModelProperty(value = "局标准分类")
    private String standardType;

    /**
     * 局标准分类编码路径
     */
    @ApiModelProperty(value = "局标准分类编码路径")
    @NotBlank(message = "局标准分类编码路径")
    private String standardTypeCodePath;

    /**
     * 财商业务板块全路径
     */
    @ApiModelProperty(value = "财商业务板块全路径")
    private String financialBusinessSegment;

    /**
     * 财商业务板块codePath
     */
    @ApiModelProperty(value = "财商业务板块codePath")
    private String businessSegmentCodePath;

    /**
     * 文件类型
     */
    @ApiModelProperty(value = "文件类型")
    @EnumVal(intValues = {6,7})
    private Integer belongFileType;

    @ApiModelProperty(value = "工程简称")
    private String projectAbbreviation;

    @ApiModelProperty(value = "云枢执行单位名称")
    @NotBlank(message = "云枢执行单位名称不能为空")
    private String yunshuExecuteUnit;

    @ApiModelProperty(value = "云枢执行单位编码")
    @NotBlank(message = "云枢执行单位编码名称不能为空")
    private String yunshuExecuteUnitCode;

    @ApiModelProperty(value = "云枢执行单位id")
    @NotBlank(message = "云枢执行单位id名称不能为空")
    private String yunshuExecuteUnitId;

    @ApiModelProperty(value = "云枢执行单位idPath")
    @NotBlank(message = "云枢执行单位idPath名称不能为空")
    private String yunshuExecuteUnitIdPath;

    @ApiModelProperty(value = "云枢执行单位简称")
    @NotBlank(message = "云枢执行单位简称名称不能为空")
    private String yunshuExecuteUnitAbbreviation;

    /**
     * 工程名称
     */
    @ApiModelProperty(value = "工程名称不能为空")
    @NotBlank(message = "工程名称不能为空")
    private String projectName;
}
