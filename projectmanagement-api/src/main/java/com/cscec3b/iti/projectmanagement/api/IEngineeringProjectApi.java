package com.cscec3b.iti.projectmanagement.api;

import com.cscec3b.iti.common.base.api.GenericityResponse;
import com.cscec3b.iti.common.base.page.Page;
import com.cscec3b.iti.projectmanagement.api.dto.request.engineeringproject.*;
import com.cscec3b.iti.projectmanagement.api.dto.response.engineeringproject.*;
import io.swagger.annotations.ApiOperation;
import io.swagger.annotations.ApiParam;
import org.springframework.validation.annotation.Validated;
import org.springframework.web.bind.annotation.*;

import java.util.List;

/**
 * 工程项目控制器接口
 * 
 * <AUTHOR>
 * @date 2025/03/13
 */
public interface IEngineeringProjectApi {

    String PATH = "/engineering-project";

    /**
     * 新增工程并关联施工项目
     * 
     * @param req 新增工程并关联施工项目参数
     * @return {@link GenericityResponse }<{@link Boolean }>
     */
    @PostMapping("/create")
    @ApiOperation(value = "新增工程并关联施工项目")
    GenericityResponse<Boolean>
        addEngineeringProject(@ApiParam("新增工程并关联施工项目参数") @RequestBody AddEngineeringProjectReq req);

    /**
     * 挂接工程项目到工程项目
     * 
     * @param req 挂接工程项目到工程项目参数
     * @return {@link GenericityResponse }<{@link Boolean }>
     */
    @PostMapping("/hook")
    @ApiOperation(value = "挂接工程项目到工程项目", notes = "可挂接上级或下级")
    GenericityResponse<Boolean> hookEngineering2EngineeringProject(
        @ApiParam("挂接工程项目到工程项目参数") @RequestBody HookEngineering2EngineeringProjectReq req);

    /**
     * 关联施工项目到工程项目
     * 
     * @param req 关联施工项目到工程项目参数
     * @return {@link GenericityResponse }<{@link Boolean }>
     */
    @PostMapping("/mapping-project")
    @ApiOperation(value = "关联施工项目到工程项目")
    GenericityResponse<Boolean> mappingProject2EngineeringProject(
        @ApiParam("关联施工项目到工程项目参数") @RequestBody MappingStandard2EngineeringProjectReq req);

    /**
     * 工程项目分页查询
     * 
     * @param req 工程项目列表查询参数
     * @return {@link GenericityResponse }<{@link Page }<{@link EngineerProjectPageResp }>>
     */
    @PostMapping("/page-list")
    @ApiOperation(value = "工程项目分页查询")
    GenericityResponse<Page<EngineerProjectPageResp>>
        pageList(@ApiParam("工程项目列表查询参数") @RequestBody EngineerProjectPageReq req);

    /**
     * 挂接工程项目分页查询
     *
     * @param req 工程项目列表查询参数
     * @return {@link GenericityResponse }<{@link Page }<{@link EngineerProjectPageResp }>>
     */
    @PostMapping("/hook/page")
    @ApiOperation(value = "工程项目分页查询")
    GenericityResponse<Page<EngineerProjectPageResp>>
        hookPageList(@ApiParam("工程项目列表查询参数") @RequestBody HookEngineerProjectPageReq req);
    /**
     * 解除工程项目与施工项目的关联
     *
     * @param projectId 施工项目id
     * @return {@link GenericityResponse }<{@link Boolean }>
     */
    @PutMapping("/unmapping-project/{projectId}")
    @ApiOperation(value = "解除工程项目与施工项目的关联", notes = "解除主施工项目将同步删除关联的工程项目")
    GenericityResponse<Boolean> unmappingProject2EngineeringProject(@ApiParam("施工项目id") @PathVariable Long projectId);

    /**
     * 解除一个施工项目与当前工程项目的关联，并关联到另一个工程项目
     *
     * @param req 转移施工项目关联请求参数
     * @return {@link GenericityResponse }<{@link Boolean }>
     */
    @PutMapping("/transfer-project-mapping")
    @ApiOperation(value = "施工项目换绑")
    GenericityResponse<Boolean> transferProjectMapping(
            @ApiParam("转移施工项目关联请求参数") @RequestBody @Validated TransferProjectMappingReq req);


    /**
     * 解除工程项目与上级工程项目的挂接
     *
     * @param engineeringProjectId 当前工程项目id
     * @return {@link GenericityResponse }<{@link Boolean }>
     */
    @PutMapping("/unhook-engineering/{engineeringProjectId}")
    @ApiOperation(value = "解除工程项目与上级工程项目的挂接")
    GenericityResponse<Boolean>
        unmappingEngineering2EngineeringProject(@ApiParam("当前工程项目id") @PathVariable Long engineeringProjectId);

    /**
     * 将一个施工项目设置为主施工项目
     *
     * @param projectId 施工项目id
     * @param engineeringProjectId 工程项目id
     * @return {@link GenericityResponse }<{@link Boolean }>
     */
    @PutMapping("/set-main-project")
    @ApiOperation(value = "将一个已联工程项目的施工项目设置为主施工项目")
    GenericityResponse<Boolean> setMainProject(@ApiParam("施工项目id") @RequestParam Long projectId,
        @ApiParam("工程项目id") @RequestParam Long engineeringProjectId);

    /**
     * 通过施工项目id获取工程信息(包含上下级关系)
     *
     * @param projectId 施工项目id
     * @return {@link GenericityResponse }<{@link EngineeringProjectInfoResp }>
     */
    @GetMapping("/info-by-project/{projectId}")
    @ApiOperation(value = "通过施工项目获取工程信息(包含上下级关系)")
    GenericityResponse<EngineeringProjectInfoResp>
        getEngineeringProjectInfoByProject(@ApiParam("工程id") @PathVariable Long projectId);

    /**
     * 按工程项目获取 MAPPING 标准项目
     *
     * @param engineeringProjectId 工程项目 ID
     * @return {@link GenericityResponse }<{@link MappingStandardProjectResp }>
     */
    @GetMapping("/mapping-standard-project/{engineeringProjectId}")
    @ApiOperation(value = "通过工程id获取已关联的施工项目")
    GenericityResponse<MappingStandardProjectResp>
        getMappingStandardProjectByEngineeringProject(@ApiParam("工程id") @PathVariable Long engineeringProjectId);

    /**
     * 按工程项目获取 MAPPING 标准项目
     *
     * @param engineeringProjectId 工程项目 ID
     * @param getSmartSite 获取 Smart Site
     * @return {@link GenericityResponse }<{@link MappingStandardProjectResp }>
     */
    @GetMapping("/mapping-standard-project/task/{engineeringProjectId}")
    @ApiOperation(value = "通过工程id获取已关联的施工项目")
    GenericityResponse<MappingStandardProjectResp> getMappingStandardProjectByEngineeringProject(
        @ApiParam("工程id") @PathVariable Long engineeringProjectId,
        @ApiParam("是否获取工地状态") @RequestParam(defaultValue = "true") Boolean getSmartSite);

    /**
     * 发送待办任务
     *
     * @param engineerIds 工程师项目 ID
     * @return {@link GenericityResponse }<{@link Boolean }>
     */
    @PutMapping("/send-todo-task")
    @ApiOperation(value = "发送待办任务")
    GenericityResponse<Boolean> sendTodoTask(@ApiParam("施工项目id") @RequestBody List<Long> engineerIds);

    /**
     * 以公司视角查询工程信息
     */
    @PostMapping("/page/company-view")
    @ApiOperation(value = "以公司视角查询工程信息")
    GenericityResponse<Page<CompanyViewEngineeringProjectResp>> companyViewPageList(
            @ApiParam("查询参数") @Validated @RequestBody CompanyViewEngineerProjectReq req);

    /**
     * 获取单个工程的树形结构
     */
    @GetMapping("/company-view/tree/{id}")
    @ApiOperation(value = "获取单个工程的树形结构")
    GenericityResponse<List<CompanyViewEngineeringProjectTreeResp>> getCompanyViewEngineeringProjectTree(
            @ApiParam("工程项目id") @PathVariable(value = "id") Long engineerId);
}
