package com.cscec3b.iti.projectmanagement.api.dto.request.approvalstep;

import com.cscec3b.iti.projectmanagement.api.dto.request.BasePage;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;
import lombok.EqualsAndHashCode;

import java.io.Serializable;

@Data

@EqualsAndHashCode(callSuper = true)
@ApiModel("立项步骤注意事项分页请求参数")
public class ApprovalStepPageReq extends BasePage implements Serializable {

    private static final long serialVersionUID = 1L;

    /**
     * 立项步骤id
     */
    @ApiModelProperty(value = "立项步骤No")
    private Integer stepNo;

    /**
     * 注意事项内容
     */
    @ApiModelProperty(value = "注意事项内容")
    private String content;
}
