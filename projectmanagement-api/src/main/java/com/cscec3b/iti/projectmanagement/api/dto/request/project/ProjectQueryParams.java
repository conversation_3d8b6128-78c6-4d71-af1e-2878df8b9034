package com.cscec3b.iti.projectmanagement.api.dto.request.project;

import java.math.BigDecimal;

import javax.validation.constraints.Min;

import com.cscec3b.iti.projectmanagement.api.dto.request.BasePage;

import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;
import lombok.experimental.Accessors;

/**
 * 项目列表分页查询参数实体
 *
 * <AUTHOR>
 * @Description
 * @Date 2022/10/31 12:28
 */
@Data
@Accessors(chain = true)
@ApiModel(value = "ProjectQueryParams", description = "项目列表查询参数")
public class ProjectQueryParams extends BasePage {

    @ApiModelProperty("idPath")
    private String idPath;

    @ApiModelProperty("云枢执行单位IdPath")
//    @NotBlank(message = "云枢执行单位IdPath不允许为空")
    private String yunshuExecuteUnitIdPath;

    @ApiModelProperty("项目名称(财商返回)")
    private String projectFinanceName;

    @ApiModelProperty("项目编码(财商返回)")
    private String projectFinanceCode;

    @ApiModelProperty("业务类型")
    private String businessType;

    @ApiModelProperty("项目总金额最小值（合同总金额）")
    @Min(value = 0, message = "最小值必须大于等于0")
    private BigDecimal contractAmountMin;

    @ApiModelProperty("项目总金额最大值（合同总金额）")
    private BigDecimal contractAmountMax;

    @ApiModelProperty("云枢组织id")
    private String yunshuOrgId;

    @ApiModelProperty("项目标识")
    private String cpmProjectKey;

    @ApiModelProperty("项目状态(工程): 00:开工准备; 01:在施; 02:完工; 03:竣工; 04:销项; 0199:停工; 0399:质保;")
    private String projectStatusEng;

    @ApiModelProperty("项目状态(财务): 01:在施; 0301:已竣未结; 0302:已竣已结; 0199:停工; 04:销项;")
    private String projectStatusFin;

    @ApiModelProperty("项目状态(商务): 05:未结; 06:已结")
    private String projectStatusBiz;

    /**
     * 组织类型(70：项目部)
     */
    private String orgType;

}