package com.cscec3b.iti.projectmanagement.api.dto.request.special;

import com.cscec3b.iti.projectmanagement.api.dto.request.BasePage;

import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;
import lombok.experimental.Accessors;

/**
 * @description: CreateSpecialProjectReq
 * <AUTHOR>
 * @date 2023/2/14 9:29
 */

@Data
@Accessors(chain = true)
@ApiModel(value = "QuerySpecialProjectParams", description = "查询特殊立项请求对象")
public class QuerySpecialProjectParams extends BasePage {

    @ApiModelProperty("执行单位IdPath")
    private String executeUnitIdPath;

    @ApiModelProperty("云枢执行单位IdPath")
//    @NotBlank(message = "云枢执行单位IdPath不允许为空")
    private String yunshuExecuteUnitIdPath;

//    /**
//     * 特殊立项项目分类IdPath
//     */
//    @ApiModelProperty(value = "特殊立项项目分类IdPath")
//    private String projectClassIdPath;

    /**
     * 财商立项名称
     */
    @ApiModelProperty(value = "财商立项名称")
    private String projectFinanceName;

    /**
     * 财商立项编号
     */
    @ApiModelProperty(value = "财商立项编号")
    private String projectFinanceCode;

    /**
     * 项目地址
     */
    @ApiModelProperty(value = "项目地址")
    private String projectAddress;

    @ApiModelProperty(value = "工程名称")
    private String projectName;

    @ApiModelProperty("项目标识")
    private String cpmProjectKey;

    /**
     * 局标准分类编码路径
     */
    @ApiModelProperty(value = "局标准分类编码路径")
    private String standardTypeCodePath;


    @ApiModelProperty("项目状态(工程): 00:开工准备; 01:在施; 02:完工; 03:竣工; 04:销项; 0199:停工; 0399:质保;")
    private String projectStatusEng;

    @ApiModelProperty("项目状态(财务): 01:在施; 0301:已竣未结; 0302:已竣已结; 0199:停工; 04:销项;")
    private String projectStatusFin;

    @ApiModelProperty("项目状态(商务): 05:未结; 06:已结")
    private String projectStatusBiz;
}

