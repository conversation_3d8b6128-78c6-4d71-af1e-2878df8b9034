package com.cscec3b.iti.projectmanagement.api.dto.request;

import com.fasterxml.jackson.annotation.JsonProperty;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;
import lombok.experimental.Accessors;

import javax.validation.constraints.NotBlank;
import javax.validation.constraints.NotNull;
import javax.validation.constraints.Size;
import java.io.Serializable;
import java.math.BigDecimal;
import java.util.List;
import java.util.Map;

/**
 * 合同录入入参对象
 *
 * <AUTHOR>
 * @Description
 * @Date 2022/10/19 11:05
 */
@Data
@Accessors(chain = true)
@ApiModel(value = "ContractEntryReq", description = "合同")
public class ContractEntryReq implements Serializable {
    private static final long serialVersionUID = 1L;
    /**
     * 发起人单位
     */
    @ApiModelProperty(value = "发起人", position = 1)
    @Size(max = 50, message = "发起人不超过50个字符")
    private String submitPerson;
    /**
     * 合同编号
     */
    @ApiModelProperty(value = "合同编号", position = 2)
    @Size(max = 50, message = "合同编号不超过50个字符")
    private String contractCode;
    /**
     * 工程名称
     */
    @ApiModelProperty(value = "工程名称", position = 3)
    @Size(max = 128, message = "工程名称不超过128个字符")
    private String projectName;
    /**
     * 工程编号
     */
    @ApiModelProperty(value = "工程编号", position = 4)
    @Size(max = 32, message = "工程编号不超过32个字符")
    private String projectCode;
    /**
     * 工程简称
     */
    @ApiModelProperty(value = "工程简称", position = 5)
    @Size(max = 255, message = "工程编号不超过{max}个字符")
    private String projectShortName;
    /**
     * 工程属地
     */
    @ApiModelProperty(value = "工程属地", position = 6)
    @Size(min = 1, max = 128, message = "工程属地长度要求[{min},{max}]")
    private String projectBelong;
    /**
     * 省
     */
    @ApiModelProperty(value = "省", position = 7)
    @Size(max = 50, message = "省名称长度不超过50个字符")
    private String province;
    /**
     * 市
     */
    @ApiModelProperty(value = "市", position = 8)
    @Size(max = 50, message = "市名称长度不超过50个字符")
    private String city;
    /**
     * 区
     */
    @ApiModelProperty(value = "区", position = 9)
    @Size(max = 50, message = "区名称长度不超过50个字符")
    private String region;
    /**
     * 具体地址
     */
    @ApiModelProperty(value = "具体地址", position = 10)
    @Size(max = 255, message = "具体地址的长度必须小于{max}")
    private String address;
    /**
     * 国别
     */
    @ApiModelProperty(value = "国别", position = 11)
    @Size(max = 128, message = "国名称长度不超过{max}个字符")
    private String country;
    /**
     * 工程类型（国家标准）
     */
    @ApiModelProperty(value = "工程类型（国家标准）", position = 12)
    @Size(min = 1, max = 32, message = "工程类型（国家标准）长度要求[{min},{max}]")
    private String countryProjectType;
    /**
     * 工程类型（总公司市场口径）
     */
    @ApiModelProperty(value = "工程类型（总公司市场口径）", position = 13)
    @Size(min = 1, max = 32, message = "工程类型（总公司市场口径）长度要求[{min},{max}]")
    private String marketProjectType;
    /**
     * 工程类型(总公司市场口径)2
     */
    @ApiModelProperty(value = "工程类型(总公司市场口径)2", position = 14)
    @Size(max = 32, message = "工程类型(总公司市场口径)2不超过{max}个字符")
    private String marketProjectType2;
    /**
     * 工程类型(总公司综合口径)
     */
    @ApiModelProperty(value = "工程类型(总公司综合口径)", position = 15)
    @Size(min = 1, max = 32, message = "工程类型（总公司综合口径）长度要求[{min},{max}]")
    private String projectType;
    /**
     * 工程类型(总公司综合口径)2
     */
    @ApiModelProperty(value = "工程类型(总公司综合口径)2", position = 16)
    @Size(max = 32, message = "工程类型(总公司综合口径)2不超过{max}个字符")
    private String projectType2;
    /**
     * 工程类型(总公司综合口径)3
     */
    @ApiModelProperty(value = "工程类型(总公司综合口径)3", position = 17)
    @Size(max = 32, message = "工程类型(总公司综合口径)3不超过{max}个字符")
    private String projectType3;
    /**
     * 工程类型(总公司综合口径)4
     */
    @ApiModelProperty(value = "工程类型(总公司综合口径)4", position = 18)
    @Size(max = 32, message = "工程类型(总公司综合口径)4不超过{max}个字符")
    private String projectType4;
    /**
     * 项目附件信息对象
     */
    @ApiModelProperty(value = "项目附件信息对象", position = 19)
    @JsonProperty("projectAttachment")
    private List<Map> projectAttachment1;
    /**
     * 总分包类别
     */
    @ApiModelProperty(value = "总分包类别", position = 20)
    @Size(max = 32, message = "总分包类别不超过{max}个字符")
    private String totalSubcontractingCategory;
    /**
     * 结构形式
     */
    @ApiModelProperty(value = "结构形式", position = 21)
    @Size(max = 64, message = "结构形式不超过{max}个字符")
    private String structuralStyle;
    /**
     * 结构形式2
     */
    @ApiModelProperty(value = "结构形式2", position = 22)
    @Size(max = 64, message = "结构形式2不超过{max}个字符")
    private String structuralStyle2;
    /**
     * 是否有钢结构
     */
    @ApiModelProperty(value = "是否有钢结构", position = 23)
    @Size(max = 4, message = "是否有钢结构不超过{max}个字符")
    private String includingSteel;
    /**
     * 最长桩基长度
     */
    @ApiModelProperty(value = "最长桩基长度", position = 24)
    @Size(max = 32, message = "最长桩基长度不超过{max}个字符")
    private String projectMaxLength;
    /**
     * 最大桩径
     */
    @ApiModelProperty(value = "最大桩径", position = 25)
    @Size(max = 32, message = "最大桩径不超过{max}个字符")
    private String projectMaxWidth;
    /**
     * 合同类型
     */
    @ApiModelProperty(value = "合同类型", position = 26)
    @Size(max = 64, message = "合同类型不超过{max}个字符")
    private String contractType;
    /**
     * 是否装配式
     */
    @ApiModelProperty(value = "是否装配式", position = 27)
    @Size(max = 4, message = "是否装配式不超过{max}个字符")
    private String fabricated;
    /**
     * 是否为投融资带动项目
     */
    @ApiModelProperty(value = "是否为投融资带动项目", position = 28)
    @Size(max = 4, message = "是否为投融资带动项目不超过{max}个字符")
    private String isInvestmentFinancingDrivenProjects;
    /**
     * 业务类型
     */
    @ApiModelProperty(value = "业务类型", position = 29)
    @Size(min = 1, max = 32, message = "业务类型长度要求[{min},{max}]")
    private String businessType;
    /**
     * 是否投资项目
     */
    @ApiModelProperty(value = "是否投资项目", position = 30)
    @Size(min = 1, max = 32, message = "是否投资项目长度要求[{min},{max}]")
    private String investmentProjects;
    /**
     * 投资主体
     */
    @ApiModelProperty(value = "投资主体", position = 31)
    @Size(max = 20, message = "投资主体不超过20个字符")
    private String investors;
    /**
     * 所属办事处
     */
    @ApiModelProperty(value = "所属办事处", position = 32)
    @Size(max = 128, message = "所属办事处不超过{max}个字符")
    private String signFormOffice;
    /**
     * 客户名称
     */
    @ApiModelProperty(value = "客户名称", position = 33)
    @Size(min = 1, max = 255, message = "客户名称长度要求[{min},{max}]")
    private String customerName;
    /**
     * 客户母公司
     */
    @ApiModelProperty(value = "客户母公司", position = 34)
    @Size(max = 255, message = "客户母公司不超过{max}个字符")
    private String superiorCompanyName;
    /**
     * 客户企业性质
     */
    @ApiModelProperty(value = "客户企业性质", position = 35)
    @Size(min = 1, max = 255, message = "客户企业性质长度要求[{min},{max}]")
    private String enterpriseType;
    /**
     * 建设单位（甲方）联系人
     */
    @ApiModelProperty(value = "建设单位（甲方）联系人", position = 36)
    @Size(max = 255, message = "建设单位（甲方）联系人不超过{max}个字符")
    private String contactPerson;
    /**
     * 建设单位（甲方）联系人电话
     */
    @ApiModelProperty(value = "建设单位（甲方）联系人电话", position = 37)
    @Size(max = 64, message = "建设单位（甲方）联系人电话不超过{max}个字符")
    private String contactPersonMobile;
    /**
     * 设计单位
     */
    @ApiModelProperty(value = "设计单位", position = 38)
    @Size(max = 255, message = "设计单位不超过{max}个字符")
    private String designer;
    /**
     * 监理单位
     */
    @ApiModelProperty(value = "监理单位", position = 39)
    @Size(max = 255, message = "监理单位不超过{max}个字符")
    private String supervisor;
    /**
     * 实际中标日期
     */
    @ApiModelProperty(value = "实际中标日期", position = 40)
    private Long successfulTime;
    /**
     * 实际签约日期
     */
    @ApiModelProperty(value = "实际签约日期", position = 41)
    private Long actualSignedTime;
    /**
     * 签约主体
     */
    @ApiModelProperty(value = "签约主体", position = 42)
    @Size(max = 255, message = "监理单位不超过{max}个字符")
    private String signedSubjectValue;

    /**
     * 签约主体Code
     */
    @ApiModelProperty(value = "签约主体", position = 42)
    @NotBlank(message = "签约主体Code不能为空")
    @Size(max = 100, message = "签约主体Code的长度必须小于{max}")
    private String signedSubjectCode;

    /**
     * 实施单位
     */
    @ApiModelProperty(value = "实施单位", position = 43)
    @Size(max = 255, message = "实施单位不超过{max}个字符")
    private String doUnit;
    /**
     * 含税合同总价（人民币）
     */
    @ApiModelProperty(value = "含税合同总价（人民币）", position = 44)
    private BigDecimal totalAmount;
    /**
     * 不含税金额
     */
    @ApiModelProperty(value = "不含税金额", position = 45)
    private BigDecimal noTaxIncludedMoney;
    /**
     * 自行施工不含税金额
     */
    @ApiModelProperty(value = "自行施工不含税金额", position = 46)
    private BigDecimal midAmountSelf;
    /**
     * 土建不含税金额
     */
    @ApiModelProperty(value = "土建不含税金额", position = 47)
    private BigDecimal selfCivilAmount;
    /**
     * 安装不含税金额
     */
    @ApiModelProperty(value = "安装不含税金额", position = 48)
    private BigDecimal selfInstallAmount;
    /**
     * 钢结构不含税金额
     */
    @ApiModelProperty(value = "钢结构不含税金额", position = 49)
    private BigDecimal selfSteelStructureAmount;
    /**
     * 总包服务费
     */
    @ApiModelProperty(value = "总包服务费", position = 50)
    private BigDecimal selfTotalServiceAmount;
    /**
     * 暂列金额或甲指分包金额
     */
    @ApiModelProperty(value = "暂列金额或甲指分包金额", position = 51)
    private BigDecimal subcontractAmount;
    /**
     * 销项税额
     */
    @ApiModelProperty(value = "销项税额", position = 52)
    private BigDecimal projectTaxAmount;
    /**
     * 合同优化条款
     */
    @ApiModelProperty(value = "合同优化条款", position = 53)
    @Size(max = 1024, message = "合同优化条款不超过1024个字符")
    private String contractOptimizeClause;
    /**
     * 合同优化金额
     */
    @ApiModelProperty(value = "合同优化金额", position = 54)
    private BigDecimal contractOptimizeAmount;
    /**
     * 合同优化率
     */
    @ApiModelProperty(value = "合同优化率", position = 55)
    @Size(max = 22, message = "合同优化率不超过{max}个字符")
    private String contractOptimizeRatio;
    /**
     * 暂列金额工作内容
     */
    @ApiModelProperty(value = "暂列金额工作内容", position = 56)
    @Size(max = 1024, message = "暂列金额工作内容不超过1024个字符")
    private String subcontractContent;
    /**
     * 中标项目经理
     */
    @ApiModelProperty(value = "中标项目经理", position = 57)
    @Size(max = 128, message = "中标项目经理不超过{max}个字符")
    private String bidManager;
    /**
     * 中标项目经理注册证书编号
     */
    @ApiModelProperty(value = "中标项目经理注册证书编号", position = 58)
    @Size(max = 128, message = "中标项目经理注册证书编号不超过{max}个字符")
    private String bidManagerCode;
    /**
     * 执行项目经理
     */
    @ApiModelProperty(value = "执行项目经理", position = 59)
    @Size(max = 128, message = "执行项目经理不超过{max}个字符")
    private String excuteManager;
    /**
     * 执行项目经理联系方式
     */
    @ApiModelProperty(value = "执行项目经理联系方式", position = 60)
    @Size(max = 64, message = "执行项目经理联系方式不超过{max}个字符")
    private String excuteManagerCode;
    /**
     * 合同项目经理
     */
    @ApiModelProperty(value = "合同项目经理", position = 61)
    @Size(max = 128, message = "合同项目经理联系方式不超过{max}个字符")
    private String contractManager;
    /**
     * 合同项目经理注册证书编号
     */
    @ApiModelProperty(value = "合同项目经理注册证书编号", position = 62)
    @Size(max = 128, message = "合同项目经理联系方式不超过{max}个字符")
    private String contractManagerCode;
    /**
     * 政府备案项目经理
     */
    @ApiModelProperty(value = "政府备案项目经理", position = 63)
    @Size(max = 128, message = "政府备案项目经理不超过{max}个字符")
    private String governmentManager;
    /**
     * 政府备案项目经理注册证书编号
     */
    @ApiModelProperty(value = "政府备案项目经理注册证书编号", position = 64)
    @Size(max = 128, message = "政府备案项目经理注册证书编号不超过{max}个字符")
    private String governmentManagerCode;
    /**
     * 承包模式
     */
    @ApiModelProperty(value = "承包模式", position = 65)
    @Size(max = 64, message = "承包模式不超过{max}个字符")
    private String contractMode1;
    /**
     * 承包模式2
     */
    @ApiModelProperty(value = "承包模式2", position = 66)
    @Size(max = 64, message = "承包模式2不超过{max}字符")
    private String contractMode2;
    /**
     * 合同承包范围
     */
    @ApiModelProperty(value = "合同承包范围", position = 67)
    @Size(max = 2048, message = "合同承包范围不超过{max}个字符")
    private String contractScope;
    /**
     * 发包人指定分包、独立分包的工程
     */
    @ApiModelProperty(value = "发包人指定分包、独立分包的工程", position = 68)
    @Size(max = 2048, message = "发包人指定分包、独立分包的工程不超过{max}}个字符")
    private String issuerProject;
    /**
     * 总工期（天）
     */
    @ApiModelProperty(value = "总工期（天）", position = 69)
    private Integer countDays;
    /**
     * 合同开工日期
     */
    @ApiModelProperty(value = "合同开工日期", position = 70)
    private Long workerBeginTime;
    /**
     * 合同竣工日期
     */
    @ApiModelProperty(value = "合同竣工日期", position = 71)
    private Long workerEndTime;
    /**
     * 实际开工日期
     */
    @ApiModelProperty(value = "实际开工日期", position = 72)
    private Long realWorkBeginTime;
    /**
     * 预计实际竣工日期
     */
    @ApiModelProperty(value = "预计实际竣工日期", position = 73)
    private Long predictWorkEndTime;
    /**
     * 工期奖罚类型
     */
    @ApiModelProperty(value = "工期奖罚类型", position = 74)
    @Size(max = 64, message = "工期奖罚类型不超过{max}个字符")
    private String workerDateRewardPunish;
    /**
     * 工期奖罚条款
     */
    @ApiModelProperty(value = "工期奖罚条款", position = 75)
    @Size(max = 512, message = "工期奖罚条款不超过{max}个字符")
    private String workerRewardPunishAppoint;
    /**
     * 合同范本类型
     */
    @ApiModelProperty(value = "合同范本类型", position = 76)
    @Size(max = 128, message = "合同范本类型不超过{max}个字符")
    private String contractStyle;
    /**
     * 质量要求
     */
    @ApiModelProperty(value = "质量要求", position = 77)
    @Size(max = 64, message = "质量要求不超过{max}个字符")
    private String qualityGuarantee;
    /**
     * 质量奖罚类型
     */
    @ApiModelProperty(value = "质量奖罚类型", position = 78)
    @Size(max = 64, message = "质量奖罚类型不超过{max}个字符")
    private String rewardPunishType;
    /**
     * 质量奖罚条款
     */
    @ApiModelProperty(value = "质量奖罚条款", position = 79)
    @Size(max = 2048, message = "质量奖罚类型不超过{max}个字符")
    private String rewardPunishTerms;
    /**
     * 安全文明施工要求
     */
    @ApiModelProperty(value = "安全文明施工要求", position = 80)
    @Size(max = 2048, message = "安全文明施工要求不超过{max}个字符")
    private String safetyRequirement;
    /**
     * 安全文明施工奖罚条款
     */
    @ApiModelProperty(value = "安全文明施工奖罚条款", position = 81)
    @Size(max = 2048, message = "安全文明施工奖罚条款不超过{max}个字符")
    private String safetyRewardPunishTerms;
    /**
     * 计价方式
     */
    @ApiModelProperty(value = "计价方式", position = 82)
    @Size(max = 64, message = "计价方式不超过{max}个字符")
    private String pricingMethod;
    /**
     * 合同形式
     */
    @ApiModelProperty(value = "合同形式", position = 83)
    @Size(max = 64, message = "合同形式不超过{max}个字符")
    private String contractForm;
    /**
     * 人工费是否可调
     */
    @ApiModelProperty(value = "人工费是否可调", position = 84)
    @Size(max = 2, message = "人工费是否可调不超过2个字符")
    private String costOfLaborChange;
    /**
     * 主材费是否可调
     */
    @ApiModelProperty(value = "主材费是否可调", position = 85)
    @Size(max = 2, message = "主材费是否可调不超过2个字符")
    private String costOfLaborChange2;
    /**
     * 是否有预付款
     */
    @ApiModelProperty(value = "是否有预付款", position = 86)
    @Size(max = 2, message = "是否有预付款不超过2个字符")
    private String advancesFlag;
    /**
     * 进度款付款方式
     */
    @ApiModelProperty(value = "进度款付款方式", position = 87)
    @Size(max = 64, message = "进度款付款方式不超过{max}个字符")
    private String advancesWay;
    /**
     * 月进度付款比例
     */
    @ApiModelProperty(value = "月进度付款比例", position = 88)
    @Size(max = 64, message = "月进度付款比例不超过{max}个字符")
    private String advancesMonthRate;
    /**
     * 竣工验收支付比例
     */
    @ApiModelProperty(value = "竣工验收支付比例", position = 89)
    @Size(max = 16, message = "竣工验收支付比例不超过{max}个字符")
    private String completedRate;
    /**
     * 竣工验收收款周期（月）
     */
    @ApiModelProperty(value = "竣工验收收款周期（月）", position = 90)
    @Size(max = 64, message = "竣工验收收款周期（月）不超过{64}个字符")
    private String completedCycle;
    /**
     * 结算支付比例
     */
    @ApiModelProperty(value = "结算支付比例", position = 91)
    @Size(max = 20, message = "结算支付比例不超过{max}个字符")
    private String settlementRate;
    /**
     * 结算周期（月）
     */
    @ApiModelProperty(value = "结算周期（月）", position = 92)
    @Size(max = 64, message = "结算周期（月）不超过{max}个字符")
    private String settlementCycle;
    /**
     * 保修金
     */
    @ApiModelProperty(value = "保修金", position = 93)
    @Size(max = 22, message = "保修金不超过{max}个字符")
    private String warrantyPremium;
    /**
     * 保修金比例
     */
    @ApiModelProperty(value = "保修金比例", position = 94)
    @Size(max = 50, message = "保修金比例不超过{max}个字符")
    private String warrantyPremiumRate;
    /**
     * 保修金支付方式
     */
    @ApiModelProperty(value = "保修金支付方式", position = 95)
    @Size(max = 128, message = "保修金支付方式不超过{max}个字符")
    private String warrantyPremiumWay;
    /**
     * 是否垫资
     */
    @ApiModelProperty(value = "是否垫资", position = 96)
    @Size(max = 2, message = "是否垫资不超过2个字符")
    private String advancesFundFlag;
    /**
     * 履约担保方式
     */
    @ApiModelProperty(value = "履约担保方式", position = 97)
    @Size(max = 64, message = "履约担保方式不超过{max}个字符")
    private String guaranteeWay;
    /**
     * 项目及土地是否合法
     */
    @ApiModelProperty(value = "项目及土地是否合法", position = 98)
    @Size(max = 2, message = "项目及土地是否合法不超过2个字符")
    private String landLegalityFlag;
    /**
     * 是否放弃优先受偿权
     */
    @ApiModelProperty(value = "是否放弃优先受偿权", position = 99)
    @Size(max = 2, message = "是否放弃优先受偿权不超过2个字符")
    private String giveUpCompensateFlag;
    /**
     * 付款比例是否低于百分之八十
     */
    @ApiModelProperty(value = "付款比例是否低于百分之八十", position = 100)
    @Size(max = 4, message = "付款比例是否低于百分之八十不超过4个字符")
    private String payRateLessEightyFlag;
    /**
     * 支付节点时间是否超2个月
     */
    @ApiModelProperty(value = "支付节点时间是否超2个月", position = 101)
    @Size(max = 4, message = "支付节点时间是否超2个月不超过4个字符")
    private String nodeMoreTwoMonthFlag;
    /**
     * 是否首合同
     */
    @ApiModelProperty(value = "是否首合同", position = 102)
    private Integer isFirstContract;

    /**
     * 源文件所属ID
     */
    @ApiModelProperty(value = "源文件所属ID", position = 97)
    @NotNull(message = "源文件所属id不能为空")
    private Long BelongId;

    /**
     * 客户级别
     */
    @ApiModelProperty(value = "客户级别", position = 98)
    @Size(max = 50, message = "客户级别不超过50个字符")
    private String customerLevel;

    /**
     * 其他金额
     */
    @ApiModelProperty(value = "其他金额", position = 99)
    private BigDecimal selfOtherAmount;

    /**
     * 其他金额
     */
    @ApiModelProperty(value = "支付方式", position = 100)
    private String payTypeNew;

    /**
     * 工程类型Code（国家标准）
     */
    @ApiModelProperty(value = "工程类型Code（国家标准）")
    private String countryProjectTypeCode;

    /**
     * 工程类型code（总公司市场口径）
     */
    @ApiModelProperty(value = "工程类型code（总公司市场口径）")
    private String marketProjectTypeCode;

    /**
     * 工程类型code（总公司市场口径）2
     */
    @ApiModelProperty(value = "工程类型code（总公司市场口径）2")
    private String marketProjectType2Code;

    /**
     * 工程类型code(总公司综合口径)
     */
    @ApiModelProperty(value = "工程类型code(总公司综合口径)")
    private String projectTypeCode;

    /**
     * 工程类型code(总公司综合口径)2
     */
    @ApiModelProperty(value = "工程类型code(总公司综合口径)2")
    private String projectType2Code;

    /**
     * 工程类型code(总公司综合口径)3
     */
    @ApiModelProperty(value = "工程类型code(总公司综合口径)3")
    private String projectType3Code;

    /**
     * 工程类型code(总公司综合口径)4
     */
    @ApiModelProperty(value = "工程类型code(总公司综合口径)4")
    private String projectType4Code;

    /**
     * 工程承包模式Code
     */
    @ApiModelProperty(value = "工程承包模式Code")
    private String contractMode1Code;

    /**
     * 施工承包模式Code
     */
    @ApiModelProperty(value = "施工承包模式Code")
    private String contractMode2Code;

    /**
     * 投资主体Code
     */
    @ApiModelProperty(value = "投资主体Code")
    private String investorsCode;

    /**
     * 业务类型Code
     */
    @ApiModelProperty(value = "业务类型Code")
    private String businessTypeCode;

    /**
     * 客户级别Code
     */
    @ApiModelProperty(value = "客户级别Code")
    private String customerLevelCode;

    /**
     * 客户企业性质Code
     */
    @ApiModelProperty(value = "客户企业性质Code")
    private String enterpriseTypeCode;

    /**
     * 进度款付款方式Code
     */
    @ApiModelProperty(value = "进度款付款方式Code")
    private String paymentTypeCode;


    /**
     * 保修金支付方式Code
     */
    @ApiModelProperty(value = "质量奖罚类型Code")
    private String qualityAwardTypeCode;

    /**
     * 客户id
     */
    @ApiModelProperty(value = "客户id")
    private String customerId;
}
