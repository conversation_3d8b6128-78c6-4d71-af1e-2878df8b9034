
package com.cscec3b.iti.projectmanagement.api.dto.request.task;

import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;
import lombok.experimental.Accessors;

import javax.validation.constraints.NotNull;
import java.io.Serializable;

/**
 * @description 任务状态请求数据
 * <AUTHOR>
 * @date 2022/10/19
 */
@Data
@Accessors(chain = true)
@ApiModel(value = "TaskStatusReq", description = "任务状态请求数据")
public class TaskStatusReq implements Serializable {

    private static final long serialVersionUID = 1L;

    @NotNull(message = "主键id不能为空")
    @ApiModelProperty(value = "主键id", required = true)
    private Long id;

    @ApiModelProperty(value = "状态")
    private Integer status;
}
