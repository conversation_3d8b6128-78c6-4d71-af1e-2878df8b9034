package com.cscec3b.iti.projectmanagement.api.dto.request.civilmilitary;

import com.cscec3b.iti.projectmanagement.api.dto.request.BasePage;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;
import lombok.experimental.Accessors;

/**
 * @description: CreateSpecialProjectReq
 * <AUTHOR>
 * @date 2023/2/14 9:29
 */

@Data
@Accessors(chain = true)
@ApiModel(value = "QuerySpecialProjectParams", description = "查询特殊立项请求对象")
public class QueryCivilMilitaryIntegrationParams extends BasePage {

    @ApiModelProperty("云枢执行单位IdPath")
    private String yunshuExecuteUnitIdPath;



    /**
     * 项目地址
     */
    @ApiModelProperty(value = "项目地址")
    private String projectAddress;

    @ApiModelProperty(value = "工程名称")
    private String projectName;



    /**
     * 局标准分类编码路径
     */
    @ApiModelProperty(value = "局标准分类编码路径")
    private String standardTypeCodePath;


}

