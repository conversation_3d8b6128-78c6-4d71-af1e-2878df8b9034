package com.cscec3b.iti.projectmanagement.api;

import org.springframework.web.bind.annotation.CrossOrigin;
import org.springframework.web.bind.annotation.GetMapping;
import org.springframework.web.bind.annotation.PathVariable;
import org.springframework.web.servlet.mvc.method.annotation.SseEmitter;

import io.swagger.annotations.ApiOperation;
import io.swagger.annotations.ApiParam;

/**
 * SSE 消息推送控制器
 * <AUTHOR>
 * @date 2023-01-2023/1/11 18:02
 */
public interface ISseApi {

    /**
     * 路径
     */
    String PATH = "/sse";

    /**
     * sse 通道连接
     * @param key
     * @return
     * <AUTHOR>
     * @date 2023/10/8
     */
    @CrossOrigin
    @GetMapping("/connect/{key}")
    @ApiOperation(value = "sse 通道连接")
    SseEmitter connect(@ApiParam("key") @PathVariable String key);


}
