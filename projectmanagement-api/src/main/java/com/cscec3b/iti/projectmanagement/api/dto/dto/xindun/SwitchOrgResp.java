package com.cscec3b.iti.projectmanagement.api.dto.dto.xindun;

import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

import java.io.Serializable;

@Data
@ApiModel(description = "用户当前组织信息")
public class SwitchOrgResp implements Serializable {
    private static final long serialVersionUID = 1L;

    /**
     * orgId
     */
    @ApiModelProperty(value = "组织ID")
    private String orgId;

    /**
     * 组织简称
     */
    @ApiModelProperty(value = "组织简称")
    private String orgShortName;

}

