package com.cscec3b.iti.projectmanagement.api.dto.dto;

import io.swagger.annotations.ApiModelProperty;
import lombok.Data;
import lombok.experimental.Accessors;

import java.io.Serializable;
import java.util.ArrayList;
import java.util.List;
import java.util.Objects;

/**
 * 执行单元树dto
 *
 * <AUTHOR>
 * @date 2023/08/23
 */
@Data
@Accessors(chain = true)
public class ExecuteUnitTreeDto implements Serializable {
    @ApiModelProperty(value = "执行单位组织id(treeId)")
    private String id;
    @ApiModelProperty(value = "执行单位组织idPath(从根节点到当前节点)")
    private String idPath;
    @ApiModelProperty(value = "执行单位部门id")
    private String deptId;
    @ApiModelProperty(value = "执行单位组织编码(部门)")
    private String code;
    @ApiModelProperty(value = "执行单位全名")
    private String name;
    @ApiModelProperty(value = "执行单位简称")
    private String abbreviation;
    @ApiModelProperty(value = "执行单位父节点treeId")
    private String parentId;
    @ApiModelProperty(value = "执行单位排序编号")
    private String deptSort;
    @ApiModelProperty(value = "是否叶子节点")
    private Boolean leaf = Boolean.FALSE;
    @ApiModelProperty(value = "组织类型: 10:局; 15:托管单位; 20:分局; 30:公司; 40:分公司; 45:专业公司; 47:城市公司; 50:项目经理部; 60:指挥部; 70:项目部; 80:机关; 90:部门; ")
    private Integer orgType;
    @ApiModelProperty(value = "能否直接下设项目部或指挥部: 0:不可添加; 1:可以添加;")
    private Integer isSetupSub;
    @ApiModelProperty(value = "组织层级")
    private Integer level;
    @ApiModelProperty(value = "同步标识")
    private String syncMark;

    @ApiModelProperty(value = "执行单位全名路径")
    private String idPathName;
    @ApiModelProperty(value = "执行单位简称路径")
    private String idPathAbbreviation;

    /**
     * 树名称
     */
    @ApiModelProperty(value = "树名称")
    private String treeName;

    /**
     * 组织标签编码
     */
    @ApiModelProperty(value = "组织标签编码")
    private String orgMarkCode;
    /**
     * 组织标签code
     */
    @ApiModelProperty(value = "组织标签名称")
    private String orgMarkName;


    private List<ExecuteUnitTreeDto> children = new ArrayList<>();

    @Override
    public boolean equals(Object o) {
        if (this == o) {
            return true;
        }
        if (!(o instanceof ExecuteUnitTreeDto)) {
            return false;
        }
        final ExecuteUnitTreeDto that = (ExecuteUnitTreeDto) o;
        return Objects.equals(id, that.id);
    }

    @Override
    public int hashCode() {
        return Objects.hash(id, deptId);
    }
}

