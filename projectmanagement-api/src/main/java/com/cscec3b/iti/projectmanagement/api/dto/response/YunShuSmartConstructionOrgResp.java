package com.cscec3b.iti.projectmanagement.api.dto.response;

import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

/**
 * <AUTHOR>
 * @date 2023/9/14 16:43
 */
@Data
public class YunShuSmartConstructionOrgResp {

    @ApiModelProperty(value = "id")
    private String id;
    @ApiModelProperty(value = "原系统部门标识")
    private String orgID;
    @ApiModelProperty(value = "原系统部门编码")
    private String orgCode;
    @ApiModelProperty(value = "部门名称")
    private String orgFullName;
    @ApiModelProperty(value = "部门简称")
    private String orgShortName;
    @ApiModelProperty(value = "部门英文名")
    private String orgEnglishName;
    @ApiModelProperty(value = "部门所在地")
    private String orgAdress;
    @ApiModelProperty(value = "部门所在地GPS")
    private String orgAdressGPS;
    @ApiModelProperty(value = "部门所属国家")
    private String country;
    @ApiModelProperty(value = "父部门ID【用于获取部门信息】(父部门是顶层时为组织关系编码)")
    private String parentId;
    @ApiModelProperty(value = "部门树ID")
    private String treeId;
    @ApiModelProperty(value = "父部门树ID【用于获取下级部门】")
    private String treeParentId;
    @ApiModelProperty(value = "queryCode")
    private String queryCode;
    @ApiModelProperty(value = "下级")
    private YunShuSmartConstructionOrgResp child;
}
