package com.cscec3b.iti.projectmanagement.api.dto.request.open;

import com.cscec3b.iti.projectmanagement.api.dto.request.BasePage;
import com.cscec3b.iti.projectmanagement.api.validation.annotation.MultiFieldCheck;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;
import lombok.EqualsAndHashCode;

import java.io.Serializable;
import java.util.ArrayList;
import java.util.List;

@EqualsAndHashCode(callSuper = true)
@Data
@ApiModel("工程项目档案分页查询参数")
@MultiFieldCheck.Rules({
        @MultiFieldCheck(message = "cpmProjectKeys和projectIds不能同时为空，必须传入其中一个参数",
                conditional = "true",
                checkRule = "!#cpmProjectKeys.empty || !#projectIds.empty"),
        @MultiFieldCheck(message = "cpmProjectKeys和projectIds不能同时传入，只能传入其中一个参数",
                conditional = "!#cpmProjectKeys.empty && !#projectIds.empty",
                checkRule = "false")
})
public class OpenEngineeringMappingArchiveReq extends BasePage implements Serializable {

    /**
     * 项目标识
     */
    @ApiModelProperty(value = "施工项目标识", notes = "不支持模糊查询")
    private List<String> cpmProjectKeys = new ArrayList<>();

    /**
     * 项目id
     */
    @ApiModelProperty(value = "项目id", notes = "不支持模糊查询")
    private List<Long> projectIds = new ArrayList<>();

}
