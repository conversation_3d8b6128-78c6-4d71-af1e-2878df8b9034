package com.cscec3b.iti.projectmanagement.api.dto.request.open;

import com.cscec3b.iti.projectmanagement.api.dto.request.BasePage;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;
import lombok.EqualsAndHashCode;

import javax.validation.constraints.NotEmpty;
import java.io.Serializable;
import java.util.ArrayList;
import java.util.List;

@EqualsAndHashCode(callSuper = true)
@Data
@ApiModel("工程项目档案分页查询参数")
public class OpenEngineeringMappingArchiveReq extends BasePage implements Serializable {

    /**
     * 项目标识
     */
    @ApiModelProperty(value = "施工项目标识", notes = "不支持模糊查询")
    @NotEmpty(message = "施工项目标识不能为空")
    private List<String> cpmProjectKeys = new ArrayList<>();


}
