package com.cscec3b.iti.projectmanagement.api;

import org.springframework.validation.annotation.Validated;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.PutMapping;
import org.springframework.web.bind.annotation.RequestBody;

import com.cscec3b.iti.common.base.api.GenericityResponse;
import com.cscec3b.iti.common.base.page.Page;
import com.cscec3b.iti.projectmanagement.api.dto.request.task.TaskQry;
import com.cscec3b.iti.projectmanagement.api.dto.request.task.TaskReq;
import com.cscec3b.iti.projectmanagement.api.dto.request.task.TaskStatusReq;
import com.cscec3b.iti.projectmanagement.api.dto.response.task.TaskResp;

import io.swagger.annotations.ApiOperation;
import io.swagger.annotations.ApiParam;

/**
 *
 */
public interface ITaskController {

    /**
     * 路径
     */
    String PATH = "/task";

    /**
     * 创建待办任务
     * @param taskReq
     * @return
     * <AUTHOR>
     * @date 2023/10/8
     */
    @PostMapping("/create")
    @ApiOperation(value = "创建待办任务")
    GenericityResponse<Boolean> createTask(@ApiParam(value = "任务参数") @RequestBody TaskReq taskReq);

    /**
     * 更新任务状态
     * @param statusReq
     * @return
     * <AUTHOR>
     * @date 2023/10/8
     */
    @PutMapping("/status/update")
    @ApiOperation(value = "更新任务状态")
    GenericityResponse<Boolean> updateStatus(@Validated @ApiParam("任务参数") @RequestBody TaskStatusReq statusReq);

    /**
     * 查询任务列表
     * @param taskQry
     * @return
     * <AUTHOR>
     * @date 2023/10/8
     */
    @PostMapping("/list/page")
    @ApiOperation(value = "查询任务列表")
    GenericityResponse<Page<TaskResp>> pageList(@Validated @ApiParam("任务查询条件") @RequestBody TaskQry taskQry);
}
