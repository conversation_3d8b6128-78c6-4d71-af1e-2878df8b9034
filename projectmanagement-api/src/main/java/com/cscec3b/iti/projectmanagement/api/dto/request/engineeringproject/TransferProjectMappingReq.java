package com.cscec3b.iti.projectmanagement.api.dto.request.engineeringproject;

import java.io.Serializable;

import javax.validation.constraints.NotNull;

import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

/**
 * 转移施工项目关联请求参数
 * 
 * <AUTHOR>
 * @date 2025/03/13
 */
@Data
@ApiModel(value = "转移施工项目关联请求参数")
public class TransferProjectMappingReq implements Serializable {

    private static final long serialVersionUID = 1L;

    /**
     * 施工项目id
     */
    @ApiModelProperty(value = "施工项目id", required = true)
    @NotNull(message = "施工项目id不能为空")
    private Long projectId;

    /**
     * 当前工程项目id（需要解除关联的工程项目）
     */
    @ApiModelProperty(value = "当前工程项目id（需要解除关联的工程项目）", required = true)
    @NotNull(message = "当前工程项目id不能为空")
    private Long currentEngineeringProjectId;

    /**
     * 目标工程项目id（需要重新关联的工程项目）
     */
    @ApiModelProperty(value = "目标工程项目id（需要重新关联的工程项目）", required = true)
    @NotNull(message = "目标工程项目id不能为空")
    private Long targetEngineeringProjectId;
}
