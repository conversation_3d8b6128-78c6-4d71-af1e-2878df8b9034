package com.cscec3b.iti.projectmanagement.api.dto.request.open;

import java.io.Serializable;

import javax.validation.constraints.NotBlank;

import com.cscec3b.iti.projectmanagement.api.dto.request.BasePage;

import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;
import lombok.EqualsAndHashCode;

@EqualsAndHashCode(callSuper = true)
@Data
@ApiModel(value = "项目列表分页请信息")
public class OpenEngineerProjectPageReq extends BasePage implements Serializable {

    /**
     * 工程项目标识
     */
    @ApiModelProperty(value = "工程项目标识, 模糊查询")
    private String engineerKey;

    /**
     * 工程项目名称
     */
    @ApiModelProperty(value = "工程项目名称, 模糊查询")
    private String engineerName;

    /**
     * 项目编码
     */
    @ApiModelProperty(value = "项目编码, 模糊查询")
    private String engineerCode;

    /**
     * 执行单位组织id
     */
    @ApiModelProperty(value = "执行单位组织id,查询本下")
    @NotBlank(message = "执行单位组织id不能为空")
    private String executeUnitId;

    /**
     * 执行单位名称
     */
    @ApiModelProperty(value = "执行单位名称, 模糊查询")
    private String executeUnitName;

    /**
     * 项目部门组织id
     */
    @ApiModelProperty(value = "项目部门组织id,精准查询")
    private String projectDeptId;

    /**
     * 项目部名称
     */
    @ApiModelProperty(value = "项目部名称，模糊查询")
    private String projectDeptName;


}
