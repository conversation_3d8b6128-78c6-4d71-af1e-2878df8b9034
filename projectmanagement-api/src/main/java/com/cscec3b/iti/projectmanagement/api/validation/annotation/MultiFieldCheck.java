package com.cscec3b.iti.projectmanagement.api.validation.annotation;

import com.cscec3b.iti.projectmanagement.api.validation.validator.MultiFieldValidator;

import javax.validation.Constraint;
import javax.validation.Payload;
import java.lang.annotation.*;

/**
 * 多字段联合校验器，放在类上，校验类中多个字段之间的逻辑关系
 *
 * <AUTHOR>
 * @date 2024/10/15
 */
@Documented
@Retention(RetentionPolicy.RUNTIME)
@Target({ElementType.TYPE})
@Constraint(validatedBy = {MultiFieldValidator.class})
@Repeatable(MultiFieldCheck.Rules.class)
public @interface MultiFieldCheck {

    /**
     * 校验不通过时的提示信息
     *
     * @return {@link String }
     */
    String message() default "字段关系校验不通过";


    /**
     * 校验组
     *
     * @return {@link Class }<{@link ? }>{@link [] }
     */
    Class<?>[] groups() default {};

    /**
     * 负载
     *
     * @return {@link Class }<{@link ? } {@link extends } {@link Payload }>{@link [] }
     */
    Class<? extends Payload>[] payload() default {};

    /**
     * 条件规则 spEl表达式,
     * <BR> 如conditional = "#field.equals('b')", checkRule = "#field2 > 5", 表示filed为b，filed2需要大于5, filed不为b 则跳过
     * <BR> conditional = "true", checkRule = "#field != null || field1 != null || field2 != null",
     * 表示field、field1、field2至少有一个不为空
     *
     * @return {@link String }
     */
    String conditional() default "true";

    /**
     * 条件校验规则 spEl表达式
     *
     * @return {@link String }
     */
    String checkRule();

    @Target({ElementType.TYPE})
    @Retention(RetentionPolicy.RUNTIME)
    @Documented
    public @interface Rules {
        MultiFieldCheck[] value();
    }
}
