package com.cscec3b.iti.projectmanagement.api.dto.request;

import com.baomidou.mybatisplus.annotation.TableField;
import com.fasterxml.jackson.annotation.JsonProperty;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;
import lombok.experimental.Accessors;

import javax.validation.Valid;
import javax.validation.constraints.NotNull;
import javax.validation.constraints.Pattern;
import javax.validation.constraints.Size;
import java.io.Serializable;
import java.math.BigDecimal;
import java.util.List;
import java.util.Map;

/**
 * 投标总结入参对象
 *
 * <AUTHOR>
 * @Description
 * @Date 2022/10/19 11:05
 */
@Data
@Accessors(chain = true)
@ApiModel(value = "BidSummaryReq", description = "投标总结")
public class BidSummaryReq implements Serializable {
    private static final long serialVersionUID = 1L;

    /**
     * 独立文件id
     */
    @ApiModelProperty(value = "独立文件id", position = 1)
    @NotNull(message = "独立文件id不能为空")
    private Long originFileId;

    /**
     * 复核审批用户id
     */
    @ApiModelProperty(value = "复核审批用户id", position = 2)
    @Size(min = 1, max = 20, message = "复核审批用户id长度要求[{min},{max}]")
    private String approvalPerson;

    /**
     * 发起人单位
     */
    @ApiModelProperty(value = "发起人", position = 3)
    @Size(max = 50, message = "发起人长度不超过50个字符")
    private String submitPerson;

    /**
     * 是否创建指挥部
     */
    @ApiModelProperty(value = "是否创建指挥部", position = 4)
    @Size(min = 1, max = 2, message = "是否创建指挥部长度要求[{min},{max}]")
    private String isCreateHead;

    /**
     * 执行单位
     */
    @ApiModelProperty(value = "执行单位", position = 5)
    @Size(max = 200, message = "执行单位长度不超过128个字符")
    private String executeUnit;

    @ApiModelProperty(value = "执行单位简称", position = 5)
    @Size(max = 200, message = "执行单位简称长度不超过{max}个字符")
    private String executeUnitAbbreviation;


    /**
     * 独立性
     */
    @ApiModelProperty(value = "独立性", position = 6)
    @Size(max = 2, message = "独立性不超过2个字符")
    private String isIndependent;

    /**
     * 工程编号
     */
    @ApiModelProperty(value = "工程编号", position = 7)
    @Size(min = 1, max = 32, message = "工程编号长度要求[{min},{max}]")
    private String projectCode;

    /**
     * 工程名称
     */
    @ApiModelProperty(value = "工程名称", position = 8)
    @Size(min = 1, max = 128, message = "工程名称长度要求[{min},{max}]")
    private String projectName;

    /**
     * 工程简称
     */
    @ApiModelProperty(value = "工程简称", position = 9)
    @Size(max = 255, message = "工程简称不超过[{max}]个字符")
    private String projectAbbreviation;

    /**
     * 工程属地
     */
    @ApiModelProperty(value = "工程属地", position = 10)
    @Size(min = 1, max = 128, message = "工程属地长度要求[{min},{max}]")
    private String projectBelong;

    /**
     * 所属办事处
     */
    @ApiModelProperty(value = "所属办事处", position = 11)
    @Size(max = 128, message = "所属办事处不超过{max}个字符")
    private String signFormOffice;

    /**
     * 省
     */
    @ApiModelProperty(value = "省", position = 12)
    @Size(max = 50, message = "省名称长度不超过50个字符")
    private String province;

    /**
     * 市
     */
    @ApiModelProperty(value = "市", position = 13)
    @Size(max = 50, message = "市名称长度不超过50个字符")
    private String city;

    /**
     * 区
     */
    @ApiModelProperty(value = "区", position = 14)
    @Size(max = 50, message = "区名称长度不超过50个字符")
    private String region;

    /**
     * 具体地址
     */
    @ApiModelProperty(value = "具体地址", position = 15)
    @Size(max = 255, message = "具体地址长度不超过{max}个字符")
    private String address;

    /**
     * 国别
     */
    @ApiModelProperty(value = "国别", position = 16)
    @Size(max = 128, message = "国名称长度不超过{max}个字符")
    private String country;

    /**
     * 工程类型（国家标准）
     */
    @ApiModelProperty(value = "工程类型（国家标准）", position = 17)
    @Size(min = 1, max = 32, message = "工程类型（国家标准）长度要求[{min},{max}]")
    private String countryProjectType;

    /**
     * 工程类型（总公司市场口径）
     */
    @ApiModelProperty(value = "工程类型（总公司市场口径）", position = 18)
    @Size(min = 1, max = 32, message = "工程类型（总公司市场口径）长度要求[{min},{max}]")
    private String marketProjectType;

    /**
     * 工程类型(总公司市场口径)2
     */
    @ApiModelProperty(value = "工程类型(总公司市场口径)2", position = 19)
    @Size(max = 32, message = "工程类型(总公司市场口径)2不超过{max}个字符")
    private String marketProjectType2;

    /**
     * 工程类型(总公司综合口径)
     */
    @ApiModelProperty(value = "工程类型(总公司综合口径)", position = 20)
    @Size(min = 1, max = 32, message = "工程类型（总公司综合口径）长度要求[{min},{max}]")
    private String projectType;

    /**
     * 工程类型(总公司综合口径)2
     */
    @ApiModelProperty(value = "工程类型(总公司综合口径)2", position = 21)
    @Size(max = 32, message = "工程类型(总公司综合口径)2不超过{max}个字符")
    private String projectType2;

    /**
     * 工程类型(总公司综合口径)3
     */
    @ApiModelProperty(value = "工程类型(总公司综合口径)3", position = 22)
    @Size(max = 32, message = "工程类型(总公司综合口径)3不超过{max}个字符")
    private String projectType3;

    /**
     * 工程类型(总公司综合口径)4
     */
    @ApiModelProperty(value = "工程类型(总公司综合口径)4", position = 23)
    @Size(max = 32, message = "工程类型(总公司综合口径)4不超过{max}个字符")
    private String projectType4;

    /**
     * 项目附件信息对象
     */
    @ApiModelProperty(value = "项目附件信息对象", position = 24)
    @JsonProperty("projectAttachment")
    private List<Map> projectAttachment1;

    /**
     * 总分包类别
     */
    @ApiModelProperty(value = "总分包类别", position = 25)
    @Size(max = 32, message = "总分包类别不超过32个字符")
    private String totalSubcontractingCategory;

    /**
     * 是否投资项目
     */
    @ApiModelProperty(value = "是否投资项目", position = 26)
    @Size(min = 1, max = 32, message = "是否投资项目长度要求[{min},{max}]")
    private String investmentProjects;

    /**
     * 投资主体
     */
    @ApiModelProperty(value = "投资主体", position = 27)
    @Size(max = 20, message = "投资主体不超过20个字符")
    private String investors;

    /**
     * 结构形式
     */
    @ApiModelProperty(value = "结构形式", position = 28)
    @Size(max = 64, message = "结构形式不超过{max}个字符")
    private String structuralStyle;

    /**
     * 结构形式2
     */
    @ApiModelProperty(value = "结构形式2", position = 29)
    @Size(max = 64, message = "结构形式2不超过{max}个字符")
    private String structuralStyle2;

    /**
     * 是否有钢结构
     */
    @ApiModelProperty(value = "是否有钢结构", position = 30)
    @Size(max = 4, message = "是否有钢结构不超过{max}个字符")
    private String includingSteel;

    /**
     * 钢结构体量
     */
    @ApiModelProperty(value = "钢结构体量", position = 31)
    @Size(max = 128, message = "钢结构体量不超过{max}个字符")
    private String volume;

    /**
     * 是否装配式
     */
    @ApiModelProperty(value = "是否装配式", position = 32)
    @Size(max = 4, message = "是否装配式不超过{max}个字符")
    private String fabricated;

    /**
     * 预计造价
     */
    @ApiModelProperty(value = "预计造价", position = 33)
    private BigDecimal estimatedCost;

    /**
     * 业务类型
     */
    @ApiModelProperty(value = "业务类型", position = 34)
    @Size(min = 1, max = 32, message = "业务类型长度要求[{min},{max}]")
    private String businessType;

    /**
     * 客户名称
     */
    @ApiModelProperty(value = "客户名称", position = 35)
    @Size(min = 1, max = 255, message = "客户名称长度要求[{min},{max}]")
    private String customerName;

    /**
     * 上级相关方
     */
    @ApiModelProperty(value = "上级相关方", position = 36)
    @Size(max = 255, message = "上级相关方不超过{max}个字符")
    private String superiorCompanyName;

    /**
     * 客户企业性质
     */
    @ApiModelProperty(value = "客户企业性质", position = 37)
    @Size(min = 1, max = 255, message = "客户企业性质长度要求[{min},{max}]")
    private String enterpriseType;

    /**
     * 建设单位（甲方）联系人
     */
    @ApiModelProperty(value = "建设单位（甲方）联系人", position = 38)
    @Size(max = 255, message = "建设单位（甲方）联系人不超过{max}个字符")
    private String contactPerson;

    /**
     * 建设单位（甲方）联系人电话
     */
    @ApiModelProperty(value = "建设单位（甲方）联系人电话", position = 39)
    @Size(max = 64, message = "建设单位（甲方）联系人电话不超过{max}个字符")
    private String contactPersonMobile;

    /**
     * 设计单位
     */
    @ApiModelProperty(value = "设计单位", position = 40)
    @Size(max = 255, message = "设计单位不超过{max}个字符")
    private String designer;

    /**
     * 监理单位
     */
    @ApiModelProperty(value = "监理单位", position = 41)
    @Size(max = 255, message = "监理单位不超过{max}个字符")
    private String supervisor;

    /**
     * 招标代理机构
     */
    @ApiModelProperty(value = "招标代理机构", position = 42)
    @Size(max = 255, message = "招标代理机构不超过{max}个字符")
    private String biddingAgency;

    /**
     * 开标时间
     */
    @ApiModelProperty(value = "开标时间", position = 43)
    private Long bidOpeningTime;

    /**
     * 定标时间
     */
    @ApiModelProperty(value = "定标时间", position = 44)
    private Long calibrationTime;

    /**
     * 参与开标人员
     */
    @ApiModelProperty(value = "参与开标人员", position = 45)
    @Size(max = 128, message = "参与开标人员不超过{max}个字符")
    private String bidOpeningPersonnel;

    /**
     * 是否中标
     */
    @ApiModelProperty(value = "是否中标", position = 46)
    @Size(max = 32, message = "是否中标不超过{max}个字符")
    private String inBidType;

    /**
     * 中标主体
     */
    @ApiModelProperty(value = "中标主体", position = 47)
    @Size(max = 20, message = "中标主体不超过20个字符")
    private String inBidValue;

    /**
     * 是否纳入统计
     */
    @ApiModelProperty(value = "是否纳入统计", position = 48)
    @Size(max = 2, message = "是否纳入统计不超过2个字符")
    private String isStatistic;

    /**
     * 承包模式
     */
    @ApiModelProperty(value = "承包模式", position = 49)
    @Size(max = 64, message = "承包模式不超过{max}个字符")
    private String contractMode1;

    /**
     * 承包模式2
     */
    @ApiModelProperty(value = "承包模式2", position = 50)
    @Size(max = 64, message = "承包模式2不超过{max}个字符")
    private String contractMode2;

    /**
     * 招标模式
     */
    @ApiModelProperty(value = "招标模式", position = 51)
    @Size(max = 64, message = "招标模式不超过{max}个字符")
    private String biddingType;

    /**
     * 招标范围
     */
    @ApiModelProperty(value = "招标范围", position = 52)
    @Size(max = 1024, message = "招标范围不超过{max}个字符")
    private String biddingRange;

    /**
     * 发包人指定分包、独立分包的工程
     */
    @ApiModelProperty(value = "发包人指定分包、独立分包的工程", position = 53)
    @Size(max = 128, message = "发包人指定分包、独立分包的工程 不超过128个字符")
    private String subpackageEngineering;

    /**
     * 交标时间
     */
    @ApiModelProperty(value = "交标时间", position = 54)
    private Long crossBiddingTime;

    /**
     * 开工时间
     */
    @ApiModelProperty(value = "开工时间", position = 55)
    private Long startTime;

    /**
     * 竣工时间
     */
    @ApiModelProperty(value = "竣工时间", position = 56)
    private Long beCompletedTime;

    /**
     * 总工期（天）
     */
    @ApiModelProperty(value = "总工期（天）", position = 57)
    @Size(min = 1, max = 10, message = "总工期（天）不超过10个字符")
    @Pattern(regexp = "^[0-9]+$", message = "总工期必须为整数")
    private String totalDuration;

    /**
     * 工期奖罚类型
     */
    @ApiModelProperty(value = "工期奖罚类型", position = 58)
    @Size(max = 32, message = "工期奖罚类型不超过{max}个字符")
    private String durationAwardType;

    /**
     * 工期奖罚条款
     */
    @ApiModelProperty(value = "工期奖罚条款", position = 59)
    private String durationAwardClause;

    /**
     * 质量要求
     */
    @ApiModelProperty(value = "质量要求", position = 60)
    @Size(max = 20, message = "质量要求不超过20个字符")
    private String qualityRequirement;

    /**
     * 质量奖罚类型
     */
    @ApiModelProperty(value = "质量奖罚类型", position = 61)
    @Size(max = 20, message = "质量奖罚类型不超过20个字符")
    private String qualityAwardType;

    /**
     * 质量奖罚条款
     */
    @ApiModelProperty(value = "质量奖罚条款", position = 62)
    private String qualityAwardClause;

    /**
     * 安全文明施工要求
     */
    @ApiModelProperty(value = "安全文明施工要求", position = 63)
    private String safeConstruction;

    /**
     * 安全文明施工要求奖罚
     */
    @ApiModelProperty(value = "安全文明施工要求奖罚", position = 64)
    private String safeConstructionAward;

    /**
     * 计价方式
     */
    @ApiModelProperty(value = "计价方式", position = 65)
    @Size(max = 64, message = "计价方式不超过{max}个字符")
    private String pricingMethod;

    /**
     * 合同形式
     */
    @ApiModelProperty(value = "合同形式", position = 66)
    @Size(max = 64, message = "合同形式不超过{max}个字符")
    private String contractForm;

    /**
     * 人工费可调
     */
    @ApiModelProperty(value = "人工费可调", position = 67)
    @Size(max = 32, message = "人工费可调不超过{max}个字符")
    private String laborCostType;

    /**
     * 主材费可调
     */
    @ApiModelProperty(value = "主材费可调", position = 68)
    @Size(max = 32, message = "主材费可调不超过{max}个字符")
    private String materialScienceType;

    /**
     * 是否有预付款
     */
    @ApiModelProperty(value = "是否有预付款", position = 69)
    @Size(max = 32, message = "是否有预付款不超过{max}个字符")
    private String advanceChargeType;

    /**
     * 进度款付款方式
     */
    @ApiModelProperty(value = "进度款付款方式", position = 70)
    @Size(max = 128, message = "进度款付款方式不超过{max}个字符")
    private String paymentType;

    /**
     * 竣工验收支付比例
     */
    @ApiModelProperty(value = "竣工验收支付比例", position = 71)
    @Size(max = 22, message = "竣工验收支付比例不超过{max}个字符")
    private String beCompletedProportion;

    /**
     * 竣工验收收款周期（月）
     */
    @ApiModelProperty(value = "竣工验收收款周期（月）", position = 72)
    @Size(max = 64, message = "竣工验收收款周期（月）不超过{max}个字符")
    private String beCompletedCycle;

    /**
     * 结算支付比例
     */
    @ApiModelProperty(value = "结算支付比例", position = 73)
    @Size(max = 22, message = "结算支付比例不超过{max}个字符")
    private String settlementPaymentProportion;

    /**
     * 结算周期（月）
     */
    @ApiModelProperty(value = "结算周期（月）", position = 74)
    @Size(max = 64, message = "结算周期（月）不超过{max}个字符")
    private String settlementCycle;

    /**
     * 是否垫资
     */
    @ApiModelProperty(value = "是否垫资", position = 75)
    @Size(max = 32, message = "是否垫资不超过{max}个字符")
    private String advanceOrNot;

    /**
     * 预估垫资金额
     */
    @ApiModelProperty(value = "预估垫资金额", position = 76)
    @Size(max = 22, message = "预估垫资金额不超过{max}个字符")
    private String estimatedAdvanceAmount;

    /**
     * 投标担保方式
     */
    @ApiModelProperty(value = "投标担保方式", position = 77)
    @Size(max = 128, message = "投标担保方式不超过{max}个字符")
    private String investmentGuaranteeMode;

    /**
     * 保修金
     */
    @ApiModelProperty(value = "保修金", position = 78)
    @Size(max = 32, message = "保修金不超过{max}个字符")
    private String warrantyMoney;

    /**
     * 保修金支付比例
     */
    @ApiModelProperty(value = "保修金支付比例", position = 79)
    @Size(max = 22, message = "保修金支付比例不超过{max}个字符")
    private String warrantyMoneyProportion;

    /**
     * 保修金支付方式
     */
    @ApiModelProperty(value = "保修金支付方式", position = 80)
    @Size(max = 128, message = "保修金支付方式不超过{max}个字符")
    private String warrantyMoneyMode;

    /**
     * 支付方式
     */
    @ApiModelProperty(value = "支付方式", position = 81)
    @Size(max = 128, message = "支付方式不超过{max}个字符")
    private String payTypeNew;

    /**
     * 现金支付方式
     */
    @ApiModelProperty(value = "现金支付方式", position = 82)
    @Size(max = 128, message = "现金支付方式不超过{max}个字符")
    private String paymentMode;

    /**
     * 履约担保方式
     */
    @ApiModelProperty(value = "履约担保方式", position = 83)
    @Size(max = 128, message = "履约担保方式不超过{max}个字符")
    private String performanceGuaranteeMode;

    /**
     * 评标办法
     */
    @ApiModelProperty(value = "评标办法", position = 84)
    @Size(max = 255, message = "评标办法不超过255个字符")
    private String bidEvaluationMethod;

    /**
     * 合同范本类型
     */
    @ApiModelProperty(value = "合同范本类型", position = 85)
    @Size(max = 128, message = "合同范本类型不超过{max}个字符")
    private String contractStyle;

    /**
     * 含税中标金额
     */
    @ApiModelProperty(value = "含税中标金额", position = 86)
    private BigDecimal taxIncludedMoney;

    /**
     * 不含税中标金额
     */
    @ApiModelProperty(value = "不含税中标金额", position = 87)
    private BigDecimal noTaxIncludedMoney;

    /**
     * 不含税自行施工金额
     */
    @ApiModelProperty(value = "不含税自行施工金额", position = 88)
    private BigDecimal noTaxIncludedConstructionMoney;

    /**
     * 中标项目经理
     */
    @ApiModelProperty(value = "中标项目经理", position = 89)
    @Size(max = 128, message = "中标项目经理不超过128个字符")
    private String winningProjectManager;

    /**
     * 中标项目经理注册证书编号
     */
    @ApiModelProperty(value = "中标项目经理注册证书编号", position = 90)
    @Size(max = 128, message = "中标项目经理不超过{max}个字符")
    private String winningProjectManagerNumber;

    /**
     * 执行项目经理
     */
    @ApiModelProperty(value = "执行项目经理", position = 91)
    @Size(max = 128, message = "执行项目经理不超过128个字符")
    private String executiveProjectManager;

    /**
     * 联系方式
     */
    @ApiModelProperty(value = "执行项目经理联系方式", position = 92)
    @Size(max = 128, message = "执行项目经理联系方式不超过{max}个字符")
    private String executiveProjectManagerNumber;

    /**
     * 突破底线条款
     */
    @ApiModelProperty(value = "突破底线条款", position = 93)
    private String breakBottom;

    /**
     * 中标通知书
     */
    @ApiModelProperty(value = "中标通知书", position = 94)
    private String bidWinningNoticeUrl;

    /**
     * 开标记录
     */
    @ApiModelProperty(value = "开标记录", position = 95)
    private String bidOpeningRecordUrl;

    /**
     * 源文件所属ID
     */
    @ApiModelProperty(value = "源文件所属ID", position = 96)
    @NotNull(message = "源文件所属id不能为空")
    private Long BelongId;

    /**
     * 关联工程/合同
     */
    @ApiModelProperty(value = "关联工程/合同", position = 97)
    @Size(max = 32, message = "关联工程/合同不超过32个字符")
    private String associatedProject;

    /**
     * 项目附件信息对象
     */
    @ApiModelProperty(value = "开标记录", position = 98)
    @Valid
    private List<BidOpenRecordReq> bidOpeningRecords;

    /**
     * 项目附件信息对象
     */
    @ApiModelProperty(value = "执行单位id", position = 99)
    @Size(max = 128, message = "执行单位id不超过128个字符")
    private String executeUnitId;

    /**
     * 项目附件信息对象
     */
    @ApiModelProperty(value = "执行单位Code", position = 100)
    @Size(max = 128, message = "执行单位Code不超过128个字符")
    private String executeUnitCode;

    /**
     * 执行单位在标准组织的idPath
     */
    @ApiModelProperty(value = "执行单位在标准组织的idPath", position = 101)
    @Size(max = 1024, message = "执行单位在标准组织的idPath不超过1024个字符")
    private String executeUnitIdPath;

    /**
     * 工程中标编号
     */
    @ApiModelProperty(value = "工程中标编号", position = 102)
    @Size(max = 64, message = "工程中标编号不超过{max}个字符")
    private String bidProjectCode;

    /**
     * 客户级别
     */
    @ApiModelProperty(value = "客户级别", position = 103)
    @Size(max = 50, message = "客户级别不超过50个字符")
    private String customerLevel;

    /**
     * 云枢组织名称
     */
    @ApiModelProperty(value = "云枢组织名称", position = 104)
    private String yunshuExecuteUnit;

    /**
     * 云枢组织ID
     */
    @ApiModelProperty(value = "云枢组织ID", position = 104)
    private String yunshuExecuteUnitId;

    /**
     * 云枢组织编码
     */
    @ApiModelProperty(value = "云枢组织编码", position = 104)
    private String yunshuExecuteUnitCode;

    /**
     * 云枢组织idPath
     */
    @ApiModelProperty(value = "云枢组织idPath", position = 104)
    private String yunshuExecuteUnitIdPath;

    /**
     * 是否创新业务
     */
    @ApiModelProperty(value = "是否创新业务")
    private String ifInnovativeBusiness;

    /**
     * 创新业务分类
     */
    @ApiModelProperty(value = "创新业务分类")
    private String innovativeBusinessType;

    /**
     * 创新业务分类2
     */
    @ApiModelProperty(value = "创新业务分类2")
    private String innovativeBusinessType2;


    /**
     * 创新业务分类3
     */
    @ApiModelProperty(value = "创新业务分类3")
    private String innovativeBusinessType3;

    @ApiModelProperty(value = "是否战新")
    private String ifStrategicNewBusiness;

    @ApiModelProperty(value = "战新业务一级分类")
    private String strategicNewBusinessType;

    @ApiModelProperty(value = "战新业务二级分类")
    private String strategicNewBusinessType2;

    @ApiModelProperty(value = "战新业务三级分类")
    private String strategicNewBusinessType3;

    /**
     * 局标准分类1
     */
    @ApiModelProperty(value = "局标准分类1")
    private String standardType1;

    /**
     * 局标准分类2
     */
    @ApiModelProperty(value = "局标准分类2")
    private String standardType2;

    /**
     * 局标准分类3
     */
    @ApiModelProperty(value = "局标准分类3")
    private String standardType3;

    /**
     * 局标准分类4
     */
    @ApiModelProperty(value = "局标准分类4")
    private String standardType4;

    /**
     * 工程类型Code（国家标准）
     */
    @ApiModelProperty(value = "工程类型Code（国家标准）")
    private String countryProjectTypeCode;
    /**
     * 工程类型code（总公司市场口径）
     */
    @ApiModelProperty(value = "工程类型code（总公司市场口径）")
    private String marketProjectTypeCode;
    /**
     * 工程类型code（总公司市场口径）2
     */
    @ApiModelProperty(value = "工程类型code（总公司市场口径）2")
    private String marketProjectType2Code;
    /**
     * 工程类型code(总公司综合口径)
     */
    @ApiModelProperty(value = "工程类型code(总公司综合口径)")
    private String projectTypeCode;
    /**
     * 工程类型code(总公司综合口径)2
     */
    @ApiModelProperty(value = "工程类型code(总公司综合口径)2")
    private String projectType2Code;
    /**
     * 工程类型code(总公司综合口径)3
     */
    @ApiModelProperty(value = "工程类型code(总公司综合口径)3")
    private String projectType3Code;
    /**
     * 工程类型code(总公司综合口径)4
     */
    @ApiModelProperty(value = "工程类型code(总公司综合口径)4")
    private String projectType4Code;
    /**
     * 工程承包模式Code
     */
    @ApiModelProperty(value = "工程承包模式Code")
    private String contractMode1Code;

    /**
     * 工程承包模式Code
     */
    @ApiModelProperty(value = "施工承包模式Code")
    private String contractMode2Code;

    /**
     * 投资主体Code
     */
    @ApiModelProperty(value = "投资主体Code")
    private String investorsCode;
    /**
     * 业务类型Code
     */
    @ApiModelProperty(value = "业务类型Code")
    private String businessTypeCode;

    /**
     * 客户级别Code
     */
    @ApiModelProperty(value = "客户级别Code")
    private String customerLevelCode;
    /**
     * 客户企业性质Code
     */
    @ApiModelProperty(value = "客户企业性质Code")
    private String enterpriseTypeCode;
    /**
     * 进度款付款方式Code
     */
    @ApiModelProperty(value = "进度款付款方式Code")
    private String paymentTypeCode;

    /**
     * 保修金支付方式Code
     */
    @ApiModelProperty(value = "保修金支付方式Code")
    private String qualityAwardTypeCode;

    /**
     * 局标准分类Code1
     */
    @ApiModelProperty(value = "局标准分类Code1")
    private String standardType1Code;

    /**
     * 局标准分类Code2
     */
    @ApiModelProperty(value = "局标准分类Code2")
    private String standardType2Code;

    /**
     * 局标准分类Code3
     */
    @ApiModelProperty(value = "局标准分类Code3")
    private String standardType3Code;

    /**
     * 局标准分类Code4
     */
    @ApiModelProperty(value = "局标准分类Code4")
    private String standardType4Code;

    /**
     * 创新业务分类Code
     */
    @ApiModelProperty(value = "创新业务分类Code")
    private String innovativeBusinessTypeCode;
    /**
     * 创新业务分类2Code
     */
    @ApiModelProperty(value = "创新业务分类2Code")
    private String innovativeBusinessType2Code;

    /**
     * "创新业务分类3Code"
     */
    @ApiModelProperty(value = "创新业务分类3Code")
    private String innovativeBusinessType3Code;

    @ApiModelProperty(value = "战新业务一级分类code")
    private String strategicNewBusinessTypeCode;

    @ApiModelProperty(value = "战新业务二级分类code")
    private String strategicNewBusinessType2Code;

    @ApiModelProperty(value = "战新业务三级分类code")
    private String strategicNewBusinessType3Code;

    /**
     * 客户id
     */
    @ApiModelProperty(value = "客户ID ")
    private String customerId;

    /**
     * 客户编号
     */
    @ApiModelProperty(value = "客户编号")
    private String customerCode;

    /**
     * 客户母公司id
     */
    @ApiModelProperty(value = "客户母公司id")
    private String superiorCompanyId;

    /**
     * 统一社会信用代码
     */
    @ApiModelProperty(value = "统一社会信用代码")
    private String businessLicenseCode;
}
