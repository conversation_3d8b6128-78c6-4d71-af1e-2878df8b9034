
package com.cscec3b.iti.projectmanagement.api.dto.request.open.v2;

import java.io.Serializable;

import com.cscec3b.iti.projectmanagement.api.dto.request.BasePage;
import com.fasterxml.jackson.annotation.JsonProperty;

import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;
import lombok.experimental.Accessors;

/**
 * @description 项目对外请求数据
 * <AUTHOR>
 * @date 2022/11/03
 */
@Data
@Accessors(chain = true)
@ApiModel(value = "ProjectOpenHookQueryReq_v2.0", description = "项目挂接查询对外请求数据")
public class ProjectOpenHookQueryReq extends BasePage implements Serializable {

    private static final long serialVersionUID = 1L;

    @ApiModelProperty(value = "云枢组织id")
    @JsonProperty(value = "projectOrgId")
    private String yunshuOrgId;

    @ApiModelProperty(value = "财商项目编码")
    @JsonProperty(value = "projectCode")
    private String projectFinanceCode;

    @ApiModelProperty(value = "财商项目名称")
    @JsonProperty(value = "projectName")
    private String projectFinanceName;

    @ApiModelProperty(value = "执行单位id")
    @JsonProperty(value = "implementationUnitId")
    private String executeUnitId;

}
