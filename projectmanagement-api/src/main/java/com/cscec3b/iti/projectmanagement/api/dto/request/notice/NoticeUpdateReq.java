package com.cscec3b.iti.projectmanagement.api.dto.request.notice;

import com.cscec3b.iti.projectmanagement.api.dto.dto.AttachmentDto;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;
import lombok.experimental.Accessors;

import javax.validation.Valid;
import javax.validation.constraints.NotNull;
import javax.validation.constraints.Size;
import java.io.Serializable;
import java.util.List;

/**
 * @Description NoticeReq
 * <AUTHOR>
 * @Date 2023/1/3 10:55
 */
@Data
@Accessors(chain = true)
@ApiModel(value="NoticeReq", description="通知公告请求对象")
public class NoticeUpdateReq implements Serializable {
    private static final long serialVersionUID = 1L;

    /**
     * 公告id
     */
    @ApiModelProperty(value = "通知公告id")
    @NotNull(message = "通知公告id不能为空")
    private Long id;

    /**
     * 公告标题
     */
    @ApiModelProperty(value = "公告标题")
    @NotNull(message = "公告标题不能为空")
    @Size(min = 1, max = 50, message = "公告标题长度要求 [{min},{max}]")
    private String noticeTitle;

    /**
     * 发布单位
     */
    @ApiModelProperty(value = "发布单位")
    @NotNull(message = "发布单位不能为空")
    @Size(min = 1, max = 20, message = "发布单位长度要求 [{min},{max}]")
    private String publishOrganization;

    /**
     * 公告类型
     */
    @ApiModelProperty(value = "公告类型（1：系统公告，2：业务公告）")
    @NotNull(message = "公告类型不能为空")
    private Integer noticeType;

    /**
     * 公告内容
     */
    @ApiModelProperty(value = "公告内容")
    private String noticeContent;

    /**
     * 发布状态
     */
    @ApiModelProperty(value = "发布状态（0：未发布，1：已发布）")
    @NotNull(message = "发布状态不能为空")
    private Integer publishStatus;

    /**
     * 发布时间
     */
    @ApiModelProperty(value = "发布时间")
    private Long publishTime;

    /**
     * 阅读状态
     */
    @ApiModelProperty(value = "阅读状态（0：未读，1：已读）")
    private Integer readStatus;

    /**
     * 阅读量
     */
    @ApiModelProperty(value = "阅读量")
    private Long readingQuantity ;

    /**
     * 公告附件信息
     */
    @ApiModelProperty(value = "公告附件信息")
    @Valid
    private List<AttachmentDto> attachments;
}
