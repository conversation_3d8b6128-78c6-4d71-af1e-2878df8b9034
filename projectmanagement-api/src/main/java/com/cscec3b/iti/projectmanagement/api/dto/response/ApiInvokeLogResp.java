package com.cscec3b.iti.projectmanagement.api.dto.response;

import lombok.Data;
import lombok.experimental.Accessors;

/**
 * <AUTHOR>
 * @description ApiInvokeLogResp
 * @date 2023/07/21 09:40
 */
@Data
@Accessors(chain = true)
public class ApiInvokeLogResp {

    private static final long serialVersionUID = 1L;
    private Long id;

    private String requestUrl;

    private String name;

    private String requestMethod;

    private String requestParam;

    private String responseJson;

    private Integer responseStatus;

    private String errorMsg;

    private Integer operatorType;

    private Long requestTime;

    private Long createAt;
}
