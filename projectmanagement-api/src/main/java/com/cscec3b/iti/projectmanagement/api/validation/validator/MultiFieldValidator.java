package com.cscec3b.iti.projectmanagement.api.validation.validator;

import com.cscec3b.iti.projectmanagement.api.validation.annotation.MultiFieldCheck;
import net.sf.cglib.beans.BeanMap;
import org.apache.commons.lang3.StringUtils;
import org.springframework.expression.ExpressionParser;
import org.springframework.expression.spel.standard.SpelExpressionParser;
import org.springframework.expression.spel.support.SimpleEvaluationContext;

import javax.validation.ConstraintValidator;
import javax.validation.ConstraintValidatorContext;
import java.util.HashMap;
import java.util.Map;

public class MultiFieldValidator implements ConstraintValidator<MultiFieldCheck, Object> {

    private static final String EL_PREFIX = "#{";
    private static final String EL_SUFFIX = "}";

    private String conditional;

    private String checkRule;

    @Override
    public void initialize(MultiFieldCheck constraintAnnotation) {
        this.conditional = constraintAnnotation.conditional();
        this.checkRule = constraintAnnotation.checkRule();
    }

    /**
     * 校验
     * condition 为true时 才会 进入校验规则 checkRule;
     *
     * @param value   value
     * @param context context
     * @return boolean
     */
    @Override
    public boolean isValid(Object value, ConstraintValidatorContext context) {
        Map<String, Object> objectMap = new HashMap<String, Object>();
        if (value != null) {
            BeanMap beanMap = BeanMap.create(value);
            beanMap.keySet().forEach(key -> objectMap.put(key + "", beanMap.get(key)));
        }
        // 表达式
        if (Boolean.parseBoolean(parseSpEl(conditional, objectMap))) {
            if (!Boolean.parseBoolean(parseSpEl(checkRule, objectMap))) {
                String msg = context.getDefaultConstraintMessageTemplate();
                context.buildConstraintViolationWithTemplate(msg).addConstraintViolation();
                return false;
            }
        }
        return true;
    }

    public static String parseSpEl(String spElStr, Map<String, Object> objectMap) {
        if (StringUtils.isBlank(spElStr)) {
            return spElStr;
        }
        // StandardEvaluationContext 功能较强大 但是会有安全漏洞 会执行方法 底层使用的反射
//        String format = String.format("%s%s%s", EL_PREFIX, spElStr, EL_SUFFIX);
//        SpelExpressionParser parser = new SpelExpressionParser();
//        StandardEvaluationContext context = new StandardEvaluationContext();
//        context.setVariables(objectMap);
//        context.addPropertyAccessor(new MapAccessor());
//        context.addPropertyAccessor(new BeanFactoryAccessor());
//        return parser.parseExpression(format, new TemplateParserContext()).getValue(context, String.class);
        ExpressionParser parser = new SpelExpressionParser();
        SimpleEvaluationContext context = SimpleEvaluationContext.forReadOnlyDataBinding().build();
        objectMap.forEach(context::setVariable);
        return parser.parseExpression(spElStr).getValue(context, String.class);

    }

}
