package com.cscec3b.iti.projectmanagement.api.dto.response.project;

import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;
import lombok.experimental.Accessors;

import java.io.Serializable;
import java.math.BigDecimal;
import java.util.List;


@Data
@Accessors(chain = true)
@ApiModel(value = "BidSummaryResp", description = "投标总结详细信息响应对象")
public class BidSummaryResp implements Serializable {
    private static final long serialVersionUID = 1L;

    private Long id;

    /**
     * 复核审批用户id
     */
    @ApiModelProperty(value = "复核审批用户id", position = 1)
    private String approvalPerson;

    /**
     * 发起人单位
     */
    @ApiModelProperty(value = "发起人", position = 2)
    private String submitPerson;

    /**
     * 是否创建指挥部
     */
    @ApiModelProperty(value = "是否创建指挥部", position = 3)
    private String isCreateHead;

    /**
     * 执行单位
     */
    @ApiModelProperty(value = "执行单位", position = 4)
    private String executeUnit;

    /**
     * 独立性
     */
    @ApiModelProperty(value = "独立性", position = 5)
    private String isIndependent;

    /**
     * 关联工程/合同
     */
    @ApiModelProperty(value = "关联工程/合同", position = 6)
    private String associatedProject;

    /**
     * 工程编号
     */
    @ApiModelProperty(value = "工程编号", position = 7)
    private String projectCode;

    /**
     * 工程名称
     */
    @ApiModelProperty(value = "工程名称", position = 8)
    private String projectName;

    /**
     * 工程简称
     */
    @ApiModelProperty(value = "工程简称", position = 9)
    private String projectAbbreviation;

    /**
     * 工程属地
     */
    @ApiModelProperty(value = "工程属地", position = 10)
    private String projectBelong;

    /**
     * 所属办事处
     */
    @ApiModelProperty(value = "所属办事处", position = 11)
    private String signFormOffice;

    /**
     * 省
     */
    @ApiModelProperty(value = "省", position = 12)
    private String province;

    /**
     * 市
     */
    @ApiModelProperty(value = "市", position = 13)
    private String city;

    /**
     * 区
     */
    @ApiModelProperty(value = "区", position = 14)
    private String region;

    /**
     * 具体地址
     */
    @ApiModelProperty(value = "具体地址", position = 15)
    private String address;

    /**
     * 国别
     */
    @ApiModelProperty(value = "国别", position = 16)
    private String country;

    /**
     * 工程类型（国家标准）
     */
    @ApiModelProperty(value = "工程类型（国家标准）", position = 17)
    private String countryProjectType;

    /**
     * 工程类型（总公司市场口径）
     */
    @ApiModelProperty(value = "工程类型（总公司市场口径）", position = 18)
    private String marketProjectType;

    /**
     * 工程类型(总公司市场口径)2
     */
    @ApiModelProperty(value = "工程类型(总公司市场口径)2", position = 19)
    private String marketProjectType2;

    /**
     * 工程类型(总公司综合口径)
     */
    @ApiModelProperty(value = "工程类型(总公司综合口径)", position = 20)
    private String projectType;

    /**
     * 工程类型(总公司综合口径)2
     */
    @ApiModelProperty(value = "工程类型(总公司综合口径)2", position = 21)
    private String projectType2;

    /**
     * 工程类型(总公司综合口径)3
     */
    @ApiModelProperty(value = "工程类型(总公司综合口径)3", position = 22)
    private String projectType3;

    /**
     * 工程类型(总公司综合口径)4
     */
    @ApiModelProperty(value = "工程类型(总公司综合口径)4", position = 23)
    private String projectType4;

    /**
     * 总分包类别
     */
    @ApiModelProperty(value = "总分包类别", position = 24)
    private String totalSubcontractingCategory;

    /**
     * 是否投资项目
     */
    @ApiModelProperty(value = "是否投资项目", position = 25)
    private String investmentProjects;

    /**
     * 投资主体
     */
    @ApiModelProperty(value = "投资主体", position = 26)
    private String investors;

    /**
     * 结构形式
     */
    @ApiModelProperty(value = "结构形式", position = 27)
    private String structuralStyle;

    /**
     * 结构形式2
     */
    @ApiModelProperty(value = "结构形式2", position = 28)
    private String structuralStyle2;

    /**
     * 是否有钢结构
     */
    @ApiModelProperty(value = "是否有钢结构", position = 29)
    private String includingSteel;

    /**
     * 钢结构体量
     */
    @ApiModelProperty(value = "钢结构体量", position = 30)
    private String volume;

    /**
     * 是否装配式
     */
    @ApiModelProperty(value = "是否装配式", position = 31)
    private String fabricated;

    /**
     * 预计造价
     */
    @ApiModelProperty(value = "预计造价", position = 32)
    private BigDecimal estimatedCost;

    /**
     * 业务类型
     */
    @ApiModelProperty(value = "业务类型", position = 33)
    private String businessType;

    /**
     * 客户名称
     */
    @ApiModelProperty(value = "客户名称", position = 34)
    private String customerName;

    /**
     * 上级相关方
     */
    @ApiModelProperty(value = "上级相关方", position = 35)
    private String superiorCompanyName;

    /**
     * 客户企业性质
     */
    @ApiModelProperty(value = "客户企业性质", position = 36)
    private String enterpriseType;

    /**
     * 建设单位（甲方）联系人
     */
    @ApiModelProperty(value = "建设单位（甲方）联系人", position = 37)
    private String contactPerson;

    /**
     * 建设单位（甲方）联系人电话
     */
    @ApiModelProperty(value = "建设单位（甲方）联系人电话", position = 38)
    private String contactPersonMobile;

    /**
     * 设计单位
     */
    @ApiModelProperty(value = "设计单位", position = 39)
    private String designer;

    /**
     * 监理单位
     */
    @ApiModelProperty(value = "监理单位", position = 40)
    private String supervisor;

    /**
     * 招标代理机构
     */
    @ApiModelProperty(value = "招标代理机构", position = 41)
    private String biddingAgency;

    /**
     * 开标时间
     */
    @ApiModelProperty(value = "开标时间", position = 42)
    private Long bidOpeningTime;

    /**
     * 定标时间
     */
    @ApiModelProperty(value = "定标时间", position = 43)
    private Long calibrationTime;

    /**
     * 参与开标人员
     */
    @ApiModelProperty(value = "参与开标人员", position = 44)
    private String bidOpeningPersonnel;

    /**
     * 是否中标
     */
    @ApiModelProperty(value = "是否中标", position = 45)
    private String inBidType;

    /**
     * 中标主体
     */
    @ApiModelProperty(value = "中标主体", position = 46)
    private String inBidValue;

    /**
     * 是否纳入统计
     */
    @ApiModelProperty(value = "是否纳入统计,Y:是,N:否", position = 47)
    private String isStatistic;

    /**
     * 承包模式
     */
    @ApiModelProperty(value = "承包模式", position = 48)
    private String contractMode1;

    /**
     * 承包模式2
     */
    @ApiModelProperty(value = "承包模式2", position = 49)
    private String contractMode2;

    /**
     * 招标模式
     */
    @ApiModelProperty(value = "招标模式", position = 50)
    private String biddingType;

    /**
     * 招标范围
     */
    @ApiModelProperty(value = "招标范围", position = 51)
    private String biddingRange;

    /**
     * 发包人指定分包、独立分包的工程
     */
    @ApiModelProperty(value = "发包人指定分包、独立分包的工程", position = 52)
    private String subpackageEngineering;

    /**
     * 交标时间
     */
    @ApiModelProperty(value = "交标时间", position = 53)
    private Long crossBiddingTime;

    /**
     * 开工时间
     */
    @ApiModelProperty(value = "开工时间", position = 54)
    private Long startTime;

    /**
     * 竣工时间
     */
    @ApiModelProperty(value = "竣工时间", position = 55)
    private Long beCompletedTime;

    /**
     * 总工期（天）
     */
    @ApiModelProperty(value = "总工期（天）", position = 56)
    private String totalDuration;

    /**
     * 工期奖罚类型
     */
    @ApiModelProperty(value = "工期奖罚类型", position = 57)
    private String durationAwardType;

    /**
     * 工期奖罚条款
     */
    @ApiModelProperty(value = "工期奖罚条款", position = 58)
    private String durationAwardClause;

    /**
     * 质量要求
     */
    @ApiModelProperty(value = "质量要求", position = 59)
    private String qualityRequirement;

    /**
     * 质量奖罚类型
     */
    @ApiModelProperty(value = "质量奖罚类型", position = 60)
    private String qualityAwardType;

    /**
     * 质量奖罚条款
     */
    @ApiModelProperty(value = "质量奖罚条款", position = 61)
    private String qualityAwardClause;

    /**
     * 安全文明施工要求
     */
    @ApiModelProperty(value = "安全文明施工要求", position = 62)
    private String safeConstruction;

    /**
     * 安全文明施工要求奖罚
     */
    @ApiModelProperty(value = "安全文明施工要求奖罚", position = 63)
    private String safeConstructionAward;

    /**
     * 计价方式
     */
    @ApiModelProperty(value = "计价方式", position = 64)
    private String pricingMethod;

    /**
     * 合同形式
     */
    @ApiModelProperty(value = "合同形式", position = 65)
    private String contractForm;

    /**
     * 人工费可调
     */
    @ApiModelProperty(value = "人工费可调", position = 66)
    private String laborCostType;

    /**
     * 主材费可调
     */
    @ApiModelProperty(value = "主材费可调", position = 67)
    private String materialScienceType;

    /**
     * 是否有预付款
     */
    @ApiModelProperty(value = "是否有预付款", position = 68)
    private String advanceChargeType;

    /**
     * 进度款付款方式
     */
    @ApiModelProperty(value = "进度款付款方式", position = 69)
    private String paymentType;

    /**
     * 竣工验收支付比例
     */
    @ApiModelProperty(value = "竣工验收支付比例", position = 70)
    private String beCompletedProportion;

    /**
     * 竣工验收收款周期（月）
     */
    @ApiModelProperty(value = "竣工验收收款周期（月）", position = 71)
    private String beCompletedCycle;

    /**
     * 结算支付比例
     */
    @ApiModelProperty(value = "结算支付比例", position = 72)
    private String settlementPaymentProportion;

    /**
     * 结算周期（月）
     */
    @ApiModelProperty(value = "结算周期（月）", position = 73)
    private String settlementCycle;

    /**
     * 是否垫资
     */
    @ApiModelProperty(value = "是否垫资", position = 74)
    private String advanceOrNot;

    /**
     * 投标担保方式
     */
    @ApiModelProperty(value = "投标担保方式", position = 75)
    private String investmentGuaranteeMode;

    /**
     * 保修金
     */
    @ApiModelProperty(value = "保修金", position = 76)
    private String warrantyMoney;

    /**
     * 保修金支付比例
     */
    @ApiModelProperty(value = "保修金支付比例", position = 77)
    private String warrantyMoneyProportion;

    /**
     * 保修金支付方式
     */
    @ApiModelProperty(value = "保修金支付方式", position = 78)
    private String warrantyMoneyMode;

    /**
     * 支付方式
     */
    @ApiModelProperty(value = "支付方式", position = 79)
    private String payTypeNew;

    /**
     * 现金支付方式
     */
    @ApiModelProperty(value = "现金支付方式", position = 80)
    private String paymentMode;

    /**
     * 履约担保方式
     */
    @ApiModelProperty(value = "履约担保方式", position = 81)
    private String performanceGuaranteeMode;

    /**
     * 评标办法
     */
    @ApiModelProperty(value = "评标办法", position = 82)
    private String bidEvaluationMethod;

    /**
     * 合同范本类型
     */
    @ApiModelProperty(value = "合同范本类型", position = 83)
    private String contractStyle;

    /**
     * 含税中标金额
     */
    @ApiModelProperty(value = "含税中标金额", position = 84)
    private BigDecimal taxIncludedMoney;

    /**
     * 不含税中标金额
     */
    @ApiModelProperty(value = "不含税中标金额", position = 85)
    private BigDecimal noTaxIncludedMoney;

    /**
     * 不含税自行施工金额
     */
    @ApiModelProperty(value = "不含税自行施工金额", position = 86)
    private BigDecimal noTaxIncludedConstructionMoney;

    /**
     * 中标项目经理
     */
    @ApiModelProperty(value = "中标项目经理", position = 87)
    private String winningProjectManager;

    /**
     * 中标项目经理注册证书编号
     */
    @ApiModelProperty(value = "中标项目经理注册证书编号", position = 88)
    private String winningProjectManagerNumber;

    /**
     * 执行项目经理
     */
    @ApiModelProperty(value = "执行项目经理", position = 89)
    private String executiveProjectManager;

    /**
     * 联系方式
     */
    @ApiModelProperty(value = "联系方式", position = 90)
    private String executiveProjectManagerNumber;

    /**
     * 中标通知书
     */
    @ApiModelProperty(value = "中标通知书", position = 91)
    private String bidWinningNoticeUrl;

    /**
     * 开标记录
     */
    @ApiModelProperty(value = "开标记录", position = 92)
    private String bidOpeningRecordUrl;

    /**
     * 独立合同ID
     */
    @ApiModelProperty(value = "独立合同ID", position = 93)
    private Long independentContractId;

    /**
     * 独立合同类型：1投标总结；2补充协议；3局内分包合同
     */
    @ApiModelProperty(value = "独立合同类型：1投标总结；2补充协议；3局内分包合同", position = 94)
    private Integer independentContractType;

    /**
     * 项目附件信息对象
     */
    @ApiModelProperty(value = "项目附件信息对象", position = 95)
    private String projectAttachment;

    /**
     * 突破底线条款
     */
    @ApiModelProperty(value = "突破底线条款", position = 96)
    private String breakBottom;

    /**
     * 预估垫资金额
     */
    @ApiModelProperty(value = "预估垫资金额", position = 97)
    private String estimatedAdvanceAmount;

    /**
     * 独立文件id
     */
    @ApiModelProperty(value = "独立文件id", position = 98)
    private Long originFileId;

    /**
     * 接口幂等性校验字段
     */
    @ApiModelProperty(value = "所属源文件ID，接口幂等性校验字段", position = 99)
    private Long belongId;

    /**
     * 开标记录
     */
    @ApiModelProperty(value = "开标记录", position = 100)
    private List<BidOpenRecordResp> bidOpeningRecords;

    /**
     * 执行单位id
     */
    @ApiModelProperty(value = "执行单位id", position = 101)
    private String executeUnitId;

    /**
     * 执行单位Code
     */
    @ApiModelProperty(value = "执行单位Code", position = 102)
    private String executeUnitCode;

    /**
     * 执行单位在标准组织的idPath
     */
    @ApiModelProperty(value = "执行单位在标准组织的idPath", position = 103)
    private String executeUnitIdPath;

    /**
     * 工程中标编号
     */
    @ApiModelProperty(value = "工程中标编号", position = 104)
    private String bidProjectCode;

    /**
     * 客户级别
     */
    @ApiModelProperty(value = "客户级别", position = 105)
    private String customerLevel;

    /**
     * 工程类型（国家标准)code
     */
    @ApiModelProperty(value = "工程类型（国家标准）code")
    private String countryProjectTypeCode;

    /**
     * 工程类型（总公司市场口径）
     */
    @ApiModelProperty(value = "工程类型（总公司市场口径）code")
    private String marketProjectTypeCode;

    /**
     * 工程类型(总公司市场口径）2code
     */
    @ApiModelProperty(value = "工程类型（总公司市场口径）2code")
    private String marketProjectType2Code;
    /**
     * 工程类型(总公司综合口径) code
     */
    @ApiModelProperty(value = "工程类型(总公司综合口径)code")
    private String projectTypeCode;
    /**
     * 工程类型(总公司综合口径) 2 code
     */
    @ApiModelProperty(value = "工程类型(总公司综合口径)2code")
    private String projectType2Code;

    /**
     * 工程类型(总公司综合口径)3 code
     */
    @ApiModelProperty(value = "工程类型(总公司综合口径)3 code")
    private String projectType3Code;
    /**
     * 工程类型(总公司综合口径) 4 code
     */
    @ApiModelProperty(value = "工程类型(总公司综合口径)4 code")
    private String projectType4Code;

    /**
     * 承包模式1 code
     */
    @ApiModelProperty(value = "承包模式1 code")
    private String contractMode1Code;
    /**
     * 承包模式2 code
     */
    @ApiModelProperty(value = "承包模式2 code")
    private String contractMode2Code;

    /**
     * 投资主体code
     */
    @ApiModelProperty(value = "投资主体code")
    private String investorsCode;
    /**
     * 业务类型 code
     */
    @ApiModelProperty(value = "业务类型code")
    private String businessTypeCode;
    /**
     * 客户级别code
     */
    @ApiModelProperty(value = "客户级别")
    private String customerLevelCode;
    /**
     * 客户企业性质 code
     */
    @ApiModelProperty(value = "客户企业性质code")
    private String enterpriseTypeCode;
    /**
     * 进度款付款方式 code
     */
    @ApiModelProperty(value = "进度款付款方式")
    private String paymentTypeCode;
    /**
     * 质量奖罚类型code
     */
    @ApiModelProperty(value = "质量奖罚类型code")
    private String qualityAwardTypeCode;


    /**
     * 是否创新业务
     */
    @ApiModelProperty(value = "是否创新业务")
    private String ifInnovativeBusiness;

    /**
     * 创新业务分类
     */
    @ApiModelProperty(value = "创新业务分类")
    private String innovativeBusinessType;

    /**
     * 创新业务分类2
     */
    @ApiModelProperty(value = "创新业务分类2")
    private String innovativeBusinessType2;
    /**
     * 创新业务分类3
     */
    @ApiModelProperty(value = "创新业务分类3")
    private String innovativeBusinessType3;
    /**
     * 创新业务分类1code
     */
    @ApiModelProperty(value = "创新业务分类1code")
    private String innovativeBusinessTypeCode;
    /**
     * 创新业务分类2code
     */
    @ApiModelProperty(value = "创新业务分类2code")
    private String innovativeBusinessType2Code;
    /**
     * 创新业务分类3code
     */
    @ApiModelProperty(value = "创新业务分类3code")
    private String innovativeBusinessType3Code;

    @ApiModelProperty(value = "是否战新")
    private String ifStrategicNewBusiness;

    @ApiModelProperty(value = "战新业务一级分类")
    private String strategicNewBusinessType;

    @ApiModelProperty(value = "战新业务二级分类")
    private String strategicNewBusinessType2;

    @ApiModelProperty(value = "战新业务三级分类")
    private String strategicNewBusinessType3;

    @ApiModelProperty(value = "战新业务一级分类code")
    private String strategicNewBusinessTypeCode;

    @ApiModelProperty(value = "战新业务二级分类code")
    private String strategicNewBusinessType2Code;

    @ApiModelProperty(value = "战新业务三级分类code")
    private String strategicNewBusinessType3Code;

    /**
     * 局标准分类
     */
    @ApiModelProperty(value = "局标准分类")
    private String standardType1;

    /**
     * 局标准分类2
     */
    @ApiModelProperty(value = "局标准分类2")
    private String standardType2;

    /**
     * 局标准分类3
     */
    @ApiModelProperty(value = "局标准分类3")
    private String standardType3;

    /**
     * 局标准分类4
     */
    @ApiModelProperty(value = "局标准分类4")
    private String standardType4;

    /**
     * 局标准分类1code
     */
    @ApiModelProperty(value = "局标准分类1code")
    private String standardType1Code;
    /**
     * 局标准分类2code
     */
    @ApiModelProperty(value = "局标准分类2code")
    private String standardType2Code;
    /**
     * 局标准分类3code
     */
    @ApiModelProperty(value = "局标准分类3code")
    private String standardType3Code;
    /**
     * 局标准分类4code
     */
    @ApiModelProperty(value = "局标准分类4code")
    private String standardType4Code;
    /**
     * 客户id
     */
    @ApiModelProperty(value = "客户id")
    private String customerId;

    /**
     * 云枢执行单位名称
     */
    @ApiModelProperty(value = "云枢执行单位名称")
    private String yunshuExecuteUnit;

    /**
     * 云枢执行单位code
     */
    @ApiModelProperty(value = "云枢执行单位code")
    private String yunshuExecuteUnitCode;

    /**
     * 云枢执行单位Id
     */
    @ApiModelProperty(value = "云枢执行单位Id")
    private String yunshuExecuteUnitId;

    /**
     * 云枢执行单位id_path
     */
    @ApiModelProperty(value = "云枢执行单位id_path")
    private String yunshuExecuteUnitIdPath;

    /**
     * 创建时间
     */
    @ApiModelProperty(value = "创建时间")
    private Long createAt;

    /**
     * 更新时间
     */
    @ApiModelProperty(value = "更新时间")
    private Long updateAt;

    /**
     * 统一社会信用代码
     */
    @ApiModelProperty(value = "统一社会信用代码")
    private String businessLicenseCode;
}
