package com.cscec3b.iti.model.resp;

import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

import java.io.Serializable;
import java.util.List;


/**
 * 工程项目信息版块
 *
 * <AUTHOR>
 * @date 2025/06/05
 */
@Data
@ApiModel(value = "EngineProjectSegment", description = "项目信息分发-商务数据部分")
public class EngineProjectSegment implements Serializable {

    private static final long serialVersionUID = 1L;

    /**
     * 工程项目标识（项目中心）
     */
    @ApiModelProperty(value = "工程项目标识（项目中心）")
    private String engineeringKey;

    /**
     * 工程项目编码 （股份）
     */
    @ApiModelProperty(value = "工程项目编码 （股份）")
    private String engineeringCode;

    /**
     * 工程项目名称
     */
    @ApiModelProperty(value = "工程项目名称")
    private String engineeringName;

    /**
     * 绑定标准项目列表
     */
    @ApiModelProperty(value = "绑定标准项目列表")
    private List<StandardProjectInfoResp> bindingStandardProjectList;

    /**
     * 变更前的工程项目信息
     */
    private EngineProjectSegment preEngineProjectInfo;
}
