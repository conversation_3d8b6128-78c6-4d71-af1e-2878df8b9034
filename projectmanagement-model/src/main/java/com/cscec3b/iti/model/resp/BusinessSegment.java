package com.cscec3b.iti.model.resp;

import java.io.Serializable;

import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

/**
 * <AUTHOR>
 * @description BusinessSegment
 * @date 2023/09/16 14:45
 */

@Data
@ApiModel(value = "BusinessSegment", description = "项目信息分发-商务数据部分")
public class BusinessSegment implements Serializable {
    /**
     * 施工项目状态(商务): 05:未结; 06:已结
     */
    @ApiModelProperty(value = "施工项目状态(商务): 05:未结; 06:已结")
    private String projectStatusBiz;
}
