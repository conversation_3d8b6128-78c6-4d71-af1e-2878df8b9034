package com.cscec3b.iti.model.req;

import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

import javax.validation.constraints.NotBlank;
import javax.validation.constraints.NotEmpty;
import java.io.Serializable;
import java.util.Map;

@Data
@ApiModel(value = "项目同步请求数据", description = "其他系统同步项目信息到项目中心的请求参数")
public class OpenProjectSyncReq implements Serializable {

    private static final long serialVersionUID = 2405172041950251807L;

    @ApiModelProperty(value = "项目标识")
    @NotBlank(message = "项目标识不能为空")
    private String cpmProjectKey;

    @ApiModelProperty(value = "项目云枢ID")
    private String yunshuOrgId;

    @ApiModelProperty(value = "板块信息: 'bms_segment': 商务板块, 'finance_segment': 财商板块")
    @NotBlank(message = "板块信息不能为空")
    private String segment;

    @ApiModelProperty(value = "业务板块信息")
    @NotEmpty(message = "业务板块信息不能为空")
    private Map<String, Object> data;

}
