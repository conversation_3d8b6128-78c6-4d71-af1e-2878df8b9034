
package com.cscec3b.iti.model.req;

import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;
import lombok.experimental.Accessors;

import java.io.Serializable;

/**
 * @description 项目对外请求数据
 * <AUTHOR>
 * @date 2022/11/03
 */
@Data
@Accessors(chain = true)
@ApiModel(value = "ProjectOpenReq", description = "项目对外请求数据")
public class ProjectOpenReq implements Serializable {

    private static final long serialVersionUID = 1L;

    /**
     * A8项目编码，
     *  @deprecated 已废弃，使用cpmProjectKey,或yunshuOrgId
     */
    @Deprecated
    @ApiModelProperty(value = "A8项目编码")
    private String a8ProjectCode;

    /**
     * 云枢组织id
     */
    @ApiModelProperty(value = "云枢组织id")
    private String yunshuOrgId;

    /**
     * 财商项目编码
     */
    @ApiModelProperty(value = "财商项目编码")
    private String projectFinanceCode;

//    @ApiModelProperty(value = "标准组织编码")
//    private String projectDeptId;

    /**
     * 组织路径
     * @deprecated 已废弃，使用cpmProjectKey,或yunshuOrgId
     */
    @Deprecated
    @ApiModelProperty(value = "组织路径")
    private String projectDeptIdPath;

    /**
     * 项目标识
     */
    @ApiModelProperty(value = "项目标识", notes = "如果cpmProjectKey不为空, 则只取cpmProjectKey作为查询条件，否则其他参数至少要有一个不能为空")
    private String cpmProjectKey;

}
