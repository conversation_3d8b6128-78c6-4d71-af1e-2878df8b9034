ALTER TABLE sys_dict_data
    ADD CONSTRAINT un_type_label_value UNIQUE KEY (dict_type, dict_label, dict_value, deleted);
CREATE UNIQUE INDEX sys_dict_data_dict_type_IDX USING BTREE ON project_management.sys_dict_data (dict_type,
                                                                                                 dict_label,
                                                                                                 dict_value, deleted);


ALTER TABLE sys_dict_type
    DROP KEY dict_type;
ALTER TABLE sys_dict_type
    ADD CONSTRAINT sys_dict_type_unique UNIQUE KEY (dict_type, deleted);

ALTER TABLE project
    ADD signed_subject_code varchar(128) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci NULL COMMENT '签约主体code';
ALTER TABLE contract
    ADD signed_subject_code varchar(128) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci NULL COMMENT '签约主体code';
ALTER TABLE supplementary_agreement
    ADD signed_subject_code varchar(128) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci NULL COMMENT '签约主体code';
ALTER TABLE bureau_contract
    ADD signed_subject_code varchar(128) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci NULL COMMENT '签约主体code';
ALTER TABLE civil_military_integration
    ADD signed_subject_code varchar(128) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci NULL COMMENT '签约主体code';
ALTER TABLE secrecy_project_info
    ADD signed_subject_code varchar(128) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci NULL COMMENT '签约主体code';



update project, sys_dict_data
set project.signed_subject_code = sys_dict_data.dict_value
where project.signed_subject_value = sys_dict_data.dict_label
  and sys_dict_data.dict_type = 'signed_bid_subject';

update contract, sys_dict_data
set contract.signed_subject_code = sys_dict_data.dict_value
where contract.signed_subject_value = sys_dict_data.dict_label
  and sys_dict_data.dict_type = 'signed_bid_subject';

update supplementary_agreement, sys_dict_data
set supplementary_agreement.signed_subject_code = sys_dict_data.dict_value
where supplementary_agreement.signed_subject_value = sys_dict_data.dict_label
  and sys_dict_data.dict_type = 'signed_bid_subject';

update bureau_contract, sys_dict_data
set bureau_contract.signed_subject_code = sys_dict_data.dict_value
where bureau_contract.signed_subject_value = sys_dict_data.dict_label
  and sys_dict_data.dict_type = 'signed_bid_subject';


ALTER TABLE project
    ADD project_status_biz_time BIGINT UNSIGNED NULL COMMENT '项目业务时间(商务)';

ALTER TABLE business_system_data_bms
    ADD project_status_biz_time bigint(20) unsigned NULL COMMENT '项目业务时间(商务)';
